*:not(popover) {
  all: unset;
}

.test {
  background-image: linear-gradient(45deg, #f4d609 0%, #f4d609 10%, #212121 10%, #212121 20%, #f4d609 20%, #f4d609 30%, #212121 30%, #212121 40%, #f4d609 40%, #f4d609 50%, #212121 50%, #212121 60%, #f4d609 60%, #f4d609 70%, #212121 70%, #212121 80%, #f4d609 80%, #f4d609 90%, #212121 90%, #212121 100%);
  background-repeat: repeat;
}

.elevation {
  border-radius: 1.159rem;
  margin: 0.76rem;
}

.shadow-window {
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15), 0 14px 25px rgba(0, 0, 0, 0.2), 0 20px 28px rgba(0, 0, 0, 0.05);
}

.shadow-window-light {
  box-shadow: 0 1px 10px rgba(0, 0, 0, 0.15), 0 0 25px rgba(0, 0, 0, 0.2);
}

.test-size {
  min-height: 3rem;
  min-width: 3rem;
}

.txt-title {
  font-family: "Geist", "GeistMono", "Cascadia Code", "Segoe UI", "Roboto", sans-serif;
  font-size: 2.045rem;
}

.txt-title-small {
  font-family: "Geist", "GeistMono", "Cascadia Code", "Segoe UI", "Roboto", sans-serif;
  font-size: 1.364rem;
}

.techfont {
  font-family: "Geist Mono", "Geist Mono NF", "Cascadia Mono", "Fira Mono", monospace;
}

.txt-reading {
  font-family: "Geist", "GeistMono", "Cascadia Code", "Segoe UI", "Open Sans", sans-serif;
  font-weight: 500;
}

.no-anim {
  transition: 0ms;
}

.txt {
  color: #dfe3e7;
}

.txt-primary {
  color: #8ccff1;
}

.txt-onSecondaryContainer {
  color: #ffffff;
}

.txt-onSurfaceVariant {
  color: #c0c7cd;
}

.txt-onLayer1 {
  color: #c0c7cd;
}

.txt-shadow {
  text-shadow: 1px 2px 20px rgba(0, 0, 0, 0.6);
}

.txt-monospace {
  font-family: "Iosevka";
}

.txt-gigantic {
  font-family: "Geist", "GeistMono", "Cascadia Code", "Segoe UI", "Roboto", sans-serif;
  font-size: 3rem;
}

.txt-massive {
  font-family: "Geist", "GeistMono", "Cascadia Code", "Segoe UI", "Roboto", sans-serif;
  font-size: 2.7273rem;
}

.txt-hugerass {
  font-family: "Geist", "GeistMono", "Cascadia Code", "Segoe UI", "Roboto", sans-serif;
  font-size: 2.045rem;
}

.txt-hugeass {
  font-family: "Geist", "GeistMono", "Cascadia Code", "Segoe UI", "Roboto", sans-serif;
  font-size: 1.8182rem;
}

.txt-larger {
  font-family: "Geist", "GeistMono", "Cascadia Code", "Segoe UI", "Roboto", sans-serif;
  font-size: 1.6363rem;
}

.txt-large {
  font-family: "Geist", "GeistMono", "Cascadia Code", "Segoe UI", "Roboto", sans-serif;
  font-size: 1.4545rem;
}

.txt-norm {
  font-family: "Geist", "GeistMono", "Cascadia Code", "Segoe UI", "Roboto", sans-serif;
  font-size: 1.2727rem;
}

.txt-small {
  font-family: "Geist", "GeistMono", "Cascadia Code", "Segoe UI", "Roboto", sans-serif;
  font-size: 1.0909rem;
}

.txt-smallie {
  font-family: "Geist", "GeistMono", "Cascadia Code", "Segoe UI", "Roboto", sans-serif;
  font-size: 1rem;
}

.txt-smaller {
  font-family: "Geist", "GeistMono", "Cascadia Code", "Segoe UI", "Roboto", sans-serif;
  font-size: 0.9091rem;
}

.txt-tiny {
  font-family: "Geist", "GeistMono", "Cascadia Code", "Segoe UI", "Roboto", sans-serif;
  font-size: 0.7273rem;
}

.txt-poof {
  font-size: 0px;
}

.txt-subtext {
  color: #90979c;
}

.txt-action {
  color: rgb(191.8, 195.95, 199.8);
}

.txt-thin {
  font-weight: 300;
}

.txt-semibold {
  font-weight: 500;
}

.txt-bold {
  font-weight: bold;
}

.txt-italic {
  font-style: italic;
}

.btn-primary {
  border-radius: 9999px;
  -gtk-outline-radius: 9999px;
  background-color: #8ccff1;
  color: #002c3b;
  padding: 0.682rem 1.023rem;
}

.titlefont {
  font-family: "Geist", "GeistMono", "Cascadia Code", "Segoe UI", "Roboto", sans-serif;
}

.mainfont {
  font-family: "Geist", "GeistMono", "Cascadia Code", "Segoe UI", "Roboto", sans-serif;
}

.icon-material {
  font-family: "Material Icons", "Material Symbols Rounded", "MaterialSymbolsRounded", "Material Symbols Outlined", "Material Icons Outlined", "Material Symbols Sharp";
}

.icon-nerd {
  font-family: "Cascadia Code NF", "Cascadia Mono NF", "Fira Code", monospace;
}

.separator-line {
  background-color: rgb(79.5, 85.5, 89.5);
  min-width: 0.068rem;
  min-height: 0.068rem;
}

.separator-circle {
  border-radius: 9999px;
  -gtk-outline-radius: 9999px;
  background-color: #90979c;
  margin: 0rem 0.682rem;
  min-width: 0.273rem;
  min-height: 0.273rem;
}

.spacing-h-3 > * {
  margin-right: 0.205rem;
}

.spacing-h-3 > *:last-child {
  margin-right: 0rem;
}

.spacing-v-3 > * {
  margin-bottom: 0.205rem;
}

.spacing-v-3 > *:last-child {
  margin-bottom: 0rem;
}

.spacing-v-15 > * {
  margin-bottom: 1.023rem;
}

.spacing-v-15 > *:last-child {
  margin-bottom: 0rem;
}

.spacing-h-15 > * {
  margin-right: 1.023rem;
}

.spacing-h-15 > *:last-child {
  margin-right: 0rem;
}

.spacing-h-15 > revealer > * {
  margin-right: 1.023rem;
}

.spacing-h-15 > revealer:last-child > * {
  margin-right: 0rem;
}

.spacing-h-15 > scrolledwindow > * {
  margin-right: 1.023rem;
}

.spacing-h-15 > scrolledwindow:last-child > * {
  margin-right: 0rem;
}

.spacing-v-5 > box {
  margin-bottom: 0.341rem;
}

.spacing-v-5 > box:last-child {
  margin-bottom: 0rem;
}

.spacing-v-5 > * {
  margin-bottom: 0.341rem;
}

.spacing-v-5 > *:last-child {
  margin-bottom: 0rem;
}

.spacing-v-5-revealer > revealer > * {
  margin-bottom: 0.341rem;
}

.spacing-v-5-revealer > revealer:last-child > * {
  margin-bottom: 0rem;
}

.spacing-v-5-revealer > scrolledwindow > * {
  margin-bottom: 0.341rem;
}

.spacing-v-5-revealer > scrolledwindow:last-child > * {
  margin-bottom: 0rem;
}

.spacing-h-4 > * {
  margin-right: 0.273rem;
}

.spacing-h-4 > *:last-child {
  margin-right: 0rem;
}

.spacing-h-4 > overlay > *:first-child {
  margin-right: 0.273rem;
}

.spacing-h-4 > overlay:last-child > * {
  margin-right: 0rem;
}

.spacing-h-5 > * {
  margin-right: 0.341rem;
}

.spacing-h-5 > *:last-child {
  margin-right: 0rem;
}

.spacing-h-5 > widget > * {
  margin-right: 0.341rem;
}

.spacing-h-5 > widget:last-child > * {
  margin-right: 0rem;
}

.spacing-h-5 > revealer > * {
  margin-right: 0.341rem;
}

.spacing-h-5 > revealer:last-child > * {
  margin-right: 0rem;
}

.spacing-h-5 > scrolledwindow > * {
  margin-right: 0.341rem;
}

.spacing-h-5 > scrolledwindow:last-child > * {
  margin-right: 0rem;
}

.spacing-v-minus5 > * {
  margin-bottom: -0.341rem;
}

.spacing-v-minus5 > *:last-child {
  margin-bottom: 0rem;
}

.spacing-h-10 > * {
  margin-right: 0.682rem;
}

.spacing-h-10 > *:last-child {
  margin-right: 0rem;
}

.spacing-h-10 > revealer > * {
  margin-right: 0.682rem;
}

.spacing-h-10 > revealer:last-child > * {
  margin-right: 0rem;
}

.spacing-h-10 > scrolledwindow > * {
  margin-right: 0.682rem;
}

.spacing-h-10 > scrolledwindow:last-child > * {
  margin-right: 0rem;
}

.spacing-h-10 > flowboxchild > * {
  margin-right: 0.682rem;
}

.spacing-h-10 > flowboxchild:last-child > * {
  margin-right: 0rem;
}

.spacing-v-10 > * {
  margin-bottom: 0.682rem;
}

.spacing-v-10 > *:last-child {
  margin-bottom: 0rem;
}

.spacing-h-20 > * {
  margin-right: 1.364rem;
}

.spacing-h-20 > *:last-child {
  margin-right: 0rem;
}

.spacing-v-20 > * {
  margin-bottom: 1.364rem;
}

.spacing-v-20 > *:last-child {
  margin-bottom: 0rem;
}

.spacing-h-30 > * {
  margin-right: 1.364rem;
}

.spacing-h-30 > *:last-child {
  margin-right: 0rem;
}

.spacing-v-30 > * {
  margin-bottom: 1.364rem;
}

.spacing-v-30 > *:last-child {
  margin-bottom: 0rem;
}

.anim-enter {
  transition: 200ms cubic-bezier(0.05, 0.7, 0.1, 1);
}

.anim-exit {
  transition: 150ms cubic-bezier(0.3, 0, 0.8, 0.15);
}

.button-minsize {
  min-width: 2.727rem;
  min-height: 2.727rem;
}

.margin-top-5 {
  margin-top: 0.34rem;
}

.padding-top-5 {
  padding-top: 0.34rem;
}

.margin-bottom-5 {
  margin-bottom: 0.34rem;
}

.padding-bottom-5 {
  padding-bottom: 0.34rem;
}

.margin-left-5 {
  margin-left: 0.34rem;
}

.padding-left-5 {
  padding-left: 0.34rem;
}

.margin-right-5 {
  margin-right: 0.34rem;
}

.padding-right-5 {
  padding-right: 0.34rem;
}

.padding-5 {
  padding: 0.34rem;
}

.margin-5 {
  padding: 0.34rem;
}

.margin-top-8 {
  margin-top: 0.544rem;
}

.padding-top-8 {
  padding-top: 0.544rem;
}

.margin-bottom-8 {
  margin-bottom: 0.544rem;
}

.padding-bottom-8 {
  padding-bottom: 0.544rem;
}

.margin-left-8 {
  margin-left: 0.544rem;
}

.padding-left-8 {
  padding-left: 0.544rem;
}

.margin-right-8 {
  margin-right: 0.544rem;
}

.padding-right-8 {
  padding-right: 0.544rem;
}

.padding-8 {
  padding: 0.544rem;
}

.margin-8 {
  padding: 0.544rem;
}

.margin-top-10 {
  margin-top: 0.68rem;
}

.padding-top-10 {
  padding-top: 0.68rem;
}

.margin-bottom-10 {
  margin-bottom: 0.68rem;
}

.padding-bottom-10 {
  padding-bottom: 0.68rem;
}

.margin-left-10 {
  margin-left: 0.68rem;
}

.padding-left-10 {
  padding-left: 0.68rem;
}

.margin-right-10 {
  margin-right: 0.68rem;
}

.padding-right-10 {
  padding-right: 0.68rem;
}

.padding-10 {
  padding: 0.68rem;
}

.margin-10 {
  padding: 0.68rem;
}

.margin-top-15 {
  margin-top: 1.02rem;
}

.padding-top-15 {
  padding-top: 1.02rem;
}

.margin-bottom-15 {
  margin-bottom: 1.02rem;
}

.padding-bottom-15 {
  padding-bottom: 1.02rem;
}

.margin-left-15 {
  margin-left: 1.02rem;
}

.padding-left-15 {
  padding-left: 1.02rem;
}

.margin-right-15 {
  margin-right: 1.02rem;
}

.padding-right-15 {
  padding-right: 1.02rem;
}

.padding-15 {
  padding: 1.02rem;
}

.margin-15 {
  padding: 1.02rem;
}

.margin-top-20 {
  margin-top: 1.36rem;
}

.padding-top-20 {
  padding-top: 1.36rem;
}

.margin-bottom-20 {
  margin-bottom: 1.36rem;
}

.padding-bottom-20 {
  padding-bottom: 1.36rem;
}

.margin-left-20 {
  margin-left: 1.36rem;
}

.padding-left-20 {
  padding-left: 1.36rem;
}

.margin-right-20 {
  margin-right: 1.36rem;
}

.padding-right-20 {
  padding-right: 1.36rem;
}

.padding-20 {
  padding: 1.36rem;
}

.margin-20 {
  padding: 1.36rem;
}

.width-10 {
  min-width: 0.682rem;
}

.height-10 {
  min-width: 0.682rem;
}

.invisible {
  opacity: 0;
  background-color: transparent;
  color: transparent;
}

.spacing-h--5 > box {
  margin-right: -0.341rem;
}

.spacing-h--5 > box:last-child {
  margin-right: 0rem;
}

.spacing-v--5 > * {
  margin-bottom: -0.341rem;
}

.spacing-v--5 > *:last-child {
  margin-bottom: 0rem;
}

.spacing-h--10 > * {
  margin-left: -1.364rem;
}

.spacing-h--10 > *:first-child {
  margin-left: 0rem;
}

.spacing-v--10 > * {
  margin-bottom: -0.682rem;
}

.spacing-v--10 > *:last-child {
  margin-bottom: 0rem;
}

.spacing-v--10 > * {
  margin-bottom: -0.682rem;
}

.spacing-v--10 > *:last-child {
  margin-bottom: 0rem;
}

.spacing-h--20 > * {
  margin-left: -1.364rem;
}

.spacing-h--20 > *:first-child {
  margin-left: 0rem;
}

.instant {
  transition: 0ms;
}

.menu-decel {
  transition: 400ms cubic-bezier(0.1, 1, 0, 1);
}

.element-show {
  transition: 300ms cubic-bezier(0.85, 0, 0.15, 1);
}

.element-hide {
  transition: 300ms cubic-bezier(0.85, 0, 0.15, 1);
}

.element-move {
  transition: 300ms cubic-bezier(0.85, 0, 0.15, 1);
}

.element-decel {
  transition: 300ms cubic-bezier(0, 0.55, 0.45, 1);
}

.element-bounceout {
  transition: transform 200ms cubic-bezier(0.34, 1.56, 0.64, 1);
}

.element-accel {
  transition: 300ms cubic-bezier(0.55, 0, 1, 0.45);
}

.sec-txt {
  color: #b4cad6;
}

.padding01 {
  padding: 0rem 1rem;
}

.txt-percent {
  font-family: Helvetica;
  font-weight: 900;
}

.onSurfaceVariant {
  color: #c0c7cd;
}

.quran-arabic-text {
  font-family: "TE HAFS2 Tharwat Emara", "Noto Naskh Arabic", "Noto Sans Arabic", serif;
  color: #c0c7cd;
}

* {
  caret-color: #e3e7eb;
}
* selection {
  background-color: #b4cad6;
  color: #162a34;
}

@keyframes appear {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
menu {
  border-radius: 0.818rem;
  -gtk-outline-radius: 0.818rem;
  border: 1px solid rgb(69.4, 74.2, 76.6);
  padding: 0.681rem;
  background: #1f2426;
  color: #e3e7eb;
  -gtk-outline-radius: 1.159rem;
  animation-name: appear;
  animation-duration: 40ms;
  animation-timing-function: ease-out;
  animation-iteration-count: 1;
}

menubar > menuitem {
  border-radius: 0.545rem;
  -gtk-outline-radius: 0.545rem;
  min-width: 13.636rem;
  min-height: 2.727rem;
}

menu > menuitem {
  padding: 0.4em 1.5rem;
  background: transparent;
  transition: 0.2s ease background-color;
  border-radius: 0.545rem;
  -gtk-outline-radius: 0.545rem;
}

menu > menuitem:hover,
menu > menuitem:focus {
  background-color: rgb(54.65, 59.55, 62.155);
}

menu > menuitem:active {
  background-color: rgb(73.8, 78.6, 81.36);
}

radio {
  border-radius: 9999px;
  -gtk-outline-radius: 9999px;
  margin: 0.273rem;
  min-width: 15px;
  min-height: 15px;
  border: 0.068rem solid #90979c;
}

radio:checked {
  min-width: 8px;
  min-height: 8px;
  background-color: #002c3b;
  border: 0.477rem solid #8ccff1;
}

tooltip {
  animation-name: appear;
  animation-duration: 100ms;
  animation-timing-function: ease-out;
  animation-iteration-count: 1;
  border-radius: 1.159rem;
  -gtk-outline-radius: 1.159rem;
  background-color: #dfe3e7;
  color: #2c3134;
}

popover {
  border-top: 1px solid rgb(44.68, 49.54, 52.68);
  border-left: 1px solid rgb(44.68, 49.54, 52.68);
  border-right: 1px solid rgb(36.2, 41.1, 44.2);
  border-bottom: 1px solid rgb(36.2, 41.1, 44.2);
  padding: 0.681rem;
  background: #292e31;
  color: #e3e7eb;
  border-radius: 1.159rem;
  -gtk-outline-radius: 1.159rem;
  animation-name: appear;
  animation-duration: 40ms;
  animation-timing-function: ease-out;
  animation-iteration-count: 1;
}

.configtoggle-box {
  padding: 0.205rem 0.341rem;
}

.switch-bg {
  transition: 300ms cubic-bezier(0, 0.55, 0.45, 1);
  border-radius: 9999px;
  -gtk-outline-radius: 9999px;
  border: 0.136rem solid #e3e7eb;
  min-width: 2.864rem;
  min-height: 1.637rem;
}

.switch-bg-true {
  background-color: #8ccff1;
  border: 0.136rem solid #8ccff1;
}

.switch-fg {
  border-radius: 9999px;
  -gtk-outline-radius: 9999px;
  transition: 400ms cubic-bezier(0.1, 1, 0, 1);
  background-color: #e3e7eb;
  color: #181d20;
  min-width: 0.819rem;
  min-height: 0.819rem;
  margin-left: 0.477rem;
}

.switch-fg-true {
  background-color: #002c3b;
  color: #8ccff1;
  min-width: 1.431rem;
  min-height: 1.431rem;
  margin-left: 1.431rem;
}

.switch-fg-toggling-false {
  transition: 400ms cubic-bezier(0.1, 1, 0, 1);
  min-width: 1.636rem;
  min-height: 0.819rem;
}

.segment-container {
  border-radius: 9999px;
  -gtk-outline-radius: 9999px;
  border: 0.068rem solid #90979c;
}

.segment-container > *:first-child {
  border-top-left-radius: 9999px;
  border-bottom-left-radius: 9999px;
}

.segment-container > * {
  border-right: 0.068rem solid #90979c;
  padding: 0.341rem 0.682rem;
}

.segment-container > *:last-child {
  border-right: 0rem solid transparent;
  border-top-right-radius: 9999px;
  border-bottom-right-radius: 9999px;
}

.segment-btn {
  color: #e3e7eb;
}

.segment-btn:focus,
.segment-btn:hover {
  background-color: rgb(46.2, 51.05, 54.2);
}

.segment-btn-enabled {
  background-color: #536772;
  color: #ffffff;
}

.segment-btn-enabled:hover,
.segment-btn-enabled:focus {
  background-color: #536772;
  color: #ffffff;
}

.multipleselection-btn {
  border-radius: 0.818rem;
  -gtk-outline-radius: 0.818rem;
  padding: 0rem 0.341rem;
  border: 0.034rem solid #90979c;
  color: #e3e7eb;
}

.multipleselection-btn:focus,
.multipleselection-btn:hover {
  background-color: rgb(46.2, 51.05, 54.2);
  color: #e3e7eb;
}

.multipleselection-btn-enabled {
  background-color: #536772;
  color: #ffffff;
}

.multipleselection-btn-enabled:hover,
.multipleselection-btn-enabled:focus {
  background-color: #536772;
  color: #ffffff;
}

.gap-v-5 {
  min-height: 0.341rem;
}

.gap-h-5 {
  min-width: 0.341rem;
}

.gap-v-10 {
  min-height: 0.682rem;
}

.gap-h-10 {
  min-width: 0.682rem;
}

.gap-v-15 {
  min-height: 1.023rem;
}

.gap-h-15 {
  min-width: 1.023rem;
}

.tab-btn {
  border-radius: 0.818rem;
  -gtk-outline-radius: 0.818rem;
  transition: 300ms cubic-bezier(0, 0.55, 0.45, 1);
  min-height: 2.5rem;
  color: #dfe3e7;
}

.tab-btn:hover {
  background-color: rgb(46.2, 51.05, 54.2);
}

.tab-btn:focus {
  background-color: #181d20;
}

.tab-btn-active > box > label {
  color: #8ccff1;
}

.tab-indicator {
  transition: 180ms ease-in-out;
  min-height: 0.205rem;
  padding: 0rem 1.023rem;
  color: #8ccff1;
}

.tab-icon {
  transition: 300ms cubic-bezier(0, 0.55, 0.45, 1);
  border-radius: 9999px;
  -gtk-outline-radius: 9999px;
  min-width: 2.25rem;
  min-height: 2.25rem;
  font-size: 1.406rem;
  color: #e3e7eb;
}

.tab-icon-active {
  background-color: #536772;
  color: #ffffff;
}

.tab-icon-expandable {
  transition: 0ms;
  border-radius: 9999px;
  -gtk-outline-radius: 9999px;
  min-width: 2.25rem;
  min-height: 2.25rem;
  font-size: 1.406rem;
  color: #e3e7eb;
  padding: 0rem;
}

.tab-icon-expandable-active {
  background-color: #536772;
  color: #ffffff;
  padding: 0rem 0.545rem;
  min-width: 9.545rem;
}

widget {
  border-radius: 0.818rem;
  -gtk-outline-radius: 0.818rem;
}

.spinbutton {
  border-radius: 0.818rem;
  -gtk-outline-radius: 0.818rem;
  background-color: rgb(35.5, 40.5, 42.95);
  padding: 0.341rem;
}
.spinbutton entry {
  color: #e3e7eb;
  margin: 0.136rem 0.273rem;
}
.spinbutton button {
  margin-left: 0.205rem;
  padding: 0.136rem;
}

.bar-module-box {
  background-color: #0f1417;
  color: #dfe3e7;
  border-radius: 9999px;
  -gtk-outline-radius: 9999px;
  padding: 0.5rem 1.5rem;
}

.bar-icon {
  font-family: "Material Icons", "Material Symbols Rounded", "MaterialSymbolsRounded", "Material Symbols Outlined", "Material Icons Outlined", "Material Symbols Sharp";
  border-radius: 9999px;
  -gtk-outline-radius: 9999px;
  background-color: #0f1417;
  color: #dfe3e7;
  font-size: 2.032rem;
}

.bar-util-btn2 {
  transition: 300ms cubic-bezier(0, 0.55, 0.45, 1);
  background-color: #0f1417;
  min-width: 1rem;
  border-radius: 9999px;
  -gtk-outline-radius: 9999px;
  border: 0.6px solid rgba(180, 202, 214, 0);
}

.bar-notch {
  background-color: #0f1417;
  min-width: 16.1rem;
  border-radius: 0 0 1.364rem 1.364rem;
  /* Removed bottom border */
}

.bar-resources progressbar {
  transition: 300ms cubic-bezier(0, 0.55, 0.45, 1);
}
.bar-resources progressbar trough {
  border-radius: 9999px;
  -gtk-outline-radius: 9999px;
  min-width: 6rem;
  background-color: #40484c;
}
.bar-resources progressbar progress {
  padding: 0 0;
  background-color: #8ccff1;
  border-radius: 9999px;
  -gtk-outline-radius: 9999px;
}
.bar-resources:first-child progressbar progress {
  background-color: #8ccff1;
}
.bar-resources:nth-child(2) progressbar progress {
  background-color: rgb(157.4, 211.8, 239.8);
}
.bar-resources:last-child progressbar progress {
  background-color: rgb(174.8, 216.6, 238.6);
}

.bar-knocks {
  transition: 100ms cubic-bezier(0.38, 0.04, 1, 0.07);
  border-radius: 9999px;
  -gtk-outline-radius: 9999px;
  margin: 0.6rem 0;
  border: 0.4px solid rgba(180, 202, 214, 0);
  background-color: #0f1417;
  padding: 0rem 1rem;
  min-height: 0.5rem;
  box-shadow: 0 4px 2px rgba(0, 0, 0, 0.15), 0 3px 4px rgba(0, 0, 0, 0.2);
}
.bar-knocks.song-changing {
  animation: multiSweep 2.2s linear;
  animation-iteration-count: 13;
}

.bar-floating {
  background-color: #0f1417;
  border: 0.3px solid rgba(180, 202, 214, 0);
  margin: 0.476rem;
  transition: 200ms cubic-bezier(0.05, 0.7, 0.1, 1);
  border-radius: 1.705rem;
  -gtk-outline-radius: 1.705rem;
  box-shadow: 0 5px 3px rgba(0, 0, 0, 0.15), 0 8px 6px rgba(0, 0, 0, 0.2);
}

.bar-floating-outline {
  border: 0.5px solid rgba(192, 199, 205, 0.5);
}

.bar-height {
  min-height: 2.827rem;
}

.prim-txt {
  color: #b4cad6;
}

.bar-bg {
  background-color: #0f1417;
  min-height: 2.827rem;
}

.bar-vertical-pinned {
  background-color: #0f1417;
  margin-right: 0.3rem;
}

.bar-pads {
  background-color: #0f1417;
  border-radius: 1.705rem;
  -gtk-outline-radius: 1.705rem;
}

.bar-bg-focus {
  background-color: #0f1417;
  min-height: 1.364rem;
}

.bar-bg-nothing {
  background-color: #0f1417;
  min-height: 2px;
}

.bar-bg-focus-batterylow {
  background-color: rgb(51, 22.8, 24.8);
}

.bar-sidespace {
  min-width: 1.5rem;
}

.bar-group-margin {
  padding: 0.273rem 0rem;
}

.bar-group {
  background-color: #181d20;
}

.bar-saadi {
  background-color: #0f1417;
}

.group-saadi {
  border-radius: 9999px;
  -gtk-outline-radius: 9999px;
  padding: 0 1rem;
  margin: 0.6rem 0.4rem;
  background-color: #181d20;
}

.bar-group2 {
  font-family: "Geist", "GeistMono", "Cascadia Code", "Segoe UI", "Roboto", sans-serif;
  color: #ffffff;
  border-radius: 9999px;
  -gtk-outline-radius: 9999px;
  padding: 0 1.3rem;
  background-color: #536772;
}

.bar-gradiant-tert {
  border-radius: 9999px;
  -gtk-outline-radius: 9999px;
  padding: 0 1rem;
  margin: 0.6rem 0.4rem;
  background: linear-gradient(90deg, #34393c, #e4dfff);
}

.bar-gradiant-prim {
  border-radius: 9999px;
  -gtk-outline-radius: 9999px;
  padding: 0 1rem;
  margin: 0.6rem 0.4rem;
  background: linear-gradient(90deg, #34393c, #bfe8ff);
}

.bar-gradiant-sec {
  border-radius: 9999px;
  -gtk-outline-radius: 9999px;
  padding: 0 1rem;
  margin: 0.6rem 0.4rem;
  background: linear-gradient(90deg, #34393c, #e4dfff);
}

.group-saadi-short {
  padding: 0 0.5rem;
  border-radius: 9999px;
  -gtk-outline-radius: 9999px;
  margin: 0.6rem 0.4rem;
  background-color: #181d20;
}

.bar-group-pad {
  padding: 0.205rem;
}

.bar-group-pad-less {
  padding: 0rem 0.681rem;
}

.bar-group-pad-system {
  padding: 0rem 0.341rem;
}

.bar-group-pad-vertical {
  border-radius: 0.818rem;
  -gtk-outline-radius: 0.818rem;
  -gtk-outline-radius: 1.364rem;
  padding: 1rem 0rem;
  margin: 0rem 0.5rem;
}

.bar-group-pad-music {
  padding-right: 1.023rem;
  padding-left: 0.341rem;
}

.bar-group-standalone {
  border-radius: 0.818rem;
  -gtk-outline-radius: 0.818rem;
  -gtk-outline-radius: 1.364rem;
}

.bar-group-round {
  border-radius: 10rem;
  -gtk-outline-radius: 10rem;
}

.bar-group-middle {
  border-radius: 0.477rem;
  -gtk-outline-radius: 0.477rem;
}

.bar-group-left {
  border-radius: 0.477rem;
  -gtk-outline-radius: 0.477rem;
  border-top-left-radius: 1.364rem;
  border-bottom-left-radius: 1.364rem;
}

.bar-group-right {
  border-radius: 0.477rem;
  -gtk-outline-radius: 0.477rem;
  border-top-right-radius: 1.364rem;
  border-bottom-right-radius: 1.364rem;
}

.bar-sidemodule {
  min-width: 26rem;
}

.bar-ws-width {
  min-width: 18.341rem;
}

.bar-ws-container {
  transition: 700ms cubic-bezier(0.1, 1, 0, 1);
}

.active-window-tb {
  background-color: #8ccff1;
  border-radius: 1.705rem;
  -gtk-outline-radius: 1.705rem;
}

.bar-ws {
  font-size: 1.02rem;
  font-weight: 600;
  min-width: 1.774rem;
  color: rgb(115.35, 119.9, 123.35);
}

.bar-ws-active {
  background-color: #8ccff1;
  color: #002c3b;
}

.bar-ws-occupied {
  background-color: rgb(35.5, 40.5, 42.95);
  color: #ffffff;
}

.bar-ws-focus {
  background-color: #40484c;
  min-width: 1rem;
}

.bar-ws-focus-active {
  min-width: 4.045rem;
  background-color: #bfe8ff;
}

.bar-ws-focus-occupied {
  background-color: #536772;
}

.bar-clock-box {
  margin: 0rem 0.682rem;
}

.bar-time {
  font-family: "Geist", "GeistMono", "Cascadia Code", "Segoe UI", "Roboto", sans-serif;
  font-size: 1.2727rem;
  color: #c0c7cd;
}

.power-draw-text {
  font-family: "Geist", "GeistMono", "Cascadia Code", "Segoe UI", "Roboto", sans-serif;
  font-size: 1.2rem;
  color: #c0c7cd;
}

.bar-date {
  color: #c0c7cd;
}

.bar-bat {
  padding: 0 3px;
  border-radius: 9999px;
  -gtk-outline-radius: 9999px;
  transition: 300ms cubic-bezier(0, 0.55, 0.45, 1);
  min-height: 2.3rem;
  min-width: 2.3rem;
  border-radius: 10rem;
  color: #ffffff;
}

.bar-bat-circprog {
  border: 0.12rem solid #90979c;
  transition: 1000ms cubic-bezier(0.1, 1, 0, 1);
  min-width: 0.23rem;
  min-height: 2.1rem;
  padding: 0rem;
  background-color: #536772;
  color: #ffffff;
}

.bar-music-art {
  margin: 4px;
  border: 1px solid #34393c;
  background-color: #34393c;
  border-radius: 8px;
  padding: 4px;
  -gtk-icon-style: regular;
  -gtk-icon-shadow: none;
}

.bar-batt {
  border-radius: 9999px;
  -gtk-outline-radius: 9999px;
  transition: 300ms cubic-bezier(0, 0.55, 0.45, 1);
  min-height: 1.77rem;
  min-width: 1.77rem;
  border-radius: 10rem;
  color: #ffffff;
}

.bar-batt-txt {
  color: #c0c7cd;
}

.bar-batt-circprog {
  transition: 1000ms cubic-bezier(0.1, 1, 0, 1);
  min-width: 0.136rem;
  min-height: 1.636rem;
  padding: 0rem;
  background-color: #536772;
  color: #ffffff;
}

.bar-batt-circprog-low {
  background-color: #ffb4ab;
  color: #c32220;
}

.bar-batt-low {
  background-color: #ffb4ab;
  color: #c32220;
}

.bar-ram-icon {
  border-radius: 9999px;
  -gtk-outline-radius: 9999px;
  transition: 300ms cubic-bezier(0, 0.55, 0.45, 1);
  min-height: 1.77rem;
  min-width: 1.77rem;
  border-radius: 10rem;
  color: #ffffff;
}

.bar-ram-circprog {
  transition: 1000ms cubic-bezier(0.1, 1, 0, 1);
  min-width: 0.136rem;
  min-height: 1.636rem;
  padding: 0rem;
  background-color: #536772;
  color: #ffffff;
}

.bar-ram-txt {
  color: #c0c7cd;
}

.bar-swap-icon {
  border-radius: 9999px;
  -gtk-outline-radius: 9999px;
  transition: 300ms cubic-bezier(0, 0.55, 0.45, 1);
  min-height: 1.77rem;
  min-width: 1.77rem;
  border-radius: 10rem;
  color: #ffffff;
}

.bar-swap-circprog {
  transition: 1000ms cubic-bezier(0.1, 1, 0, 1);
  min-width: 0.136rem;
  min-height: 1.636rem;
  padding: 0rem;
  background-color: #536772;
  color: #ffffff;
}

.bar-swap-txt {
  color: #c0c7cd;
}

.bar-cpu-icon {
  border-radius: 9999px;
  -gtk-outline-radius: 9999px;
  transition: 300ms cubic-bezier(0, 0.55, 0.45, 1);
  min-height: 1.77rem;
  min-width: 1.77rem;
  border-radius: 10rem;
  color: #ffffff;
}

.bar-cpu-circprog {
  transition: 1000ms cubic-bezier(0.1, 1, 0, 1);
  min-width: 0.136rem;
  min-height: 1.636rem;
  padding: 0rem;
  background-color: #536772;
  color: #ffffff;
}

.bar-cpu-txt {
  color: #c0c7cd;
}

.bar-music-playstate {
  transition: 300ms cubic-bezier(0, 0.55, 0.45, 1);
  min-height: 1.77rem;
  min-width: 1.77rem;
  border-radius: 10rem;
  color: #ffffff;
}

.bar-music-circprog {
  transition: 1000ms cubic-bezier(0.1, 1, 0, 1);
  min-width: 0.136rem;
  min-height: 1.636rem;
  padding: 0rem;
  background-color: #536772;
  color: #ffffff;
}

.bar-music-playstate-playing {
  min-height: 1.77rem;
  min-width: 1.77rem;
  border-radius: 10rem;
  color: #ffffff;
}

.bar-music-playstate-txt {
  transition: 100ms cubic-bezier(0.05, 0.7, 0.1, 1);
  font-family: "Material Icons", "Material Symbols Rounded", "MaterialSymbolsRounded", "Material Symbols Outlined", "Material Icons Outlined", "Material Symbols Sharp";
}

.bar-music-txt {
  color: #c0c7cd;
}

.bar-music-cover {
  background-position: center;
  background-repeat: no-repeat;
  background-size: 100% auto;
  min-width: 11.932rem;
}

.bar-music-extended-bg {
  border-radius: 1.364rem;
  min-width: 34.091rem;
}

.bar-music-hide-false {
  transition: 400ms cubic-bezier(0.1, 1, 0, 1);
  transition-duration: 100ms;
  opacity: 1;
}

.bar-music-hide-true {
  transition: 200ms cubic-bezier(0.38, 0.04, 1, 0.07);
  transition-duration: 100ms;
  opacity: 0;
}

.bar-corner-spacing {
  min-width: 0.882rem;
  min-height: 1.705rem;
}

.corner {
  background-color: #0f1417;
  border-radius: 1.705rem;
}

.corner-black {
  background-color: black;
  padding: 100px;
}

.bar-wintitle-topdesc {
  margin-top: -0.136rem;
  margin-bottom: -0.341rem;
  color: rgb(160.6, 164.9, 168.6);
}

.bar-wintitle-txt {
  color: #dfe3e7;
}

.bar-space-button {
  padding: 0.341rem;
}

.bar-space-button > box:first-child {
  border-radius: 9999px;
  -gtk-outline-radius: 9999px;
  padding: 0rem 0.682rem;
}

.bar-space-button-leftmost box {
  margin: 0rem 0.682rem;
}

.bar-space-area-rightmost > box {
  padding-right: 1.364rem;
}

.bar-systray {
  border-radius: 9999px;
  -gtk-outline-radius: 9999px;
  margin: 0.137rem 0rem;
  padding: 0rem 0.682rem;
}

.bar-systray-item {
  border-radius: 9999px;
  -gtk-outline-radius: 9999px;
  transition: 300ms cubic-bezier(0, 0.55, 0.45, 1);
  -gtk-icon-theme: "Adwaita";
  min-height: 1.032rem;
  min-width: 1.032rem;
  font-size: 1.032rem;
  color: #dfe3e7;
}

.bar-statusicons {
  border-radius: 9999px;
  -gtk-outline-radius: 9999px;
  transition: 300ms cubic-bezier(0, 0.55, 0.45, 1);
  margin: 0.273rem;
  padding: 0rem 0.614rem;
}

.bar-statusicons-active {
  background-color: #292e31;
  color: #e3e7eb;
}

.bar-util-btn {
  border-radius: 9999px;
  -gtk-outline-radius: 9999px;
  transition: 300ms cubic-bezier(0, 0.55, 0.45, 1);
  min-height: 1.77rem;
  min-width: 1.77rem;
  background-color: rgb(35.5, 40.5, 42.95);
  color: #e3e7eb;
}

.bar-util-btn2 {
  border-radius: 9999px;
  -gtk-outline-radius: 9999px;
  transition: 300ms cubic-bezier(0, 0.55, 0.45, 1);
  min-height: 1.77rem;
  min-width: 1.77rem;
  background-color: #0f1417;
  color: #e3e7eb;
}

.bar-util-btn:hover,
.bar-util-btn:focus {
  background-color: rgb(54.65, 59.55, 62.155);
}

.bar-util-btn:active {
  background-color: rgb(73.8, 78.6, 81.36);
}

.bar-spaceright {
  color: #dfe3e7;
}

.bar-bluetooth-device {
  border-radius: 9999px;
  -gtk-outline-radius: 9999px;
  -gtk-icon-theme: "Adwaita";
  min-height: 1.032rem;
  min-width: 1.032rem;
  font-size: 1.032rem;
  padding: 0.205rem 0.341rem;
}

.bar-time-module {
  margin-left: 0.5rem;
  margin-right: 1rem;
  color: #c0c7cd;
}

.status-icons-group {
  padding-left: 0.273rem;
  padding-right: 1rem;
}

.time-with-margin {
  margin-left: 0.5rem;
}

.bar-wintitle-icon-spacer {
  min-width: 0.714rem;
}

.battery-scale-container {
  transition: 300ms cubic-bezier(0, 0.55, 0.45, 1);
  border-radius: 0.818rem;
  -gtk-outline-radius: 0.818rem;
  padding: 0 0 0 12px;
}

.battery-scale-box {
  min-width: 100px;
}

.battery-icon-box.bar-batt-charging {
  color: #8ccff1;
}
.battery-icon-box.bar-batt-low {
  color: #ffb4ab;
}

.battery-scale-bar {
  background-color: #8ccff1;
  min-width: 50px;
  border-radius: 99px;
}
.battery-scale-bar trough progress {
  background: linear-gradient(90deg, #292e31 1%, #536772);
  animation: chargeAnimation 10s linear infinite;
  border-radius: 99px;
  min-height: 15.5px;
}
.battery-scale-bar.bar-batt-charging trough progress {
  background: linear-gradient(90deg, #292e31, #8ccff1);
  border-radius: 99px;
  min-height: 15.5px;
  animation: chargeAnimation 2s linear infinite;
}
.battery-scale-bar.bar-batt-low trough progress {
  background-color: #ffb4ab;
  border-radius: 99px;
}

@keyframes chargeAnimation {
  0% {
    background-position: 0% 50%;
  }
  100% {
    background-position: 100% 50%;
  }
}
.avatar-widget {
  border-radius: 1.159rem;
  -gtk-outline-radius: 1.159rem;
  padding: 3px 6px;
  margin: 3px;
  background-color: rgb(35.5, 40.5, 42.95);
}

.avatar-box {
  transition: 300ms cubic-bezier(0, 0.55, 0.45, 1);
  box-shadow: inset 0 0 0 1px #181d20;
}

.avatar-eventbox:hover .avatar-widget {
  background-color: #292e31;
}

.avatar-eventbox:active .avatar-widget {
  background-color: #34393c;
}

.battery-module {
  background-color: #0f1417;
  border-radius: 1.705rem;
  -gtk-outline-radius: 1.705rem;
}

.cheatsheet-bg {
  border-radius: 1.705rem;
  -gtk-outline-radius: 1.705rem;
  border-top: 1px solid #1f2426;
  border-left: 1px solid #1f2426;
  border-right: 1px solid #1f2426;
  border-bottom: 1px solid #1f2426;
  border: 0.3px solid rgba(180, 202, 214, 0);
  margin: 0.476rem;
  background-color: #0f1417;
  padding: 0.8rem;
  margin: 0.5rem;
}

.cheatsheet-title {
  color: #ffffff;
}

.cheatsheet-bind-lineheight {
  min-height: 1.8rem;
}

.cheatsheet-key {
  font-family: "Geist Mono", "Geist Mono NF", "Cascadia Mono", "Fira Mono", monospace;
  min-height: 1.2rem;
  min-width: 1.2rem;
  margin: 0.1rem;
  padding: 0.1rem 0.15rem;
  -gtk-outline-radius: 0.3rem;
  color: #002c3b;
  background-color: #8ccff1;
  border-radius: 0.3rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.cheatsheet-key-notkey {
  min-height: 1.2rem;
  padding: 0.1rem 0.15rem;
  margin: 0.1rem;
  color: #dfe3e7;
}

.cheatsheet-color-1 {
  color: #002c3b;
  background-color: #ffffff;
}

.cheatsheet-color-2 {
  color: #002c3b;
  background-color: #ffffff;
}

.cheatsheet-color-3 {
  color: #002c3b;
  background-color: #ffffff;
}

.cheatsheet-color-4 {
  color: #002c3b;
  background-color: #ffffff;
}

.cheatsheet-color-5 {
  color: #002c3b;
  background-color: #ffffff;
}

.cheatsheet-color-6 {
  color: #002c3b;
  background-color: #ffffff;
}

.cheatsheet-color-7 {
  color: #002c3b;
  background-color: #ffffff;
}

.cheatsheet-color-8 {
  color: #002c3b;
  background-color: #ffffff;
}

.cheatsheet-closebtn {
  transition: 300ms cubic-bezier(0, 0.55, 0.45, 1);
  border-radius: 9999px;
  -gtk-outline-radius: 9999px;
  min-width: 2.386rem;
  min-height: 2.386rem;
}

.cheatsheet-closebtn:hover,
.cheatsheet-closebtn:focus {
  background-color: rgb(46.2, 51.05, 54.2);
}

.cheatsheet-closebtn:active {
  background-color: #292e31;
}

.cheatsheet-category-title {
  font-family: "Geist", "GeistMono", "Cascadia Code", "Segoe UI", "Roboto", sans-serif;
  font-size: 1.5rem;
  color: #8ccff1;
  margin-bottom: 0.5rem;
  padding-bottom: 0.2rem;
  border-bottom: 1px solid #8ccff1;
}

.cheatsheet-section {
  background-color: #181d20;
  border-radius: 0.8rem;
  padding: 0.8rem;
  margin-bottom: 0.5rem;
  border-top: 1px solid #1f2426;
  border-left: 1px solid #1f2426;
  border-right: 1px solid #1f2426;
  border-bottom: 1px solid #1f2426;
}

.cheatsheet-keybind-row {
  min-height: 1.8rem;
  padding: 0.2rem 0.5rem;
  border-radius: 0.5rem;
}
.cheatsheet-keybind-row:hover {
  background-color: rgb(35.5, 40.5, 42.95);
}

.cheatsheet-search-container {
  margin-bottom: 1rem;
}

.cheatsheet-search-entry {
  border-radius: 0.818rem;
  -gtk-outline-radius: 0.818rem;
  background-color: rgb(35.5, 40.5, 42.95);
  color: #c0c7cd;
  padding: 0.5rem 0.8rem;
  caret-color: #c0c7cd;
  border: 1px solid rgba(180, 202, 214, 0);
  min-width: 25rem;
}
.cheatsheet-search-entry selection {
  background-color: #b4cad6;
  color: #162a34;
}

.cheatsheet-search-entry:focus {
  background-color: rgb(54.65, 59.55, 62.155);
  border: 1px solid #8ccff1;
}

.cheatsheet-no-results {
  padding: 2rem;
}
.cheatsheet-no-results .txt-title {
  color: #8ccff1;
  font-size: 1.3rem;
  margin-bottom: 0.5rem;
}

.cheatsheet-periodictable-elementsymbol {
  font-family: "Geist", "GeistMono", "Cascadia Code", "Segoe UI", "Open Sans", sans-serif;
  font-weight: 500;
  font-size: 1.705rem;
  font-weight: bold;
}

.cheatsheet-periodictable-elementnum {
  border-radius: 9999px;
  -gtk-outline-radius: 9999px;
  min-width: 1.364rem;
  min-height: 1.364rem;
  background-color: #8ccff1;
  color: #dfe3e7;
}

.cheatsheet-periodictable-empty {
  border-radius: 0.818rem;
  -gtk-outline-radius: 0.818rem;
  min-width: 5.455rem;
  min-height: 5.455rem;
}

.cheatsheet-periodictable-metal {
  min-width: 5.455rem;
  min-height: 5.455rem;
  border-radius: 0.818rem;
  -gtk-outline-radius: 0.818rem;
  background-color: #181d20;
  color: #c0c7cd;
  background-color: #1f6c89;
  color: #ffffff;
}

.cheatsheet-periodictable-nonmetal {
  min-width: 5.455rem;
  min-height: 5.455rem;
  border-radius: 0.818rem;
  -gtk-outline-radius: 0.818rem;
  background-color: #181d20;
  color: #c0c7cd;
  background-color: #646083;
  color: #ffffff;
}

.cheatsheet-periodictable-noblegas {
  min-width: 5.455rem;
  min-height: 5.455rem;
  border-radius: 0.818rem;
  -gtk-outline-radius: 0.818rem;
  background-color: #181d20;
  color: #c0c7cd;
  background-color: #536772;
  color: #ffffff;
}

.cheatsheet-periodictable-lanthanum {
  min-width: 5.455rem;
  min-height: 5.455rem;
  border-radius: 0.818rem;
  -gtk-outline-radius: 0.818rem;
  background-color: #181d20;
  color: #c0c7cd;
  background-color: #34393c;
  color: #ffffff;
}

.cheatsheet-periodictable-actinium {
  min-width: 5.455rem;
  min-height: 5.455rem;
  border-radius: 0.818rem;
  -gtk-outline-radius: 0.818rem;
  background-color: #181d20;
  color: #c0c7cd;
  background-color: #292e31;
  color: #ffffff;
}

.cheatsheet-periodictable-legend-color-wrapper {
  border-radius: 9999px;
  -gtk-outline-radius: 9999px;
  padding: 0.273rem;
  border: 0.136rem solid #dfe3e7;
}

.cheatsheet-periodictable-legend-color-metal {
  border-radius: 9999px;
  -gtk-outline-radius: 9999px;
  min-width: 1.023rem;
  min-height: 1.023rem;
  background-color: #1f6c89;
}

.cheatsheet-periodictable-legend-color-nonmetal {
  border-radius: 9999px;
  -gtk-outline-radius: 9999px;
  min-width: 1.023rem;
  min-height: 1.023rem;
  background-color: #646083;
}

.cheatsheet-periodictable-legend-color-noblegas {
  border-radius: 9999px;
  -gtk-outline-radius: 9999px;
  min-width: 1.023rem;
  min-height: 1.023rem;
  background-color: #536772;
}

.cheatsheet-periodictable-legend-color-lanthanum {
  border-radius: 9999px;
  -gtk-outline-radius: 9999px;
  min-width: 1.023rem;
  min-height: 1.023rem;
  background-color: #34393c;
}

.cheatsheet-periodictable-legend-color-actinium {
  border-radius: 9999px;
  -gtk-outline-radius: 9999px;
  min-width: 1.023rem;
  min-height: 1.023rem;
  background-color: #292e31;
}

.bg-wallpaper-transition {
  transition: 1400ms cubic-bezier(0.05, 0.7, 0.1, 1);
  font-size: 1px;
}

.bg-time-box {
  border-radius: 1.705rem;
  -gtk-outline-radius: 1.705rem;
  margin: 2.045rem;
  padding: 0.682rem;
}

.bg-time-clock {
  font-family: "Geist", "GeistMono", "Cascadia Code", "Segoe UI", "Roboto", sans-serif;
  font-size: 5.795rem;
  color: #dfe3e7;
}

.bg-time-date {
  font-family: "Geist", "GeistMono", "Cascadia Code", "Segoe UI", "Roboto", sans-serif;
  font-size: 2.591rem;
  color: #dfe3e7;
}

.bg-distro-box {
  border-radius: 1.705rem;
  -gtk-outline-radius: 1.705rem;
  margin: 2.045rem;
  padding: 0.682rem;
}

.bg-distro-txt {
  font-family: "Geist", "GeistMono", "Cascadia Code", "Segoe UI", "Roboto", sans-serif;
  font-size: 1.432rem;
  color: #dfe3e7;
}

.bg-distro-name {
  font-family: "Geist", "GeistMono", "Cascadia Code", "Segoe UI", "Roboto", sans-serif;
  font-size: 1.432rem;
  color: #ffffff;
}

.bg-graph {
  color: rgba(255, 255, 255, 0.5);
  border-radius: 0.614rem;
  border: 0.682rem solid;
}

.bg-quicklaunch-title {
  font-family: "Geist", "GeistMono", "Cascadia Code", "Segoe UI", "Roboto", sans-serif;
  color: #c0c7cd;
}

.bg-quicklaunch-btn {
  font-family: "Geist", "GeistMono", "Cascadia Code", "Segoe UI", "Roboto", sans-serif;
  border-radius: 9999px;
  -gtk-outline-radius: 9999px;
  background-color: rgb(35.5, 40.5, 42.95);
  color: #e3e7eb;
  min-width: 4.432rem;
  min-height: 2.045rem;
  padding: 0.273rem 0.682rem;
}

.bg-quicklaunch-btn:hover,
.bg-quicklaunch-btn:focus {
  background-color: rgb(54.65, 59.55, 62.155);
}

.bg-quicklaunch-btn:active {
  background-color: rgb(73.8, 78.6, 81.36);
}

.bg-system-bg {
  border-radius: 1.159rem;
  -gtk-outline-radius: 1.159rem;
}

.bg-system-circprog {
  transition: 1000ms cubic-bezier(0.1, 1, 0, 1);
  min-width: 0.205rem;
  min-height: 4.091rem;
  font-size: 0px;
  padding: 0rem;
  background-color: rgb(35.5, 40.5, 42.95);
}

.dock-bg {
  box-shadow: 0 2px 3px rgba(0, 0, 0, 0.1), 0 5px 10px rgba(0, 0, 0, 0.08), 0 10px 20px rgba(0, 0, 0, 0.2);
  background-color: #0f1417;
  border-top: 0.5px solid rgba(180, 202, 214, 0);
}

.dock-round {
  border-top: 0.5px solid rgba(180, 202, 214, 0);
  border-radius: 1.159rem 1.159rem 0 0;
}

.dock-app-btn-animate {
  transition-property: color;
  transition-duration: 0.5s;
}

.dock-app-btn {
  border-radius: 1.159rem;
  -gtk-outline-radius: 1.159rem;
}

.unpinned-dock-app-btn {
  color: #080c0f;
}

.pinned-dock-app-btn {
  border-radius: 9999px;
  -gtk-outline-radius: 9999px;
  color: #b4cad6;
}

.dock-app-btn:hover,
.dock-app-btn:focus {
  background-color: rgb(46.2, 51.05, 54.2);
}

.dock-app-btn:active {
  background-color: #292e31;
}

.dock-app-icon {
  min-width: 3.409rem;
  min-height: 3.409rem;
  font-size: 3.409rem;
}

.notification-badge {
  background-color: #ffb4ab;
  color: #580003;
  border-radius: 0.6rem;
  min-width: 1.2rem;
  min-height: 1.2rem;
  margin: 0;
  padding: 0;
  font-size: 0.8rem;
  font-weight: bold;
}

.notification-count {
  font-size: 0.8rem;
  font-weight: bold;
  padding: 0;
  margin: 0;
}

.window-indicators {
  margin-bottom: 0.2rem;
  min-height: 0.4rem;
  min-width: 2rem;
}

.window-indicator {
  min-width: 0.5rem;
  min-height: 0.25rem;
  margin: 0 0.1rem;
  border-radius: 0.1rem;
  background-color: #c0c7cd;
  opacity: 0.7;
}

.active-window {
  background-color: #8ccff1;
  opacity: 1;
}

.window-count {
  font-size: 0.6rem;
  color: #c0c7cd;
  margin-left: 0.2rem;
}

.dock-separator {
  min-width: 0.04rem;
  opacity: 0.4;
  margin: 0.45rem 0.6rem;
  background-color: #90979c;
}

.osd-bg {
  min-width: 8.864rem;
  min-height: 3.409rem;
}

.osd-value {
  border-top: 1px solid #1f2426;
  border-left: 1px solid #1f2426;
  border-right: 1px solid #1f2426;
  border-bottom: 1px solid #1f2426;
  border: 0.3px solid rgba(180, 202, 214, 0);
  margin: 0.476rem;
  background-color: #0f1417;
  border-radius: 1.023rem;
  padding: 0.625rem 1.023rem;
  padding-top: 0.313rem;
}

.osd-progress {
  min-height: 0.955rem;
  min-width: 0.068rem;
  padding: 0rem;
  border-radius: 10rem;
  transition: 200ms cubic-bezier(0.1, 1, 0, 1);
}
.osd-progress trough {
  min-height: 0.954rem;
  min-width: 0.068rem;
  border-radius: 10rem;
  background-color: rgb(35.5, 40.5, 42.95);
}
.osd-progress progress {
  transition: 200ms cubic-bezier(0.1, 1, 0, 1);
  min-height: 0.68rem;
  min-width: 0.68rem;
  margin: 0rem 0.137rem;
  border-radius: 10rem;
  background-color: #e3e7eb;
}

.osd-label {
  font-size: 1.023rem;
  font-weight: 500;
  margin-top: 0.341rem;
}

.osd-value-txt {
  font-family: "Geist", "GeistMono", "Cascadia Code", "Segoe UI", "Roboto", sans-serif;
  font-size: 1.688rem;
  font-weight: 500;
  color: #dfe3e7;
}

.osd-value-icon {
  font-size: 1.688rem;
}

.osd-brightness {
  color: #dfe3e7;
}

.osd-brightness-progress progress {
  background-color: #dfe3e7;
}

.osd-volume {
  color: #dfe3e7;
}

.osd-volume-progress progress {
  background-color: #dfe3e7;
}

.osd-notifs {
  padding-top: 0.313rem;
}

.osd-round {
  border-radius: 0 0 1.159rem 1.159rem;
}

.osd-colorscheme {
  background-color: #0f1417;
  padding: 0 0.626rem;
  border-bottom: 0.5px solid rgba(180, 202, 214, 0);
}

.osd-colorscheme-settings {
  background-color: #181d20;
  padding: 0.313rem 0.626rem;
  border-radius: 0.818rem;
  -gtk-outline-radius: 0.818rem;
}

.osd-color {
  border-radius: 0.65rem;
  -gtk-outline-radius: 0.65rem;
  min-width: 2.727rem;
  min-height: 1.705rem;
  padding: 0rem 0.341rem;
  font-weight: bold;
}
.osd-color box {
  border-radius: 0.818rem;
  -gtk-outline-radius: 0.818rem;
  margin: 0.409rem;
}

.osd-color-primary {
  background-color: #8ccff1;
  color: #002c3b;
}
.osd-color-primary box {
  background-color: #002c3b;
}

.osd-color-primaryContainer {
  background-color: #1f6c89;
  color: #ffffff;
}
.osd-color-primaryContainer box {
  background-color: #ffffff;
}

.osd-color-secondary {
  background-color: #b4cad6;
  color: #162a34;
}
.osd-color-secondary box {
  background-color: #162a34;
}

.osd-color-secondaryContainer {
  background-color: #536772;
  color: #ffffff;
}
.osd-color-secondaryContainer box {
  background-color: #ffffff;
}

.osd-color-tertiary {
  background-color: #c7c2ea;
  color: #272443;
}
.osd-color-tertiary box {
  background-color: #272443;
}

.osd-color-tertiaryContainer {
  background-color: #646083;
  color: #ffffff;
}
.osd-color-tertiaryContainer box {
  background-color: #ffffff;
}

.osd-color-error {
  background-color: #ffb4ab;
  color: #580003;
}
.osd-color-error box {
  background-color: #580003;
}

.osd-color-errorContainer {
  background-color: #c32220;
  color: #ffffff;
}
.osd-color-errorContainer box {
  background-color: #ffffff;
}

.osd-color-surface {
  background-color: #0f1417;
  color: #e3e7eb;
  border: 0.068rem solid #5e656a;
}
.osd-color-surface box {
  background-color: #e3e7eb;
}

.osd-color-surfaceContainer {
  background-color: #1f2426;
  color: #e3e7eb;
}
.osd-color-surfaceContainer box {
  background-color: #e3e7eb;
}

.osd-color-inverseSurface {
  background-color: #dfe3e7;
  color: #2c3134;
}
.osd-color-inverseSurface box {
  background-color: #c0c7cd;
}

.osd-color-surfaceVariant {
  background-color: #40484c;
  color: #c0c7cd;
}
.osd-color-surfaceVariant box {
  background-color: #c0c7cd;
}

.osd-color-L1 {
  background-color: #181d20;
  color: #c0c7cd;
}
.osd-color-L1 box {
  background-color: #c0c7cd;
}

.osd-color-layer0 {
  background-color: #0f1417;
  color: #dfe3e7;
}
.osd-color-layer0 box {
  background-color: #dfe3e7;
}

.osd-settings-btn-arrow {
  border-radius: 9999px;
  -gtk-outline-radius: 9999px;
  font-family: "Material Icons", "Material Symbols Rounded", "MaterialSymbolsRounded", "Material Symbols Outlined", "Material Icons Outlined", "Material Symbols Sharp";
  min-width: 1.705rem;
  min-height: 1.705rem;
  color: #e3e7eb;
}
.osd-settings-btn-arrow:hover {
  background-color: #292e31;
}
.osd-settings-btn-arrow:active {
  background-color: #34393c;
}

.osd-show {
  transition: 200ms cubic-bezier(0.1, 1, 0, 1);
}

.osd-hide {
  transition: 190ms cubic-bezier(0.85, 0, 0.15, 1);
}

.recorder-bg {
  background-color: #0f1417;
  color: #dfe3e7;
  padding: 1rem;
  border-left: 0.2px solid rgba(180, 202, 214, 0);
}

.recorder-btn {
  border-radius: 9999px;
  -gtk-outline-radius: 9999px;
  transition: 300ms cubic-bezier(0, 0.55, 0.45, 1);
  background-color: #292e31;
  padding: 1.5rem;
  min-width: 1.8rem;
  min-height: 1.8rem;
  color: #e3e7eb;
}

.recording-state {
  font-family: "Geist", "GeistMono", "Cascadia Code", "Segoe UI", "Roboto", sans-serif;
  color: #e3e7eb;
}

.recorder-btn-red {
  border-radius: 9999px;
  -gtk-outline-radius: 9999px;
  transition: 300ms cubic-bezier(0, 0.55, 0.45, 1);
  background-color: #ffb4ab;
  color: #580003;
  padding: 1.5rem;
  min-width: 1.8rem;
  min-height: 1.8rem;
}

.record-rounding {
  border-radius: 1.705rem 0 0 1.705rem;
}

.overview-search-box {
  transition: 150ms cubic-bezier(0.42, 0, 1, 1);
  min-width: 24.636rem;
  min-height: 3.409rem;
  margin-bottom: 4rem;
  border-radius: 0 0 1.364rem 1.364rem;
  padding-right: 3.5rem;
  border-bottom: 0.3px solid rgba(180, 202, 214, 0);
  background-color: #0f1417;
  color: #dfe3e7;
  caret-color: transparent;
}
.overview-search-box selection {
  background-color: #b4cad6;
  color: #162a34;
}

.overview-search-box-spotlight {
  transition: 200ms cubic-bezier(0.05, 0.7, 0.1, 1);
  min-width: 24.636rem;
  min-height: 3.409rem;
  margin-bottom: 4rem;
  border-radius: 1.364rem;
  padding: 0.2rem 3.5rem 0.2rem 0;
  border: 0.3px solid rgba(180, 202, 214, 0);
  background-color: #0f1417;
  color: #c0c7cd;
  caret-color: transparent;
}
.overview-search-box-spotlight selection {
  background-color: #b4cad6;
  color: #162a34;
}

.overview-search-box-extended {
  min-width: 45rem;
  caret-color: #ffffff;
  margin-bottom: 0.4rem;
  padding-left: 1.364rem;
}

.overview-search-prompt {
  color: #90979c;
}

.overview-search-icon {
  margin: 0rem 1.023rem;
}

.overview-search-prompt-box {
  margin-left: -18.545rem;
  margin-right: 0.544rem;
}

.overview-search-icon-box {
  margin-left: -18.545rem;
  margin-right: 0.544rem;
}

.overview-search-results {
  border-radius: 1.159rem;
  border-top: 1px solid #1f2426;
  border-left: 1px solid #1f2426;
  border-right: 1px solid #1f2426;
  border-bottom: 1px solid #1f2426;
  border: 0.3px solid rgba(180, 202, 214, 0);
  margin: 0.476rem;
  margin: 0.8rem 4rem 4rem 4rem;
  min-width: 48.5rem;
  padding: 0.682rem;
  background-color: #0f1417;
  color: #dfe3e7;
}

.overview-search-results-icon {
  margin: 0rem 0.682rem;
  font-size: 2.386rem;
  min-width: 2.386rem;
  min-height: 2.386rem;
}

.overview-search-results-txt {
  margin-right: 0.682rem;
}

.overview-search-results-txt-cmd {
  margin-right: 0.682rem;
  font-family: "Geist Mono", "Geist Mono NF", "Cascadia Mono", "Fira Mono", monospace;
  font-size: 1.227rem;
}

.overview-search-result-btn {
  border-radius: 1.159rem;
  -gtk-outline-radius: 1.159rem;
  padding: 0.341rem;
  min-width: 2.386rem;
  min-height: 2.386rem;
  caret-color: transparent;
}

.overview-search-result-btn:hover,
.overview-search-result-btn:focus {
  background-color: rgb(35.5, 40.5, 42.95);
}

.overview-search-result-btn:active {
  background-color: rgb(54.65, 59.55, 62.155);
}

.overview-round {
  border-radius: 1.364rem 1.364rem 0 0;
}

.overview-tasks {
  border-top: 0.2px solid rgba(180, 202, 214, 0);
  padding: 1rem;
  background-color: #0f1417;
  color: #dfe3e7;
}

.overview-tasks-workspace {
  border-radius: 1.159rem;
  -gtk-outline-radius: 1.159rem;
  margin: 0.341rem;
  background-color: #181d20;
}

.overview-tasks-workspace-number {
  font-family: "Geist", "GeistMono", "Cascadia Code", "Segoe UI", "Roboto", sans-serif;
  color: #c0c7cd;
}

.overview-tasks-window {
  border-radius: 1.159rem;
  -gtk-outline-radius: 1.159rem;
  transition: 400ms cubic-bezier(0.1, 1, 0, 1);
  background-color: rgba(41, 46, 49, 0.8);
  color: #e3e7eb;
  border: 0.1px solid rgba(180, 202, 214, 0);
}

.overview-tasks-window:hover,
.overview-tasks-window:focus {
  background-color: rgba(83, 103, 114, 0.7);
}

.overview-tasks-window:active {
  background-color: #536772;
}

.overview-tasks-window-selected {
  background-color: rgba(83, 103, 114, 0.7);
}

.overview-tasks-window-dragging {
  opacity: 0.2;
}

.sidebar-right-bg {
  transition: 400ms cubic-bezier(0.1, 1, 0, 1);
  background-color: #0f1417;
  padding: 1.023rem;
}

.sidebar-right-rounded {
  border-radius: 0 0.818rem 0.818rem 0;
}

.sidebar-left-bg {
  transition: 400ms cubic-bezier(0.1, 1, 0, 1);
  background-color: #0f1417;
  padding: 1.023rem;
}

.sidebar-left-rounded {
  border-radius: 0.818rem 0 0 0.818rem;
}

.sidebar-group {
  border-radius: 1.159rem;
  -gtk-outline-radius: 1.159rem;
  padding: 0.341rem;
  background-color: #181d20;
}

.calendarsep {
  margin: 50px;
}

.sidebar-group-nopad {
  border-radius: 1.159rem;
  -gtk-outline-radius: 1.159rem;
  background-color: #181d20;
}

.sidebar-group-invisible {
  padding: 0.341rem;
}

.sidebar-group-invisible-morehorizpad {
  padding: 0.341rem 0.682rem;
}

.sidebar-togglesbox {
  border-radius: 9999px;
  -gtk-outline-radius: 9999px;
  padding: 0.341rem;
  background-color: #181d20;
}

.sidebar-iconbutton {
  border-radius: 9999px;
  -gtk-outline-radius: 9999px;
  transition: 300ms cubic-bezier(0, 0.55, 0.45, 1);
  color: #c0c7cd;
  min-width: 2.727rem;
  min-height: 2.727rem;
}

.sidebar-iconbutton:hover,
.sidebar-iconbutton:focus {
  background-color: rgb(49.2, 54.5, 57.95);
}

.sidebar-iconbutton:active {
  background-color: rgb(74.4, 80, 83.9);
}

.sidebar-button-active {
  background-color: #8ccff1;
  color: #002c3b;
}

.sidebar-button-active:hover,
.sidebar-button-active:focus {
  background-color: rgb(112.76, 161.25, 186.085);
}

.sidebar-button-active:active {
  background-color: rgb(100.64, 130.8, 146.74);
}

.sidebar-buttons-separator {
  min-width: 0.068rem;
  min-height: 0.068rem;
  background-color: #c0c7cd;
}

.sidebar-navrail {
  padding: 0rem 1.159rem;
}

.sidebar-navrail-btn > box > label {
  border-radius: 9999px;
  -gtk-outline-radius: 9999px;
  transition: 400ms cubic-bezier(0.1, 1, 0, 1);
}

.sidebar-navrail-btn:hover > box > label:first-child,
.sidebar-navrail-btn:focus > box > label:first-child {
  background-color: rgb(49.2, 54.5, 57.95);
}

.sidebar-navrail-btn:active > box > label:first-child {
  background-color: rgb(74.4, 80, 83.9);
}

.sidebar-navrail-btn-active > box > label:first-child {
  background-color: #536772;
  color: #ffffff;
}

.sidebar-navrail-btn-active:hover > box > label:first-child,
.sidebar-navrail-btn-active:focus > box > label:first-child {
  background-color: rgb(79.62, 98.15, 108.395);
  color: rgb(234.42, 234.95, 235.295);
}

.sidebar-sysinfo-grouppad {
  padding: 1.159rem;
}

.sidebar-memory-ram-circprog {
  transition: 1000ms cubic-bezier(0.1, 1, 0, 1);
  min-width: 0.818rem;
  min-height: 4.091rem;
  padding: 0.409rem;
  background-color: #536772;
  color: #ffffff;
  font-size: 0px;
}

.sidebar-memory-swap-circprog {
  transition: 1000ms cubic-bezier(0.1, 1, 0, 1);
  min-width: 0.818rem;
  min-height: 2.255rem;
  padding: 0.409rem;
  margin: 0.918rem;
  background-color: #536772;
  color: #ffffff;
  font-size: 0px;
}

.sidebar-cpu-circprog {
  min-width: 0.818rem;
  min-height: 3.409rem;
  padding: 0.409rem;
  background-color: #536772;
  color: #ffffff;
  transition: 1000ms cubic-bezier(0.1, 1, 0, 1);
  font-size: 0px;
}

.sidebar-scrollbar trough {
  border-radius: 9999px;
  -gtk-outline-radius: 9999px;
  min-width: 0.545rem;
  background-color: transparent;
}
.sidebar-scrollbar slider {
  border-radius: 9999px;
  -gtk-outline-radius: 9999px;
  transition: 300ms cubic-bezier(0, 0.55, 0.45, 1);
  min-width: 0.273rem;
  min-height: 2.045rem;
  background-color: rgba(192, 199, 205, 0.3);
}
.sidebar-scrollbar slider:hover,
.sidebar-scrollbar slider:focus {
  background-color: rgba(192, 199, 205, 0.4);
}
.sidebar-scrollbar slider:active {
  background-color: rgba(227, 231, 235, 0.5);
}

.sidebar-calendar-btn {
  border-radius: 9999px;
  -gtk-outline-radius: 9999px;
  transition: 300ms cubic-bezier(0, 0.55, 0.45, 1);
  min-height: 2.523rem;
  min-width: 2.523rem;
  color: #e3e7eb;
}

.sidebar-calendar-btn:hover,
.sidebar-calendar-btn:focus {
  background-color: #292e31;
}

.sidebar-calendar-btn:active {
  background-color: #34393c;
}

.sidebar-calendar-btn-txt {
  margin-left: -10.341rem;
  margin-right: -10.341rem;
}

.sidebar-calendar-btn-today {
  background-color: #8ccff1;
  color: #002c3b;
}

.sidebar-calendar-btn-today:hover,
.sidebar-calendar-btn-today:focus {
  background-color: rgb(110.3, 158.7, 183.4);
}

.sidebar-calendar-btn-today:active {
  background-color: rgb(80.6, 110.4, 125.8);
}

.sidebar-calendar-btn-othermonth {
  color: #90979c;
}

.sidebar-calendar-header {
  margin: 0.341rem;
}

.sidebar-calendar-monthyear-btn {
  border-radius: 9999px;
  -gtk-outline-radius: 9999px;
  transition: 300ms cubic-bezier(0, 0.55, 0.45, 1);
  padding: 0rem 0.682rem;
  background-color: rgb(35.5, 40.5, 42.95);
  color: #e3e7eb;
}

.sidebar-calendar-monthyear-btn:hover,
.sidebar-calendar-monthyear-btn:focus {
  background-color: #292e31;
}

.sidebar-calendar-monthyear-btn:active {
  background-color: #34393c;
}

.sidebar-calendar-monthshift-btn {
  border-radius: 9999px;
  -gtk-outline-radius: 9999px;
  transition: 300ms cubic-bezier(0, 0.55, 0.45, 1);
  min-width: 2.045rem;
  min-height: 2.045rem;
  background-color: rgb(35.5, 40.5, 42.95);
  color: #90979c;
}

.sidebar-calendar-monthshift-btn:hover {
  background-color: #292e31;
}

.sidebar-calendar-monthshift-btn:active {
  background-color: #34393c;
}

.sidebar-todo-item {
  border-radius: 0.818rem;
  -gtk-outline-radius: 0.818rem;
  margin-right: 0.545rem;
  background-color: rgb(35.5, 40.5, 42.95);
  color: #e3e7eb;
}

.sidebar-todo-txt {
  margin: 0.682rem;
  margin-bottom: 0rem;
}

.sidebar-todo-actions {
  margin: 0.341rem;
  margin-top: 0rem;
  margin-right: 0rem;
}

.sidebar-todo-item-action {
  transition: 300ms cubic-bezier(0, 0.55, 0.45, 1);
  border-radius: 9999px;
  min-width: 1.705rem;
  min-height: 1.705rem;
}

.sidebar-todo-item-action:hover,
.sidebar-todo-item-action:focus {
  background-color: rgb(54.65, 59.55, 62.155);
}

.sidebar-todo-item-action:active {
  background-color: rgb(73.8, 78.6, 81.36);
}

.sidebar-todo-crosser {
  transition: margin 200ms cubic-bezier(0.1, 1, 0, 1), background-color 0ms;
  min-width: 0rem;
}

.sidebar-todo-crosser-crossed {
  background-color: transparent;
}

.sidebar-todo-crosser-removed {
  background-color: transparent;
}

.sidebar-todo-new {
  border-radius: 9999px;
  -gtk-outline-radius: 9999px;
  transition: 300ms cubic-bezier(0, 0.55, 0.45, 1);
  background-color: rgb(35.5, 40.5, 42.95);
  color: #e3e7eb;
  margin: 0.341rem;
  padding: 0.205rem 0.545rem;
}

.sidebar-todo-new,
.sidebar-todo-new:focus {
  color: #ffffff;
  background-color: #536772;
}

.sidebar-todo-new:active {
  color: #ffffff;
  background-color: #1f6c89;
}

.sidebar-todo-add {
  transition: 300ms cubic-bezier(0, 0.55, 0.45, 1);
  border-radius: 0.818rem;
  -gtk-outline-radius: 0.818rem;
  min-width: 1.705rem;
  min-height: 1.705rem;
  color: #ffffff;
  border: 0.068rem solid #e3e7eb;
}

.sidebar-todo-add:hover,
.sidebar-todo-add:focus {
  background-color: #292e31;
}

.sidebar-todo-add:active {
  background-color: #34393c;
}

.sidebar-todo-add-available {
  transition: 300ms cubic-bezier(0, 0.55, 0.45, 1);
  border-radius: 0.818rem;
  -gtk-outline-radius: 0.818rem;
  min-width: 1.705rem;
  min-height: 1.705rem;
  background-color: #8ccff1;
  color: #002c3b;
  border: 0.068rem solid #8ccff1;
}

.sidebar-todo-add-available:hover,
.sidebar-todo-add-available:focus {
  background-color: rgb(110.3, 158.7, 183.4);
}

.sidebar-todo-add-available:active {
  background-color: rgb(80.6, 110.4, 125.8);
}

.sidebar-todo-entry {
  transition: 300ms cubic-bezier(0, 0.55, 0.45, 1);
  border-radius: 0.818rem;
  -gtk-outline-radius: 0.818rem;
  background-color: #40484c;
  color: #c0c7cd;
  caret-color: #c0c7cd;
  margin: 0rem 0.341rem;
  min-height: 1.773rem;
  min-width: 0rem;
  padding: 0.205rem 0.682rem;
  border: 0.068rem solid #90979c;
}

.sidebar-todo-entry:focus {
  border: 0.068rem solid #c0c7cd;
}

.sidebar-module {
  border-radius: 1.159rem;
  -gtk-outline-radius: 1.159rem;
  padding: 0.341rem;
  background-color: #181d20;
  padding: 0.682rem;
}

.bluetooth-controller-info {
  margin-top: 0.682rem;
  margin-bottom: 0.682rem;
}

.bluetooth-status-indicator {
  border-radius: 9999px;
  -gtk-outline-radius: 9999px;
  padding: 0.341rem;
  background-color: #292e31;
  color: #c0c7cd;
  opacity: 0.5;
}
.bluetooth-status-indicator.active {
  background-color: #536772;
  color: #ffffff;
  opacity: 1;
}
.bluetooth-status-indicator.inactive {
  background-color: #292e31;
  color: #c0c7cd;
  opacity: 0.5;
}

.bluetooth-device-section {
  margin-bottom: 1rem;
}

.bluetooth-section-separator {
  min-height: 1px;
  background-color: #5e656a;
  opacity: 0.5;
  margin: 0.8rem 0;
  border-radius: 1px;
}

.sidebar-bluetooth-controls {
  padding: 1rem;
  margin-bottom: 1rem;
  background-color: #181d20;
  border-radius: 1.159rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.bluetooth-controls-row {
  margin-bottom: 0.8rem;
  /* GTK doesn't support align-items */
  /* Use valign property on child elements instead */
}

.sidebar-bluetooth-scan-button {
  background-color: #8ccff1;
  color: #002c3b;
  padding: 0.5rem 1rem;
  border-radius: 1.159rem;
  font-weight: 500;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  transition: all 200ms ease;
}
.sidebar-bluetooth-scan-button:hover {
  background-color: rgb(131.2, 192, 222.9);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
}
.sidebar-bluetooth-scan-button:active {
  background-color: rgb(113.6, 162, 186.7);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}
.sidebar-bluetooth-scan-button.scanning {
  background-color: #c7c2ea;
  color: #272443;
}

.bluetooth-empty-state {
  padding: 2rem;
}

.bluetooth-empty-title {
  font-size: 1.2rem;
  font-weight: 500;
  margin: 1rem 0 0.5rem;
}

.bluetooth-empty-subtitle {
  color: #c0c7cd;
  margin-bottom: 1rem;
}

.bluetooth-empty-scan-button {
  background-color: #8ccff1;
  color: #002c3b;
  padding: 0.5rem 1.5rem;
  border-radius: 1.159rem;
  font-weight: 500;
  margin-top: 1rem;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  /* GTK doesn't support all transitions */
  /* We'll use specific properties instead */
}
.bluetooth-empty-scan-button:hover {
  background-color: rgb(131.2, 192, 222.9);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
  /* GTK doesn't support transform or margin transitions */
  /* We'll use opacity changes instead */
  opacity: 0.9;
}

.bluetooth-empty-manager-button {
  background-color: #292e31;
  color: #c0c7cd;
  padding: 0.5rem 1.5rem;
  border-radius: 1.159rem;
  margin-top: 0.5rem;
  /* GTK doesn't support all transitions */
  /* We'll use specific properties instead */
}
.bluetooth-empty-manager-button:hover {
  background-color: #34393c;
  color: #e3e7eb;
}

.bluetooth-controller-info {
  background-color: #080c0f;
  padding: 1rem;
  border-radius: 1.159rem;
  margin-top: 1rem;
}

.bluetooth-controller-status {
  margin-top: 0.5rem;
}

.bluetooth-status-indicator {
  /* GTK doesn't support display, align-items, justify-content, width, height */
  /* Use min-width, min-height and child element positioning instead */
  min-width: 2rem;
  min-height: 2rem;
  border-radius: 9999px; /* Use a large value for circular shape */
  background-color: #181d20;
}
.bluetooth-status-indicator.active {
  background-color: #1f6c89;
  color: #ffffff;
}
.bluetooth-status-indicator.inactive {
  background-color: #292e31;
  color: #c0c7cd;
  opacity: 0.7;
}

.sidebar-bluetooth-bottombar {
  margin-top: 1rem;
  padding: 0.8rem;
  background-color: #181d20;
  border-radius: 1.159rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.sidebar-bluetooth-bottombar-button {
  background-color: #292e31;
  color: #e3e7eb;
  padding: 0.5rem 1rem;
  border-radius: 1.159rem;
  font-weight: 500;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  /* GTK doesn't support all transitions */
  /* We'll use specific properties instead */
}
.sidebar-bluetooth-bottombar-button:hover {
  background-color: #1f6c89;
  color: #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  /* GTK doesn't support transform or margin transitions */
  /* We'll use opacity changes instead */
  opacity: 0.9;
}
.sidebar-bluetooth-bottombar-button:active {
  background-color: #8ccff1;
  color: #002c3b;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  opacity: 0.8;
}

.sidebar-bluetooth-settings-button {
  background-color: #292e31;
  color: #c0c7cd;
  padding: 0.5rem;
  border-radius: 9999px; /* Use a large value for circular shape */
  /* GTK doesn't support all transitions */
  /* We'll use specific properties instead */
}
.sidebar-bluetooth-settings-button:hover {
  background-color: #1f6c89;
  color: #ffffff;
  /* GTK doesn't support transform */
  /* We'll skip the rotation effect */
}

/* Bluetooth toggle with label - spacing handled in JavaScript */
.sidebar-bluetooth-device-action {
  border-radius: 9999px;
  -gtk-outline-radius: 9999px;
}

.sidebar-bluetooth-device {
  border-radius: 1.159rem;
  -gtk-outline-radius: 1.159rem;
  padding: 0.8rem;
  margin-bottom: 0.8rem;
  background-color: #181d20;
  /* GTK doesn't support all transitions */
  /* We'll use specific properties instead */
  border-left: 3px solid transparent;
  margin-left: 2px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  transition: background-color 200ms ease, box-shadow 200ms ease;
  /* GTK doesn't support align-items */
  /* Use valign property on child elements instead */
}
.sidebar-bluetooth-device .sidebar-bluetooth-device-icon-container {
  margin-right: 12px;
}
.sidebar-bluetooth-device .sidebar-bluetooth-icon-box {
  border-radius: 1.159rem;
  -gtk-outline-radius: 1.159rem;
}
.sidebar-bluetooth-device .sidebar-bluetooth-icon {
  font-family: "Material Icons", "Material Symbols Rounded";
}
.sidebar-bluetooth-device .bluetooth-device-name {
  font-weight: 500;
  margin-bottom: 2px;
}
.sidebar-bluetooth-device .bluetooth-device-status {
  font-size: 0.8em;
  opacity: 0.8;
}
.sidebar-bluetooth-device.bluetooth-device-connected {
  background-color: #536772;
  border-left-color: #8ccff1;
  margin-left: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
.sidebar-bluetooth-device.bluetooth-device-connected .bluetooth-device-name {
  color: #ffffff;
  font-weight: 600;
}
.sidebar-bluetooth-device.bluetooth-device-connected .bluetooth-device-status {
  color: #ffffff;
  opacity: 0.9;
}
.sidebar-bluetooth-device.bluetooth-device-paired {
  background-color: #292e31;
  border-left-color: #536772;
}
.sidebar-bluetooth-device.bluetooth-device-paired .bluetooth-device-name {
  color: #e3e7eb;
}
.sidebar-bluetooth-device.bluetooth-device-paired .bluetooth-device-status {
  color: #c0c7cd;
}
.sidebar-bluetooth-device.bluetooth-device-available {
  background-color: #080c0f;
}
.sidebar-bluetooth-device.bluetooth-device-available .bluetooth-device-name {
  color: #e3e7eb;
}
.sidebar-bluetooth-device:hover {
  background-color: #34393c;
  /* GTK doesn't support transform or margin-top/bottom transitions */
  /* We'll use opacity changes instead */
  opacity: 0.95;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
}
.sidebar-bluetooth-device:hover.bluetooth-device-connected {
  background-color: rgb(79.9, 98.4, 108.6);
}
.sidebar-bluetooth-device:hover.bluetooth-device-paired {
  background-color: rgb(42.1, 47.1, 50.1);
}

.sidebar-module-btn-arrow {
  border-radius: 9999px;
  -gtk-outline-radius: 9999px;
  font-family: "Material Icons", "Material Symbols Rounded", "MaterialSymbolsRounded", "Material Symbols Outlined", "Material Icons Outlined", "Material Symbols Sharp";
  background-color: rgb(35.5, 40.5, 42.95);
  min-width: 1.705rem;
  min-height: 1.705rem;
}
.sidebar-module-btn-arrow:hover, .sidebar-module-btn-arrow:focus {
  background-color: rgb(54.65, 59.55, 62.155);
}
.sidebar-module-btn-arrow:active {
  background-color: rgb(73.8, 78.6, 81.36);
}

.sidebar-module-scripts-button {
  border-radius: 9999px;
  -gtk-outline-radius: 9999px;
  font-family: "Material Icons", "Material Symbols Rounded", "MaterialSymbolsRounded", "Material Symbols Outlined", "Material Icons Outlined", "Material Symbols Sharp";
  background-color: #181d20;
  min-width: 1.705rem;
  min-height: 1.705rem;
}
.sidebar-module-scripts-button:hover, .sidebar-module-scripts-button:focus {
  background-color: rgb(49.2, 54.5, 57.95);
}
.sidebar-module-scripts-button:active {
  background-color: rgb(74.4, 80, 83.9);
}

.sidebar-module-colorpicker-wrapper {
  padding: 0.341rem;
}

.sidebar-module-colorpicker-cursorwrapper {
  padding: 0.341rem 0.136rem;
}

.sidebar-module-colorpicker-hue {
  min-height: 13.636rem;
  min-width: 1.091rem;
  border-radius: 0.341rem;
}

.sidebar-module-colorpicker-hue-cursor {
  background-color: #dfe3e7;
  border: 0.136rem solid #dfe3e7;
  min-height: 0.136rem;
  margin-top: -0.136rem;
  border-radius: 0.341rem;
}

.sidebar-module-colorpicker-saturationandlightness-wrapper {
  padding: 0.341rem;
}

.sidebar-module-colorpicker-saturationandlightness {
  min-height: 13.636rem;
  min-width: 13.636rem;
  border-radius: 0.341rem;
}

.sidebar-module-colorpicker-saturationandlightness-cursorwrapper {
  padding: 0.341rem;
  margin-top: -0.409rem;
  margin-left: -0.409rem;
}

.sidebar-module-colorpicker-saturationandlightness-cursor {
  border-radius: 9999px;
  -gtk-outline-radius: 9999px;
  border: 0.136rem solid white;
  min-width: 0.682rem;
  min-height: 0.682rem;
  margin-top: -0.409rem;
  margin-left: -0.409rem;
}

.sidebar-module-colorpicker-result-area {
  padding: 0.341rem;
}

.sidebar-module-colorpicker-result-box {
  border-radius: 0.341rem;
  min-width: 2.045rem;
  min-height: 0.682rem;
  padding: 0.341rem;
}

.sidebar-icontabswitcher {
  border-radius: 9999px;
  -gtk-outline-radius: 9999px;
  padding: 0.341rem;
  background-color: #181d20;
}

.sidebar-chat-providerswitcher {
  border-radius: 0.818rem;
  -gtk-outline-radius: 0.818rem;
  padding: 0.477rem 0.682rem;
  background-color: #292e31;
  color: #c0c7cd;
}

.sidebar-chat-viewport {
  transition: 300ms cubic-bezier(0, 0.55, 0.45, 1);
  padding: 0.682rem 0rem;
}

.sidebar-chat-textarea {
  border-radius: 1.159rem;
  -gtk-outline-radius: 1.159rem;
  background-color: #181d20;
  color: #c0c7cd;
  padding: 0.682rem;
}

.sidebar-chat-entry {
  color: #c0c7cd;
  caret-color: #c0c7cd;
  min-height: 1.773rem;
  min-width: 0rem;
}

/* Removed transition from sidebar-chat-wrapper */
.sidebar-chat-wrapper-extended {
  min-height: 7.5rem;
}

.sidebar-chat-send {
  transition: 300ms cubic-bezier(0, 0.55, 0.45, 1);
  min-width: 1.705rem;
  min-height: 1.705rem;
  border-radius: 0.478rem;
}

.sidebar-chat-send:hover,
.sidebar-chat-send:focus {
  background-color: #393e41;
}

.sidebar-chat-send:active {
  background-color: #40484c;
}

.sidebar-chat-send-available {
  background-color: #8ccff1;
  color: #002c3b;
}

.sidebar-chat-send-available:hover,
.sidebar-chat-send-available:focus {
  background-color: rgb(110.3, 158.7, 183.4);
}

.sidebar-chat-send-available:active {
  background-color: rgb(80.6, 110.4, 125.8);
}

.sidebar-chat-messagearea {
  margin: 0.341rem;
}

.sidebar-chat-message {
  border-radius: 1.159rem;
  -gtk-outline-radius: 1.159rem;
  padding: 0.341rem;
  background-color: #181d20;
}

@keyframes sidebar-chat-message-skeletonline-anim {
  0% {
    background-position: 175% 0%;
  }
  100% {
    background-position: 50% 0%;
  }
}
.sidebar-chat-message-skeletonline {
  border-radius: 0.477rem;
  min-height: 1.364rem;
  background-color: rgb(35.5, 40.5, 42.95);
}

.sidebar-chat-message-skeletonline-offset0 {
  background-repeat: no-repeat;
  background: linear-gradient(to right, #292e31 0%, #657a85 25%, #292e31 50%, #292e31 100%);
  background-size: 500% 500%;
  animation: sidebar-chat-message-skeletonline-anim 2s linear;
  animation-iteration-count: infinite;
}

.sidebar-chat-message-skeletonline-offset1 {
  background-repeat: no-repeat;
  background: linear-gradient(to right, #292e31 0%, #292e31 50%, #657a85 75%, #292e31 100%);
  background-size: 500% 500%;
  animation: sidebar-chat-message-skeletonline-anim 2s linear;
  animation-iteration-count: infinite;
}

.sidebar-chat-message-skeletonline-offset2 {
  margin-right: 5.795rem;
  background-repeat: no-repeat;
  background: linear-gradient(to right, #292e31 0%, #292e31 25%, #657a85 50%, #292e31 75%, #292e31 100%);
  background-size: 500% 500%;
  animation: sidebar-chat-message-skeletonline-anim 2s linear;
  animation-iteration-count: infinite;
}

.sidebar-chat-indicator {
  transition: 300ms cubic-bezier(0, 0.55, 0.45, 1);
  border-radius: 9999px;
  -gtk-outline-radius: 9999px;
  min-width: 0.136rem;
}

.sidebar-chat-name {
  font-family: "Geist", "GeistMono", "Cascadia Code", "Segoe UI", "Roboto", sans-serif;
  border-radius: 0.818rem;
  -gtk-outline-radius: 0.818rem;
  padding: 0.341rem 0.818rem;
  margin: 0.341rem;
  background-color: rgb(35.5, 40.5, 42.95);
  color: #e3e7eb;
}

.sidebar-chat-name-user {
  background-color: rgb(35.5, 40.5, 42.95);
  color: #e3e7eb;
}

.sidebar-chat-name-bot {
  background-color: #b4cad6;
  color: #162a34;
}

.sidebar-chat-name-system {
  background-color: #536772;
  color: #ffffff;
}

.sidebar-chat-txtblock {
  margin-left: -0.136rem;
  padding: 0.341rem;
  padding-left: 0.818rem;
}

.sidebar-chat-txt {
  font-family: "Geist", "GeistMono", "Cascadia Code", "Segoe UI", "Open Sans", sans-serif;
  font-weight: 500;
}

.sidebar-chat-latex {
  border-radius: 0.818rem;
  -gtk-outline-radius: 0.818rem;
  margin: 0rem 0.682rem;
  padding: 0.682rem;
  color: #dfe3e7;
}

.sidebar-chat-codeblock {
  border-radius: 1.159rem;
  -gtk-outline-radius: 1.159rem;
  background-color: rgb(35.5, 40.5, 42.95);
  color: #e3e7eb;
  margin: 0rem 0.682rem;
}

.sidebar-chat-codeblock-topbar {
  font-family: "Geist", "GeistMono", "Cascadia Code", "Segoe UI", "Roboto", sans-serif;
  background-color: #292e31;
  color: #e3e7eb;
  border-top-left-radius: 0.818rem;
  border-top-right-radius: 0.818rem;
  padding: 0.341rem 0.477rem;
}

.sidebar-chat-codeblock-topbar-txt {
  border-radius: 9999px;
  -gtk-outline-radius: 9999px;
  padding: 0.273rem;
}

.sidebar-chat-codeblock-topbar-btn {
  border-radius: 9999px;
  -gtk-outline-radius: 9999px;
  transition: 300ms cubic-bezier(0, 0.55, 0.45, 1);
  padding: 0.273rem 0.477rem;
}

.sidebar-chat-codeblock-topbar-btn:hover,
.sidebar-chat-codeblock-topbar-btn:focus {
  background-color: #393e41;
}

.sidebar-chat-codeblock-topbar-btn:active {
  background-color: #40484c;
}

.sidebar-chat-codeblock-code {
  font-family: "Geist Mono", "Geist Mono NF", "Cascadia Mono", "Fira Mono", monospace;
  padding: 0.682rem;
}

.sidebar-chat-divider {
  min-height: 1px;
  background-color: rgb(115.2, 122.8, 127.6);
  margin: 0rem 0.545rem;
}

.sidebar-chat-welcome-txt {
  margin: 0rem 3.409rem;
}

.sidebar-chat-settings-toggles {
  margin: 0rem 5.455rem;
}

.sidebar-chat-welcome-icon {
  border-radius: 9999px;
  -gtk-outline-radius: 9999px;
  font-size: 4rem;
}

.sidebar-chat-welcome-logo {
  border-radius: 9999px;
  -gtk-outline-radius: 9999px;
  transition: 300ms cubic-bezier(0, 0.55, 0.45, 1);
  font-family: "Material Icons", "Material Symbols Rounded", "MaterialSymbolsRounded", "Material Symbols Outlined", "Material Icons Outlined", "Material Symbols Sharp";
  min-height: 4.773rem;
  min-width: 4.773rem;
  font-size: 3.076rem;
  background-color: #536772;
  color: #ffffff;
}

.sidebar-chat-chip {
  transition: 300ms cubic-bezier(0, 0.55, 0.45, 1);
  border-radius: 0.818rem;
  -gtk-outline-radius: 0.818rem;
  padding: 0.341rem 0.477rem;
}

.sidebar-chat-chip-action {
  transition: 300ms cubic-bezier(0, 0.55, 0.45, 1);
  background-color: rgb(35.5, 40.5, 42.95);
  color: #c0c7cd;
}

.sidebar-chat-chip-action:hover,
.sidebar-chat-chip-action:focus {
  background-color: #292e31;
}

.sidebar-chat-chip-action:active {
  background-color: #34393c;
}

.sidebar-chat-chip-action-active {
  color: rgb(115.2, 122.8, 127.6);
  border: 0.068rem solid rgb(115.2, 122.8, 127.6);
}

.sidebar-chat-chip-toggle {
  transition: 300ms cubic-bezier(0, 0.55, 0.45, 1);
  border-radius: 0.818rem;
  -gtk-outline-radius: 0.818rem;
  padding: 0.341rem 0.477rem;
  background-color: #292e31;
  color: #c0c7cd;
}

.sidebar-chat-chip-toggle:focus,
.sidebar-chat-chip-toggle:hover {
  background-color: #292e31;
}

.sidebar-chat-chip-toggle:active {
  background-color: #34393c;
}

.sidebar-pin {
  border-radius: 0.818rem;
  -gtk-outline-radius: 0.818rem;
  transition: 300ms cubic-bezier(0, 0.55, 0.45, 1);
  min-height: 2.386rem;
  min-width: 2.386rem;
  color: #e3e7eb;
}

.sidebar-pin:hover,
.sidebar-pin:focus {
  background-color: #292e31;
}

.sidebar-pin:active {
  background-color: #34393c;
}

.sidebar-pin-enabled {
  background-color: #8ccff1;
}
.sidebar-pin-enabled label {
  color: #002c3b;
}

.sidebar-pin-enabled:hover,
.sidebar-pin-enabled:focus {
  background-color: rgb(110.3, 158.7, 183.4);
}

.sidebar-pin-enabled:active {
  background-color: rgb(80.6, 110.4, 125.8);
}

.sidebar-volmixer-stream {
  border-bottom: 0.068rem solid #5e656a;
  padding: 0.682rem;
  color: #e3e7eb;
}

.sidebar-volmixer-stream-appicon {
  font-size: 3.273rem;
}

.sidebar-volmixer-stream-slider trough {
  border-radius: 0.477rem;
  min-height: 1.364rem;
  min-width: 1.364rem;
  background-color: #536772;
}
.sidebar-volmixer-stream-slider highlight {
  border-radius: 0.477rem;
  min-height: 1.364rem;
  min-width: 1.364rem;
  background-color: #8ccff1;
}
.sidebar-volmixer-stream-slider slider {
  border-radius: 0.477rem;
  min-height: 1.364rem;
  min-width: 1.364rem;
}

.sidebar-volmixer-status {
  color: #e3e7eb;
  margin: 0rem 0.682rem;
}

.sidebar-volmixer-deviceselector {
  border-radius: 0.818rem;
  -gtk-outline-radius: 0.818rem;
  padding: 0.477rem 0.682rem;
  background-color: #292e31;
  color: #c0c7cd;
}

.sidebar-bluetooth-controls {
  padding: 0.682rem;
  margin-bottom: 0.682rem;
  border-radius: 1.159rem;
  -gtk-outline-radius: 1.159rem;
  background-color: #181d20;
  /* Removed bottom border */
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.sidebar-bluetooth-header {
  padding: 0.341rem 0;
  margin-bottom: 0.682rem;
}

.sidebar-bluetooth-title {
  font-weight: bold;
  color: #8ccff1;
}

.sidebar-bluetooth-divider {
  background-color: #5e656a;
  margin: 0.341rem 0;
}

.material-card {
  border-radius: 1.159rem;
  -gtk-outline-radius: 1.159rem;
  background-color: rgb(35.5, 40.5, 42.95);
  /* GTK doesn't support transitions */
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.material-button {
  border-radius: 1.159rem;
  -gtk-outline-radius: 1.159rem;
  background-color: #8ccff1;
  color: #002c3b;
  padding: 0.341rem 0.682rem;
  /* GTK doesn't support transitions */
}

.material-icon-button {
  border-radius: 9999px;
  -gtk-outline-radius: 9999px;
  padding: 0.341rem;
  /* GTK doesn't support transitions */
}

.sidebar-bluetooth-scan-button {
  border-radius: 1.159rem;
  -gtk-outline-radius: 1.159rem;
  transition: 300ms cubic-bezier(0, 0.55, 0.45, 1);
  padding: 0.341rem 0.682rem;
  background-color: #8ccff1;
  color: #002c3b;
}

@keyframes pulse {
  0% {
    background-color: rgb(90.5, 126.5, 145);
  }
  50% {
    background-color: rgb(110.3, 158.7, 183.4);
  }
  100% {
    background-color: rgb(90.5, 126.5, 145);
  }
}
.scanning {
  background-color: rgb(90.5, 126.5, 145);
  animation: pulse 2s infinite ease-in-out;
}

.sidebar-bluetooth-device {
  padding: 0.682rem;
  margin-bottom: 0.682rem;
  border-radius: 1.159rem;
  -gtk-outline-radius: 1.159rem;
  background-color: rgb(35.5, 40.5, 42.95);
  color: #e3e7eb;
  /* GTK doesn't support transitions */
  border-left: 3px solid transparent;
}
.sidebar-bluetooth-device:hover {
  background-color: rgb(45.95, 57.15, 62.755);
}

.sidebar-bluetooth-device-icon-container {
  margin: 0;
  padding: 0;
}

.sidebar-bluetooth-battery {
  color: #8ccff1;
  font-weight: bold;
  margin-top: 0.2rem;
}

.sidebar-bluetooth-appicon {
  -gtk-icon-theme: "Adwaita";
  font-size: 2.045rem;
}

.sidebar-bluetooth-battery {
  margin-top: -0.341rem;
  margin-left: -0.341rem;
  background-color: #292e31;
  padding: 0.136rem 0.273rem;
  border-radius: 0.682rem;
}

.sidebar-bluetooth-device-action {
  border-radius: 9999px;
  -gtk-outline-radius: 9999px;
  min-width: 2.045rem;
  min-height: 2.045rem;
  padding: 0.341rem;
}
.sidebar-bluetooth-device-action:hover, .sidebar-bluetooth-device-action:focus {
  background-color: rgb(54.65, 59.55, 62.155);
}
.sidebar-bluetooth-device-action:active {
  background-color: rgb(73.8, 78.6, 81.36);
}

.sidebar-bluetooth-device-remove {
  border-radius: 9999px;
  -gtk-outline-radius: 9999px;
  min-width: 2.045rem;
  min-height: 2.045rem;
  padding: 0.341rem;
}
.sidebar-bluetooth-device-remove:hover, .sidebar-bluetooth-device-remove:focus {
  background-color: rgb(54.65, 59.55, 62.155);
}
.sidebar-bluetooth-device-remove:active {
  background-color: rgb(73.8, 78.6, 81.36);
}

.sidebar-bluetooth-device-actions {
  padding: 0.341rem;
  background-color: #292e31;
  border-radius: 0.818rem;
  -gtk-outline-radius: 0.818rem;
}

.sidebar-bluetooth-action-button {
  border-radius: 9999px;
  -gtk-outline-radius: 9999px;
  transition: 300ms cubic-bezier(0, 0.55, 0.45, 1);
  padding: 0.341rem 0.682rem;
  background-color: rgb(35.5, 40.5, 42.95);
}
.sidebar-bluetooth-action-button label {
  color: #8ccff1;
}
.sidebar-bluetooth-action-button:hover, .sidebar-bluetooth-action-button:focus {
  background-color: rgb(54.65, 59.55, 62.155);
}
.sidebar-bluetooth-action-button:active {
  background-color: rgb(73.8, 78.6, 81.36);
}

.sidebar-bluetooth-bottombar {
  margin-top: 0.682rem;
}

.bluetooth-info-popup {
  padding: 1rem;
  background-color: rgb(35.5, 40.5, 42.95);
  min-width: 400px;
  min-height: 300px;
  border-radius: 1.159rem;
  -gtk-outline-radius: 1.159rem;
}

.bluetooth-info-title {
  font-weight: bold;
  font-size: 1.2rem;
  color: #8ccff1;
  margin-bottom: 0.5rem;
}

.bluetooth-info-content {
  background-color: #181d20;
  padding: 0.5rem;
  margin: 0.5rem 0;
  border-radius: 0.818rem;
  -gtk-outline-radius: 0.818rem;
  min-height: 200px;
}

.bluetooth-info-close-button {
  border-radius: 1.159rem;
  -gtk-outline-radius: 1.159rem;
  background-color: #8ccff1;
  color: #002c3b;
  padding: 0.5rem 1rem;
  margin-top: 0.5rem;
}

.bluetooth-rename-popup {
  padding: 1rem;
  background-color: rgb(35.5, 40.5, 42.95);
  min-width: 350px;
  border-radius: 1.159rem;
  -gtk-outline-radius: 1.159rem;
}

.bluetooth-rename-title {
  font-weight: bold;
  font-size: 1.2rem;
  color: #8ccff1;
  margin-bottom: 0.5rem;
}

.bluetooth-rename-content {
  background-color: #181d20;
  padding: 0.5rem;
  margin: 0.5rem 0;
  border-radius: 0.818rem;
  -gtk-outline-radius: 0.818rem;
}

.bluetooth-rename-buttons {
  margin-top: 0.5rem;
}

.bluetooth-rename-cancel-button {
  border-radius: 1.159rem;
  -gtk-outline-radius: 1.159rem;
  background-color: #292e31;
  color: #e3e7eb;
  padding: 0.5rem 1rem;
}

.bluetooth-rename-confirm-button {
  border-radius: 1.159rem;
  -gtk-outline-radius: 1.159rem;
  background-color: #8ccff1;
  color: #002c3b;
  padding: 0.5rem 1rem;
}

.bluetooth-empty-state {
  padding: 2rem;
  opacity: 0.8;
}

.bluetooth-empty-title {
  font-weight: bold;
  color: #8ccff1;
  font-size: 1.2rem;
  margin-top: 1rem;
}

.bluetooth-empty-subtitle {
  color: #c0c7cd;
  margin-bottom: 1rem;
}

.bluetooth-empty-scan-button {
  border-radius: 1.159rem;
  -gtk-outline-radius: 1.159rem;
  background-color: #8ccff1;
  color: #002c3b;
  padding: 0.5rem 1rem;
  margin-top: 1rem;
  transition: background-color 0.2s ease;
}

.bluetooth-section-header {
  padding: 0.5rem 0;
  margin-bottom: 0.5rem;
  border-bottom: 2px solid #8ccff1;
}

.bluetooth-section-title {
  font-weight: bold;
  color: #8ccff1;
  font-size: 1rem;
}

.bluetooth-device-section {
  margin-bottom: 1rem;
}

.bluetooth-no-devices {
  padding: 1rem;
  color: #c0c7cd;
  font-style: italic;
  opacity: 0.7;
}

.sidebar-wifinetworks-network {
  padding: 0.682rem;
  border-radius: 1.159rem;
  -gtk-outline-radius: 1.159rem;
  background-color: rgb(35.5, 40.5, 42.95);
  color: #e3e7eb;
}

.sidebar-wifinetworks-network:hover,
.sidebar-wifinetworks-network:focus {
  background-color: rgb(54.65, 59.55, 62.155);
}

.sidebar-wifinetworks-network:active {
  background-color: rgb(73.8, 78.6, 81.36);
}

.sidebar-wifinetworks-signal {
  -gtk-icon-theme: "Adwaita";
  font-size: 2.045rem;
}

.sidebar-wifinetworks-auth-entry {
  border-radius: 0.818rem;
  -gtk-outline-radius: 0.818rem;
  background-color: #181d20;
  color: #c0c7cd;
  padding: 0.682rem;
}

.sidebar-centermodules-bottombar-button {
  border-radius: 9999px;
  -gtk-outline-radius: 9999px;
  transition: 300ms cubic-bezier(0, 0.55, 0.45, 1);
  min-width: 6.818rem;
  min-height: 2.25rem;
  background-color: rgb(35.5, 40.5, 42.95);
  color: #e3e7eb;
}

.sidebar-centermodules-bottombar-button:hover,
.sidebar-centermodules-bottombar-button:focus {
  background-color: rgb(54.65, 59.55, 62.155);
}

.sidebar-centermodules-bottombar-button:active {
  background-color: rgb(73.8, 78.6, 81.36);
}

.sidebar-centermodules-scrollgradient-bottom {
  background: linear-gradient(to top, #181d20 0%, rgba(24, 29, 32, 0) 1.023rem);
}

.sidebar-scrollable {
  min-height: 100px;
}

.sidebar-chat-message {
  margin: 0.205rem 0.341rem;
}

.sidebar-chat-message-box {
  background-color: rgb(35.5, 40.5, 42.95);
  border-radius: 0.818rem;
  padding: 0.682rem;
}

.sidebar-chat-divider {
  margin: 0.341rem 0;
  border-top: 0.068rem solid #5e656a;
}

.sidebar-module-box {
  padding: 0.341rem 0.682rem;
}

.sidebar-prayertime-top {
  border-radius: 1.159rem;
  -gtk-outline-radius: 1.159rem;
  background: #0f1417;
  padding: 0.82rem;
  margin-bottom: 1rem;
  color: #ffffff;
}

.sidebar-prayertime-next {
  margin: 1.682rem 0;
  margin-top: 0.341rem;
  margin-right: 1.5rem;
  font-family: "Geist", "GeistMono", "Cascadia Code", "Segoe UI", "Roboto", sans-serif;
  color: #ffffff;
}

.sidebar-prayertime-item {
  border-radius: 0.818rem;
  -gtk-outline-radius: 0.818rem;
  font-family: "Geist", "GeistMono", "Cascadia Code", "Segoe UI", "Roboto", sans-serif;
  color: #ffffff;
  transition: 300ms cubic-bezier(0, 0.55, 0.45, 1);
  padding: 0.682rem;
  background-color: rgb(35.36, 42.84, 46.12);
}

.sidebar-prayertime-item:hover {
  background-color: rgb(41.4, 55.7, 63.35);
}

.sidebar-prayertime-item:active {
  background-color: rgb(58.8, 82.4, 94.7);
}

.sidebar-prayertime-name {
  font-family: "Geist", "GeistMono", "Cascadia Code", "Segoe UI", "Roboto", sans-serif;
  color: #e3e7eb;
  margin: 0.273rem 0;
  font-weight: 500;
}

.sidebar-prayertime-time {
  font-family: "Geist", "GeistMono", "Cascadia Code", "Segoe UI", "Roboto", sans-serif;
  color: #8ccff1;
  font-weight: bold;
  font-size: 1.1rem;
  min-width: 45px;
  margin: 0.273rem 0;
}

.sidebar-prayertimes {
  transition: 400ms cubic-bezier(0.1, 1, 0, 1);
  border: 0.3px solid rgba(180, 202, 214, 0);
  margin: 0.476rem;
  border-radius: 1.159rem;
  -gtk-outline-radius: 1.159rem;
  min-width: 29.659rem;
  padding: 1.023rem;
  margin: 0.273rem 0;
}

.sidebar-prayertimes-header {
  font-family: "Geist", "GeistMono", "Cascadia Code", "Segoe UI", "Roboto", sans-serif;
  font-size: 1.364rem;
  color: #ffffff;
}

.sidebar-prayertimes-header-icon {
  margin-right: 0.682rem;
  color: #ffffff;
}

.sidebar-prayertimes-time {
  font-family: "Geist", "GeistMono", "Cascadia Code", "Segoe UI", "Roboto", sans-serif;
  font-size: 1.023rem;
  color: rgb(220.6, 224.6, 226.8);
}

.sidebar-prayertimes-time-icon {
  margin-right: 0.682rem;
  color: rgb(220.6, 224.6, 226.8);
}

.sidebar-prayertimes-time-text {
  font-family: "Geist", "GeistMono", "Cascadia Code", "Segoe UI", "Roboto", sans-serif;
  font-size: 1.023rem;
  color: rgb(220.6, 224.6, 226.8);
}

.sidebar-prayertimes-time-remaining {
  font-family: "Geist", "GeistMono", "Cascadia Code", "Segoe UI", "Roboto", sans-serif;
  font-size: 0.818rem;
  color: rgb(186.2, 194.2, 198.6);
  margin: 0.273rem 0;
}

.sidebar-prayertimes-time-remaining-text {
  font-family: "Geist", "GeistMono", "Cascadia Code", "Segoe UI", "Roboto", sans-serif;
  font-size: 0.818rem;
  color: rgb(186.2, 194.2, 198.6);
  margin: 0.273rem 0;
}

.prayer-times-box {
  border-radius: 1.159rem;
  -gtk-outline-radius: 1.159rem;
  background-color: #181d20;
  padding: 0.682rem;
  margin: 0.341rem 0;
}

.prayer-times-header {
  font-family: "Geist", "GeistMono", "Cascadia Code", "Segoe UI", "Roboto", sans-serif;
  padding: 0.341rem;
  margin-bottom: 0.682rem;
  color: #e3e7eb;
}
.prayer-times-header label {
  font-size: 1.2rem;
}

.prayer-times-next {
  border-radius: 1.159rem;
  -gtk-outline-radius: 1.159rem;
  background: linear-gradient(135deg, #8ccff1 0%, rgb(126.8, 184.5, 213.85) 100%);
  color: #002c3b;
  padding: 1.2rem 0.682rem;
  margin-bottom: 0.682rem;
}
.prayer-times-next:hover {
  background: linear-gradient(135deg, #8ccff1 0%, rgb(118, 169.5, 195.75) 100%);
}

.prayer-times-item {
  border-radius: 1.159rem;
  -gtk-outline-radius: 1.159rem;
  padding: 1.2rem 0.682rem;
  background-color: #292e31;
  margin: 0.341rem 0;
  transition: all 200ms ease;
  border: 1px solid transparent;
}
.prayer-times-item:hover {
  background-color: #34393c;
  border-color: #5e656a;
}

.prayer-times-name {
  font-family: "Geist", "GeistMono", "Cascadia Code", "Segoe UI", "Roboto", sans-serif;
  color: #e3e7eb;
  font-size: 1.1rem;
}

.prayer-times-time {
  font-family: "Geist", "GeistMono", "Cascadia Code", "Segoe UI", "Roboto", sans-serif;
  color: #8ccff1;
  font-weight: bold;
  font-size: 1.1rem;
  min-width: 5.5rem;
}

.prayer-times-date {
  border-radius: 0.818rem;
  -gtk-outline-radius: 0.818rem;
  padding: 0.341rem 0.682rem;
  background: linear-gradient(to right, #292e31 0%, rgb(45.95, 54.05, 58.6) 100%);
  color: #c0c7cd;
  margin-bottom: 0.682rem;
  border-left: 3px solid #8ccff1;
}
.prayer-times-date label {
  font-size: 0.95rem;
}

.prayer-times-remaining {
  border-radius: 0.818rem;
  -gtk-outline-radius: 0.818rem;
  color: rgb(152.8, 157.15, 160.8);
  font-size: 0.85rem;
  margin-top: 0.2rem;
}

.prayer-times-icon {
  font-family: "Material Icons", "Material Symbols Rounded", "MaterialSymbolsRounded", "Material Symbols Outlined", "Material Icons Outlined", "Material Symbols Sharp";
  color: rgb(174, 178.25, 182);
  margin-right: 0.5rem;
}

.prayer-times-icon-next {
  font-family: "Material Icons", "Material Symbols Rounded", "MaterialSymbolsRounded", "Material Symbols Outlined", "Material Icons Outlined", "Material Symbols Sharp";
  color: #002c3b;
  margin-right: 0.5rem;
}

.sidebar-timer {
  border-radius: 1.159rem;
  -gtk-outline-radius: 1.159rem;
  font-family: "Geist", "GeistMono", "Cascadia Code", "Segoe UI", "Roboto", sans-serif;
  color: #e3e7eb;
}

.sidebar-timer-btn {
  border-radius: 1.159rem;
  -gtk-outline-radius: 1.159rem;
  padding: 0.2rem;
  min-width: 1.8rem;
  min-height: 1.8rem;
}

.sidebar-timer-btn-start {
  background-color: #8ccff1;
  color: #002c3b;
}

.sidebar-timer-delete {
  border-radius: 1.159rem;
  -gtk-outline-radius: 1.159rem;
  padding: 0.2rem;
  min-width: 1.8rem;
  min-height: 1.8rem;
  background-color: #ffb4ab;
  color: #580003;
}

.sidebar-timer-item {
  border-radius: 1.159rem;
  -gtk-outline-radius: 1.159rem;
  font-family: "Geist", "GeistMono", "Cascadia Code", "Segoe UI", "Roboto", sans-serif;
  color: #e3e7eb;
}

.sidebar-timer-icon {
  font-family: "Material Icons", "Material Symbols Rounded", "MaterialSymbolsRounded", "Material Symbols Outlined", "Material Icons Outlined", "Material Symbols Sharp";
}

.sidebar-todo-add {
  border-radius: 1.159rem;
  -gtk-outline-radius: 1.159rem;
  background-color: rgb(35.5, 40.5, 42.95);
  padding: 0.3em 0.5em;
  margin: 0 0.5em;
}

.sidebar-todo-add:focus {
  background-color: rgb(65.2, 79.5, 87.15);
}

.sidebar-todo-new {
  padding: 0.3em;
  margin: 0 0.5em;
}

.sidebar-todo-new:hover {
  background-color: rgb(65.2, 79.5, 87.15);
}

.sidebar-timer-presets {
  border-radius: 1.159rem;
  -gtk-outline-radius: 1.159rem;
  background-color: #181d20;
  padding: 0.5em;
  margin: 0 0.5em;
}
.sidebar-timer-presets .sidebar-timer-btn {
  margin: 0.2em;
  padding: 0.3em;
}

.quran-surah-name {
  font-family: "TE HAFS2 Tharwat Emara", "Noto Naskh Arabic", "Noto Sans Arabic", serif;
  font-size: 2.2rem;
  font-weight: bold;
  color: #8ccff1;
  margin: 1rem 0;
}

.quran-loading {
  opacity: 0.7;
  transition: opacity 0.3s ease;
}

.quran-message {
  background-color: #181d20;
  border-radius: 1.159rem;
  padding: 0.682rem;
  margin: 0.341rem 0;
}

.quran-message-container {
  padding: 1rem;
  background-color: #181d20;
  border-radius: 1.705rem;
  margin: 1rem;
}

.welcome-message {
  padding: 2rem;
}
.welcome-message .welcome-title {
  font-size: 2em;
  font-weight: bold;
  margin-bottom: 0.5rem;
  color: #e3e7eb;
}
.welcome-message .welcome-subtitle {
  opacity: 0.8;
  color: #c0c7cd;
}
.welcome-message .welcome-examples {
  margin: 1rem 0;
  min-width: 300px;
}
.welcome-message .welcome-examples .welcome-example-btn {
  border-radius: 1.705rem;
  background-color: #181d20;
  border: 1px solid #34393c;
  transition: all 0.2s ease;
  min-width: 300px;
  color: #e3e7eb;
}
.welcome-message .welcome-examples .welcome-example-btn:hover {
  background-color: #292e31;
  border-color: #8ccff1;
}
.welcome-message .capability-item {
  opacity: 0.8;
  color: #c0c7cd;
  margin: 0.25rem 0;
}

.sidebar-collapsed {
  min-width: 350px;
}

.sidebar-expanded {
  min-width: 500px;
}

.task-manager-widget {
  border-radius: 1.159rem;
  -gtk-outline-radius: 1.159rem;
  background-color: #181d20;
  padding: 0.682rem;
}

.task-manager-header {
  padding: 0.341rem;
  margin-bottom: 0.682rem;
}

.task-manager-box {
  padding: 0.341rem;
}

.task-manager-item {
  border-radius: 1.159rem;
  -gtk-outline-radius: 1.159rem;
  padding: 0.682rem;
  background-color: #292e31;
}
.task-manager-item:hover {
  background-color: #34393c;
}

.task-manager-button {
  border-radius: 9999px;
  -gtk-outline-radius: 9999px;
  padding: 0.341rem;
  background-color: transparent;
  color: #ffb4ab;
  transition: background-color 0.2s ease;
}
.task-manager-button:hover {
  background-color: rgba(255, 180, 171, 0.1);
}

.task-manager-refresh-button {
  border-radius: 9999px;
  -gtk-outline-radius: 9999px;
  padding: 0.341rem;
  background-color: transparent;
  color: #e3e7eb;
  transition: background-color 0.2s ease;
}
.task-manager-refresh-button:hover {
  background-color: #34393c;
}

.task-manager-scrollable {
  min-height: 20.455rem;
}

.audio-files-widget {
  padding: 0.6rem;
  background-color: #0f1417;
  border-radius: 0.9rem;
}

.media-header {
  margin: 0 0 0.6rem 0;
  padding: 0 0.4rem;
}

.media-header-title {
  font-weight: 500;
  font-size: 1rem;
  color: #e3e7eb;
}

.media-mode-button {
  min-height: 2rem;
  min-width: 2rem;
  border-radius: 2rem;
  background-color: #536772;
  color: #ffffff;
  margin-left: 8px;
  padding: 0;
}

.media-mode-button:hover {
  background-color: rgb(78.35, 96.1, 105.9);
}

.media-mode-button:active {
  background-color: rgb(73.7, 89.2, 97.8);
}

.empty-media-message {
  margin: 1rem 0;
}

.empty-media-icon {
  margin-bottom: 0.6rem;
  color: #90979c;
  opacity: 0.8;
  font-size: 24px;
}

.empty-media-title {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #e3e7eb;
}

.empty-media-subtitle {
  font-size: 0.85rem;
  opacity: 0.8;
  color: #c0c7cd;
}

.audio-files-scrollable {
  border-radius: 0.6rem;
  min-height: 100px;
}

.audio-files-list {
  padding: 0.5rem;
}

.audio-files-button {
  padding: 0.6rem;
  margin-bottom: 0.3rem;
  border-radius: 0.6rem;
  background-color: rgb(27.4, 32.4, 35.4);
}

.audio-files-button:hover {
  background-color: rgb(27.4, 32.4, 35.4);
}

.audio-files-button:active {
  background-color: rgb(27.4, 32.4, 35.4);
}

.audio-files-icon {
  color: #b4cad6;
  font-size: 22px;
  margin-right: 8px;
  min-width: 22px;
}

.audio-files-label {
  font-size: 1rem;
  color: #e3e7eb;
  min-width: 150px;
}

.audio-files-player-name {
  font-weight: 600;
  font-size: 1rem;
  color: #e3e7eb;
  min-width: 120px;
}

.audio-files-player-track {
  font-size: 0.9rem;
  opacity: 0.8;
  color: #c0c7cd;
  margin-top: 4px;
  min-width: 150px;
}

.audio-files-button-active {
  background-color: rgb(27.4, 32.4, 35.4);
  border-left: 3px solid #8ccff1;
}

.audio-files-button-active .audio-files-player-name {
  color: #b4cad6;
  font-weight: bold;
}

.audio-files-button-active .audio-files-player-track {
  color: #b4cad6;
  opacity: 0.9;
}

.audio-files-button-active .audio-files-icon {
  color: #b4cad6;
}

.audio-files-button-last-played {
  background-color: rgb(27.4, 32.4, 35.4);
  padding-left: 0.4rem;
}

.media-container {
  border-radius: 0.8rem;
  padding: 0.8rem;
  margin-top: 0.4rem;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-color: #181d20; /* Use onSecondary for background */
  min-height: 160px; /* Reduced height */
}

.media-controls-box {
  margin-bottom: 0.5rem;
}

.media-title-label {
  font-weight: 500;
  font-size: 0.95rem;
  margin-bottom: 0.5rem;
  padding: 0.5rem;
  color: #b4cad6; /* Use secondary color for text */
  min-width: 180px;
  background-color: #0f1417; /* Use theme variable instead of black */
  border-radius: 0.4rem;
}

.media-progress-container {
  margin-bottom: 0.4rem;
  padding: 0;
}

.media-progress-bar {
  min-height: 0.4rem;
  border-radius: 0.4rem;
  margin: 0 0.4rem 0.2rem;
}

.media-progress-bar highlight {
  background-color: #b4cad6;
  border-radius: 0.4rem;
}

.media-progress-bar trough {
  background-color: #34393c;
  border-radius: 0.4rem;
  min-height: 0.4rem;
}

.media-progress-bar slider {
  min-height: 0.9rem;
  min-width: 0.9rem;
  border-radius: 0.9rem;
  background-color: #b4cad6;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.media-time-label {
  font-size: 0.8rem;
  opacity: 0.9;
  font-family: monospace;
  margin-bottom: 0.2rem;
  color: #b4cad6; /* Use secondary color for text */
  padding: 0;
}

.media-bottom-container {
  margin-top: 0.5rem; /* Reduced space above bottom container */
  padding: 0;
  background-color: transparent;
}

.media-buttons-box {
  padding: 0.5rem 0;
  background-color: transparent;
}

.media-control-button {
  min-height: 2.1rem;
  min-width: 2.1rem;
  border-radius: 0.3rem; /* More square corners for M3 design */
  padding: 0;
  margin: 0 0.15rem;
  background-color: #8ccff1;
  color: #002c3b;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.media-control-button:hover {
  background-color: rgb(130.1, 190.9, 221.8);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.15);
}

.media-control-button:active {
  background-color: rgb(122.4, 177, 204.8);
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
}

.media-control-button-toggled {
  background-color: rgb(152, 205.5, 232.9);
  color: #002c3b;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.1);
}

.media-playpause-button {
  min-height: 2.1rem;
  min-width: 2.1rem;
  border-radius: 0.3rem; /* More square corners for M3 design */
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.material-welcome-logo {
  padding: 0.5rem;
  min-width: 4rem;
  margin-left: 0.135rem;
}

/* This is a duplicate rule that was causing issues */
.media-section-title {
  font-weight: bold;
  font-size: 1.1em;
  color: #c0c7cd;
  margin-bottom: 8px;
  padding-left: 5px;
}

.media-header {
  margin-bottom: 8px;
  padding: 4px;
}

.media-header-title {
  font-weight: 600;
  font-size: 1rem;
  color: #e3e7eb;
  margin: 4px 0;
}

.media-mode-button {
  border-radius: 9999px;
  -gtk-outline-radius: 9999px;
  min-width: 30px;
  min-height: 30px;
  padding: 6px;
  background-color: #8ccff1;
  color: #002c3b;
  margin-left: 8px;
}
.media-mode-button:hover {
  background-color: rgb(110.3, 158.7, 183.4);
}

.audio-files-player-name {
  font-weight: 600;
  font-size: 0.95rem;
  color: #e3e7eb;
}

.audio-files-player-track {
  font-size: 0.85rem;
  color: #c0c7cd;
  margin-top: 2px;
}

.audio-files-button-active {
  background-color: rgb(27.4, 32.4, 35.4);
  border-left: 3px solid #8ccff1;
}
.audio-files-button-active .audio-files-player-name {
  color: #b4cad6;
  font-weight: bold;
}
.audio-files-button-active .audio-files-player-track {
  color: #b4cad6;
  opacity: 0.9;
}
.audio-files-button-active .audio-files-icon {
  color: #b4cad6;
}

/* This is a duplicate rule that was causing issues */
.session-bg {
  background-color: rgba(15, 20, 23, 0.6);
}

.session-button {
  border-radius: 1.705rem;
  -gtk-outline-radius: 1.705rem;
  min-width: 8.182rem;
  min-height: 8.182rem;
  background-color: #181d20;
  color: #c0c7cd;
  font-size: 3rem;
}

.session-button-focused {
  background-color: rgb(49.2, 54.5, 57.95);
}

.session-button-desc {
  background-color: rgb(35.5, 40.5, 42.95);
  color: #e3e7eb;
  border-bottom-left-radius: 1.705rem;
  border-bottom-right-radius: 1.705rem;
  padding: 0.205rem 0.341rem;
  font-weight: 700;
}

.session-button-cancel {
  border-radius: 1.705rem;
  -gtk-outline-radius: 1.705rem;
  min-width: 8.182rem;
  min-height: 5.455rem;
  background-color: #181d20;
  color: #c0c7cd;
  font-size: 3rem;
}

.session-color-1 {
  color: #c0c7cd;
}

.session-color-2 {
  color: #c0c7cd;
}

.session-color-3 {
  color: #c0c7cd;
}

.session-color-4 {
  color: #c0c7cd;
}

.session-color-5 {
  color: #c0c7cd;
}

.session-color-6 {
  color: #c0c7cd;
}

.session-color-7 {
  color: #c0c7cd;
}

.notif-low {
  border-radius: 1.159rem;
  -gtk-outline-radius: 1.159rem;
  background-color: rgb(35.5, 40.5, 42.95);
  color: #e3e7eb;
  padding: 0.818rem;
  padding-right: 1.363rem;
}

.notif-normal {
  border-radius: 1.159rem;
  -gtk-outline-radius: 1.159rem;
  background-color: rgb(35.5, 40.5, 42.95);
  color: #e3e7eb;
  padding: 0.818rem;
  padding-right: 1.363rem;
}

.notif-critical {
  border-radius: 1.159rem;
  -gtk-outline-radius: 1.159rem;
  background-color: #536772;
  color: #ffffff;
  padding: 0.818rem;
  padding-right: 1.363rem;
}

.notif-clicked-low {
  background-color: rgb(49.2, 54.5, 57.95);
}

.notif-clicked-normal {
  background-color: rgb(49.2, 54.5, 57.95);
}

.notif-clicked-critical {
  background-color: #162a34;
  color: #ffffff;
}

.popup-notif-low {
  border-radius: 1.159rem;
  -gtk-outline-radius: 1.159rem;
  min-width: 30.682rem;
  background-color: rgb(35.5, 40.5, 42.95);
  border: 0.034rem solid #5e656a;
  color: #e3e7eb;
  padding: 0.818rem;
  padding-right: 1.363rem;
}

.popup-notif-normal {
  border-radius: 1.159rem;
  -gtk-outline-radius: 1.159rem;
  min-width: 30.682rem;
  background-color: rgb(35.5, 40.5, 42.95);
  border: 0.034rem solid #5e656a;
  color: #e3e7eb;
  padding: 0.818rem;
  padding-right: 1.363rem;
}

.popup-notif-critical {
  border-radius: 1.159rem;
  -gtk-outline-radius: 1.159rem;
  min-width: 30.682rem;
  background-color: #536772;
  border: 0.034rem solid #ffffff;
  color: #ffffff;
  padding: 0.818rem;
  padding-right: 1.363rem;
}

.popup-notif-clicked-low {
  background-color: #181d20;
}

.popup-notif-clicked-normal {
  background-color: #181d20;
}

.popup-notif-clicked-critical {
  background-color: #162a34;
  color: #ffffff;
}

.notif-body-low {
  color: #90979c;
}

.notif-body-normal {
  color: #90979c;
}

.notif-body-critical {
  color: rgb(198.24, 204.84, 208.47);
}

.notif-icon {
  border-radius: 9999px;
  -gtk-outline-radius: 9999px;
  min-width: 3.409rem;
  min-height: 3.409rem;
  font-size: 2.182rem;
}

.notif-icon-material {
  background-color: #536772;
  color: #ffffff;
}

.notif-icon-material-low {
  background-color: #536772;
  color: #ffffff;
}

.notif-icon-material-normal {
  background-color: #536772;
  color: #ffffff;
}

.notif-icon-material-critical {
  background-color: #b4cad6;
  color: #162a34;
}

.notif-expand-btn {
  border-radius: 1.159rem;
  -gtk-outline-radius: 1.159rem;
  min-width: 1.841rem;
  min-height: 1.841rem;
}

.notif-expand-btn:hover,
.notif-expand-btn:focus {
  background: rgb(54.65, 59.55, 62.155);
}

.notif-expand-btn:active {
  background: rgb(73.8, 78.6, 81.36);
}

.notif-listaction-btn {
  border-radius: 1.159rem;
  -gtk-outline-radius: 1.159rem;
  padding: 0.341rem 0.682rem;
}

.notif-listaction-btn:hover,
.notif-listaction-btn:focus {
  background-color: rgb(54.65, 59.55, 62.155);
}

.notif-listaction-btn:active {
  background-color: rgb(73.8, 78.6, 81.36);
}

.notif-listaction-btn-enabled {
  background-color: #536772;
  color: #ffffff;
}

.notif-listaction-btn-enabled:hover,
.notif-listaction-btn-enabled:focus {
  background-color: rgb(100.2, 118.2, 128.1);
}

.notif-listaction-btn-enabled:active {
  background-color: rgb(126, 141, 149.25);
}

.osd-notif {
  border-radius: 1.159rem;
  -gtk-outline-radius: 1.159rem;
  background-color: #0f1417;
  min-width: 30.682rem;
}

.notif-circprog-low {
  transition: 0ms linear;
  min-width: 0.136rem;
  min-height: 3.136rem;
  padding: 0rem;
  color: #ffffff;
}

.notif-circprog-normal {
  transition: 0ms linear;
  min-width: 0.136rem;
  min-height: 3.136rem;
  padding: 0rem;
  color: #ffffff;
}

.notif-circprog-critical {
  transition: 0ms linear;
  min-width: 0.136rem;
  min-height: 3.136rem;
  padding: 0rem;
  color: #536772;
}

.notif-actions {
  min-height: 2.045rem;
}

.notif-action {
  border-radius: 0.818rem;
  -gtk-outline-radius: 0.818rem;
}

.notif-action-low {
  background-color: #34393c;
  color: #e3e7eb;
}

.notif-action-low:focus,
.notif-action-low:hover {
  border: 0.04rem solid #5e656a;
}

.notif-action-low:active {
  background-color: #393e41;
}

.notif-action-normal {
  background-color: #34393c;
  color: #e3e7eb;
}

.notif-action-normal:focus,
.notif-action-normal:hover {
  border: 0.04rem solid #5e656a;
}

.notif-action-normal:active {
  background-color: #393e41;
}

.notif-action-critical {
  background-color: rgb(69.4, 90, 100.6);
  color: #c0c7cd;
}

.notif-action-critical:focus,
.notif-action-critical:hover {
  border: 0.04rem solid #90979c;
}

.notif-action-critical:active {
  background-color: rgb(85.2, 106, 116.8);
}

.wallselect-bg {
  background-color: #0f1417;
  border-bottom: 0.5px solid rgba(180, 202, 214, 0);
  padding: 1rem 0rem 1.2rem;
}

.colorpicker {
  background-color: #0f1417;
  padding: 0.2rem 1.5rem;
}

.pick-rounding {
  border-radius: 0 0 1.705rem 1.705rem;
}

.wall-rounding {
  border-radius: 0.818rem 0.818rem 0 0;
}

.wallpaper-list .preview-box {
  min-width: 164px;
  min-height: 102.5px;
  background-size: cover;
  background-position: center;
  border-radius: 0.818rem;
}
.wallpaper-list button {
  margin: 5px;
}

.wallselect-content {
  background-color: #181d20;
  padding: 0.5rem 0.5rem;
  margin: 0 0.952rem;
  border-radius: 1.159rem;
}

.corner-wallselect {
  border-radius: 1.705rem;
  -gtk-outline-radius: 1.705rem;
  background-color: #0f1417;
}

.wallpaper-placeholder {
  padding: 2rem;
  font-family: "Geist", "GeistMono", "Cascadia Code", "Segoe UI", "Roboto", sans-serif;
  color: #c0c7cd;
}

.generate-thumbnails {
  padding: 0.5rem 1rem;
  border-radius: 9999px;
  background-color: #536772;
  color: #ffffff;
  margin-right: 0.5rem;
}
.generate-thumbnails:hover {
  background-color: rgb(100.2, 118.2, 128.1);
}
.generate-thumbnails:active {
  background-color: rgb(117.4, 133.4, 142.2);
}

.material-pagination-container {
  margin: 8px 0;
  padding: 4px 8px;
  border-radius: 1.159rem;
  background-color: rgba(24, 29, 32, 0.8);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.wallpaper-pagination-btn {
  padding: 4px;
  border-radius: 50%;
  background-color: rgba(8, 12, 15, 0.7);
  color: #e3e7eb;
  margin: 0 2px;
  min-width: 32px;
  min-height: 32px;
  transition: background-color 200ms ease, box-shadow 200ms ease;
}
.wallpaper-pagination-btn label {
  font-size: 18px;
}
.wallpaper-pagination-btn:hover {
  background-color: rgb(55.85, 70.15, 77.8);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}
.wallpaper-pagination-btn:active {
  background-color: rgb(41.9, 117.9, 147.4);
  color: #ffffff;
}

.wallpaper-pagination-counter {
  font-size: 1rem;
  font-weight: 500;
  color: #c0c7cd;
  margin: 0 8px;
  min-width: 45px;
  /* Use xalign instead of text-align for GTK */
  /* text-align is not supported in GTK CSS */
}

.osd-music {
  transition: 400ms cubic-bezier(0.1, 1, 0, 1);
  background-color: #0f1417;
  border: 0.2px solid rgba(180, 202, 214, 0);
  padding: 1.423rem 1.423rem;
  min-width: 600px;
}

.osd-music-round {
  border-radius: 1.705rem 1.705rem 0 0;
}

.normal-music {
  background-color: #0f1417;
}

.amberoled {
  background: linear-gradient(#0f1417, #0f1417);
}

.corner-amberoled {
  background-color: #0f1417;
}

.osd-music-cover {
  transition: 400ms cubic-bezier(0.1, 1, 0, 1);
  margin: 1rem;
  margin-top: 1rem;
  min-width: 150px;
  min-height: 150px;
}
.osd-music-cover .osd-music-cover-art {
  border-radius: 16px;
  margin: 0.4rem;
}

.elevate-music {
  border-radius: 1.705rem;
}

.osd-music-info {
  margin: 0.5rem 0;
}

.osd-music-title {
  transition: 300ms cubic-bezier(0, 0.55, 0.45, 1);
  font-family: "Geist", "GeistMono", "Cascadia Code", "Segoe UI", "Roboto", sans-serif;
  font-size: 2.5rem;
  color: #ffffff;
}

.osd-music-artists {
  transition: 300ms cubic-bezier(0, 0.55, 0.45, 1);
  font-family: "Geist", "GeistMono", "Cascadia Code", "Segoe UI", "Roboto", sans-serif;
  font-size: 1.5rem;
  color: rgba(239.0923076923, 239.8, 240.1846153846, 0.9);
}

.osd-music-pill {
  transition: 300ms cubic-bezier(0, 0.55, 0.45, 1);
  border-radius: 9999px;
  -gtk-outline-radius: 9999px;
  font-family: "Geist", "GeistMono", "Cascadia Code", "Segoe UI", "Roboto", sans-serif;
  min-width: 2.833rem;
  padding: 0.273rem 0.682rem;
  background-color: rgba(48.2, 57.4, 62.4, 0.5);
  color: #ffffff;
}

.osd-music-controls {
  transition: 300ms cubic-bezier(0, 0.55, 0.45, 1);
  border-radius: 9999px;
  -gtk-outline-radius: 9999px;
  font-family: "Geist", "GeistMono", "Cascadia Code", "Segoe UI", "Roboto", sans-serif;
  min-width: 2.833rem;
  padding: 0.3rem;
  background-color: rgba(48.2, 57.4, 62.4, 0.5);
  color: #ffffff;
  border: 1px solid rgba(180, 202, 214, 0);
}

.osd-music-controlbtn {
  transition: 400ms cubic-bezier(0.1, 1, 0, 1);
  min-width: 2.3rem;
  min-height: 2.3rem;
  border-radius: 1rem;
  margin: 0 0.1rem;
  transition: all 200ms cubic-bezier(0.05, 0.7, 0.1, 1);
  background-color: rgba(48.2, 57.4, 62.4, 0);
}

.osd-music-controlbtn:hover,
.osd-music-controlbtn:focus {
  background-color: rgba(79.9769230769, 109.1846153846, 124.2230769231, 0.575);
  margin-top: -1px;
  margin-bottom: 1px;
}

.osd-music-controlbtn:active {
  background-color: rgba(94.1, 132.2, 151.7, 0.625);
  margin-top: 0px;
  margin-bottom: 0px;
}

.osd-music-controlbtn.active {
  background-color: rgba(99.8375, 141.55, 162.8625, 0.65);
  color: #002c3b;
}

.osd-music-player-btn {
  min-width: 6rem;
  padding: 0 0.2rem;
  margin: 0 0.3rem;
  border-radius: 1rem;
  background-color: rgba(76.4, 84.3454545455, 88.6636363636, 0.525);
  border: 1px solid rgba(180, 202, 214, 0);
}

.osd-music-player-btn:hover {
  background-color: rgba(71.15, 94.8, 107.05, 0.55);
}

.osd-music-player-indicator {
  transition: opacity 200ms cubic-bezier(0.05, 0.7, 0.1, 1);
}

.osd-music-player-name {
  font-size: 0.9rem;
  font-weight: 600;
  color: #ffffff;
}

.osd-music-controlbtn-txt {
  transition: 300ms cubic-bezier(0, 0.55, 0.45, 1);
  transition: 100ms cubic-bezier(0.05, 0.7, 0.1, 1);
  font-family: "Material Icons", "Material Symbols Rounded", "MaterialSymbolsRounded", "Material Symbols Outlined", "Material Icons Outlined", "Material Symbols Sharp";
  font-size: 1.4rem;
  margin: 0;
}

.osd-music-circprog {
  transition: 1000ms cubic-bezier(0.1, 1, 0, 1);
  min-width: 0.609rem;
  min-height: 4.068rem;
  padding: 0.273rem;
  color: #ffffff;
}

.osd-music-playstate {
  transition: 400ms cubic-bezier(0.1, 1, 0, 1);
  min-height: 3.5rem;
  min-width: 3.5rem;
  border-radius: 100px;
  background-color: rgba(48.2, 57.4, 62.4, 0.5);
  color: #ffffff;
}

.osd-music-playstate-btn > label {
  transition: 50ms cubic-bezier(0.05, 0.7, 0.1, 1);
  font-family: "Material Icons", "Material Symbols Rounded", "MaterialSymbolsRounded", "Material Symbols Outlined", "Material Icons Outlined", "Material Symbols Sharp";
  font-size: 2.364rem;
  margin: -0.1rem 0rem;
}

.cava-container {
  min-height: 140px;
  padding: 5px;
  border-radius: 12px;
}
.cava-container .cava-visualizer {
  font-family: techfont;
  font-size: 24px;
  color: #8ccff1;
  border-radius: 12px;
  background-color: rgba(48.2, 57.4, 62.4, 0.5);
}
.cava-container .cava-visualizer .cava-bar {
  background-color: rgb(130.5, 147.4, 156.7);
  border-radius: 4px;
  transition: all 80ms cubic-bezier(0.4, 0, 0.2, 1);
}
.cava-container .cava-visualizer .cava-bar.cava-bar-low {
  background-color: rgb(130.5, 147.4, 156.7);
}
.cava-container .cava-visualizer .cava-bar.cava-bar-med {
  background-color: rgb(115, 169.6, 197.4);
}
.cava-container .cava-visualizer .cava-bar.cava-bar-high {
  background-color: rgb(133.75, 197.65, 230.1);
}
.cava-container .cava-visualizer .cava-bar-up {
  border-radius: 4px 4px 0 0;
}
.cava-container .cava-visualizer .cava-bar-down {
  border-radius: 0 0 4px 4px;
}

.music-mode-controls {
  transition: 300ms cubic-bezier(0, 0.55, 0.45, 1);
  padding: 0.5rem;
  margin-bottom: 1rem;
  border-radius: 1.159rem;
  background-color: rgba(48.2, 57.4, 62.4, 0.2);
}

.music-mode-button {
  transition: 400ms cubic-bezier(0.1, 1, 0, 1);
  border-radius: 9999px;
  -gtk-outline-radius: 9999px;
  min-width: 1.6rem;
  min-height: 1.6rem;
  background-color: rgba(48.2, 57.4, 62.4, 0.5);
  color: #ffffff;
}
.music-mode-button:hover {
  background-color: rgba(99.9, 106.8, 110.55, 0.55);
}
.music-mode-button:active {
  background-color: rgba(119.7846153846, 125.8, 129.0692307692, 0.575);
}

.music-mode-indicator {
  font-family: "Geist", "GeistMono", "Cascadia Code", "Segoe UI", "Roboto", sans-serif;
  color: #ffffff;
  font-size: 1.2rem;
  margin: 0.4rem 0.1rem;
}

.mpris-player-button {
  transition: 300ms cubic-bezier(0, 0.55, 0.45, 1);
  border-radius: 1.159rem;
  padding: 0.6rem 0.8rem;
  margin: 0.2rem 0;
  background-color: rgba(48.2, 57.4, 62.4, 0.5);
}
.mpris-player-button:hover {
  background-color: rgba(99.9, 106.8, 110.55, 0.55);
}
.mpris-player-button:active {
  background-color: rgba(119.7846153846, 125.8, 129.0692307692, 0.575);
}
.mpris-player-button.mpris-player-button-active {
  background-color: rgba(99.8375, 141.55, 162.8625, 0.65);
  border-left: 3px solid #8ccff1;
}

.player-name-label {
  font-family: "Geist", "GeistMono", "Cascadia Code", "Segoe UI", "Roboto", sans-serif;
  color: #ffffff;
  font-size: 1.1rem;
}

.player-track-label {
  font-family: "Geist", "GeistMono", "Cascadia Code", "Segoe UI", "Roboto", sans-serif;
  color: rgba(239.0923076923, 239.8, 240.1846153846, 0.9);
  font-size: 0.9rem;
}

.empty-music-message {
  padding: 2rem;
}
.empty-music-message .empty-music-icon {
  color: rgba(217.4, 219.0727272727, 219.9818181818, 0.8);
  margin-bottom: 1rem;
}
.empty-music-message .empty-music-title {
  font-family: "Geist", "GeistMono", "Cascadia Code", "Segoe UI", "Roboto", sans-serif;
  color: #ffffff;
  font-size: 1.4rem;
  margin-bottom: 0.5rem;
}
.empty-music-message .empty-music-subtitle {
  font-family: "Geist", "GeistMono", "Cascadia Code", "Segoe UI", "Roboto", sans-serif;
  color: rgba(239.0923076923, 239.8, 240.1846153846, 0.9);
  font-size: 1rem;
}

.osd-music-volume {
  margin: 5px 0;
  padding: 5px;
  border-radius: 8px;
}

.osd-music-volume-slider {
  margin: 0 5px;
}
.osd-music-volume-slider trough {
  background-color: rgba(119.7846153846, 125.8, 129.0692307692, 0.575);
  border-radius: 100px;
  min-height: 6px;
}
.osd-music-volume-slider highlight {
  background-color: #8ccff1;
  border-radius: 100px;
}
.osd-music-volume-slider slider {
  background-color: #8ccff1;
  border-radius: 50%;
  min-width: 16px;
  min-height: 16px;
  margin: -5px;
}

.osd-music-player-menu {
  background-color: #0f1417;
  border: 1px solid rgba(180, 202, 214, 0);
  border-radius: 1.159rem;
  padding: 4px;
}
.osd-music-player-menu menuitem {
  padding: 6px 8px;
  border-radius: 0.818rem;
}
.osd-music-player-menu menuitem:hover {
  background-color: rgba(48.2, 57.4, 62.4, 0.5);
}

.auva-clock {
  font-family: "Geist", "GeistMono", "Cascadia Code", "Segoe UI", "Roboto", sans-serif;
  font-size: 1.8rem;
  color: #002c3b;
}

.auva-date {
  font-family: "Geist", "GeistMono", "Cascadia Code", "Segoe UI", "Roboto", sans-serif;
  font-size: 1.4rem;
  color: #002c3b;
}

.auva-day {
  font-family: "steelfish";
  font-size: 4rem;
}

.auva-day-color {
  font-family: "steelfish";
  color: #8ccff1;
  font-size: 4rem;
}

.auva-weather {
  font-family: "Geist", "GeistMono", "Cascadia Code", "Segoe UI", "Roboto", sans-serif;
  font-size: 1.3rem;
  font-weight: 300;
}

.auva-greeting {
  font-family: "Big John";
  font-size: 3rem;
}

.auva-circprog-main {
  transition: 1000ms cubic-bezier(0.1, 1, 0, 1);
  min-width: 0.705rem;
  min-height: 4.7rem;
  padding: 0rem;
  background: linear-gradient(to right, #8ccff1 0%, #0f1417 100%);
}

.auva-resource-container {
  min-height: 4.7rem;
  min-width: 4.7rem;
}

.auva-circprog-main + .icon-material {
  margin: 0;
  padding: 0;
  font-size: 1.8182rem;
  margin-top: 0px;
  margin-left: 0px;
}

.auva-clock-box {
  border-radius: 9999px;
  -gtk-outline-radius: 9999px;
  padding: 0.2rem 1.5rem;
  background-color: #8ccff1;
}

.growingRadial {
  transition: 300ms cubic-bezier(0.2, 0, 0, 1);
}

.fadingRadial {
  transition: 50ms cubic-bezier(0.2, 0, 0, 1);
}

.sidebar-pinned {
  font-size: medium;
  margin-right: 0rem;
  min-width: 400px;
  border: 0 solid #000;
}

/*# sourceMappingURL=style.css.map */
