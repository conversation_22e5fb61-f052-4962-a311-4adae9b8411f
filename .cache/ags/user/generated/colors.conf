
$background = rgb(0f1417)

$error = rgb(ffb4ab)

$error_container = rgb(c32220)

$inverse_on_surface = rgb(2c3134)

$inverse_primary = rgb(045e7c)

$inverse_surface = rgb(dfe3e7)

$on_background = rgb(dfe3e7)

$on_error = rgb(580003)

$on_error_container = rgb(ffffff)

$on_primary = rgb(002c3b)

$on_primary_container = rgb(ffffff)

$on_primary_fixed = rgb(001f2b)

$on_primary_fixed_variant = rgb(004c64)

$on_secondary = rgb(162a34)

$on_secondary_container = rgb(ffffff)

$on_secondary_fixed = rgb(081e27)

$on_secondary_fixed_variant = rgb(354853)

$on_surface = rgb(e3e7eb)

$on_surface_variant = rgb(c0c7cd)

$on_tertiary = rgb(272443)

$on_tertiary_container = rgb(ffffff)

$on_tertiary_fixed = rgb(1b1736)

$on_tertiary_fixed_variant = rgb(454263)

$outline = rgb(90979c)

$outline_variant = rgb(5e656a)

$primary = rgb(8ccff1)

$primary_container = rgb(1f6c89)

$primary_fixed = rgb(bfe8ff)

$primary_fixed_dim = rgb(8ccff1)

$scrim = rgb(000000)

$secondary = rgb(b4cad6)

$secondary_container = rgb(536772)

$secondary_fixed = rgb(d0e6f3)

$secondary_fixed_dim = rgb(b4cad6)

$shadow = rgb(000000)

$source_color = rgb(1b485b)

$surface = rgb(0f1417)

$surface_bright = rgb(393e41)

$surface_container = rgb(1f2426)

$surface_container_high = rgb(292e31)

$surface_container_highest = rgb(34393c)

$surface_container_low = rgb(181d20)

$surface_container_lowest = rgb(080c0f)

$surface_dim = rgb(0f1417)

$surface_tint = rgb(8ccff1)

$surface_variant = rgb(40484c)

$tertiary = rgb(c7c2ea)

$tertiary_container = rgb(646083)

$tertiary_fixed = rgb(e4dfff)

$tertiary_fixed_dim = rgb(c7c2ea)

