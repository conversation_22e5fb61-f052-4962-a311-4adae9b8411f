{"name": "HyprLuna", "type": "dark", "semanticHighlighting": true, "semanticTokenColors": {"enumMember": {"foreground": "#bfc2ff"}, "variable.constant": {"foreground": "#ffaaf7"}, "variable.defaultLibrary": {"foreground": "#c1c3f3"}}, "tokenColors": [{"name": "unison punctuation", "scope": "punctuation.definition.delayed.unison,punctuation.definition.list.begin.unison,punctuation.definition.list.end.unison,punctuation.definition.ability.begin.unison,punctuation.definition.ability.end.unison,punctuation.operator.assignment.as.unison,punctuation.separator.pipe.unison,punctuation.separator.delimiter.unison,punctuation.definition.hash.unison", "settings": {"foreground": "#ffb4ab"}}, {"name": "haskell variable generic-type", "scope": "variable.other.generic-type.haskell", "settings": {"foreground": "#bfc2ff"}}, {"name": "haskell storage type", "scope": "storage.type.haskell", "settings": {"foreground": "#ffaaf7"}}, {"name": "support.variable.magic.python", "scope": "support.variable.magic.python", "settings": {"foreground": "#ffb4ab"}}, {"name": "punctuation.separator.parameters.python", "scope": "punctuation.separator.period.python,punctuation.separator.element.python,punctuation.parenthesis.begin.python,punctuation.parenthesis.end.python", "settings": {"foreground": "#e4e1eb"}}, {"name": "variable.parameter.function.language.special.self.python", "scope": "variable.parameter.function.language.special.self.python", "settings": {"foreground": "#c1c3f3"}}, {"name": "storage.modifier.lifetime.rust", "scope": "storage.modifier.lifetime.rust", "settings": {"foreground": "#e4e1eb"}}, {"name": "support.function.std.rust", "scope": "support.function.std.rust", "settings": {"foreground": "#bfc2ff"}}, {"name": "entity.name.lifetime.rust", "scope": "entity.name.lifetime.rust", "settings": {"foreground": "#c1c3f3"}}, {"name": "variable.language.rust", "scope": "variable.language.rust", "settings": {"foreground": "#ffb4ab"}}, {"name": "support.constant.edge", "scope": "support.constant.edge", "settings": {"foreground": "#bfc2ff"}}, {"name": "regexp constant character-class", "scope": "constant.other.character-class.regexp", "settings": {"foreground": "#ffb4ab"}}, {"name": "regexp operator.quantifier", "scope": "keyword.operator.quantifier.regexp", "settings": {"foreground": "#ffaaf7"}}, {"name": "punctuation.definition", "scope": "punctuation.definition.string.begin,punctuation.definition.string.end", "settings": {"foreground": "#c1c3f3"}}, {"name": "Text", "scope": "variable.parameter.function", "settings": {"foreground": "#e4e1eb"}}, {"name": "Comment Markup Link", "scope": "comment markup.link", "settings": {"foreground": "#9695a4"}}, {"name": "markup diff", "scope": "markup.changed.diff", "settings": {"foreground": "#c1c3f3"}}, {"name": "diff", "scope": "meta.diff.header.from-file,meta.diff.header.to-file,punctuation.definition.from-file.diff,punctuation.definition.to-file.diff", "settings": {"foreground": "#bfc2ff"}}, {"name": "inserted.diff", "scope": "markup.inserted.diff", "settings": {"foreground": "#c1c3f3"}}, {"name": "deleted.diff", "scope": "markup.deleted.diff", "settings": {"foreground": "#ffb4ab"}}, {"name": "c++ function", "scope": "meta.function.c,meta.function.cpp", "settings": {"foreground": "#ffb4ab"}}, {"name": "c++ block", "scope": "punctuation.section.block.begin.bracket.curly.cpp,punctuation.section.block.end.bracket.curly.cpp,punctuation.terminator.statement.c,punctuation.section.block.begin.bracket.curly.c,punctuation.section.block.end.bracket.curly.c,punctuation.section.parens.begin.bracket.round.c,punctuation.section.parens.end.bracket.round.c,punctuation.section.parameters.begin.bracket.round.c,punctuation.section.parameters.end.bracket.round.c", "settings": {"foreground": "#e4e1eb"}}, {"name": "js/ts punctuation separator key-value", "scope": "punctuation.separator.key-value", "settings": {"foreground": "#e4e1eb"}}, {"name": "js/ts import keyword", "scope": "keyword.operator.expression.import", "settings": {"foreground": "#bfc2ff"}}, {"name": "math js/ts", "scope": "support.constant.math", "settings": {"foreground": "#c1c3f3"}}, {"name": "math property js/ts", "scope": "support.constant.property.math", "settings": {"foreground": "#ffaaf7"}}, {"name": "js/ts variable.other.constant", "scope": "variable.other.constant", "settings": {"foreground": "#c1c3f3"}}, {"name": "java type", "scope": ["storage.type.annotation.java", "storage.type.object.array.java"], "settings": {"foreground": "#c1c3f3"}}, {"name": "java source", "scope": "source.java", "settings": {"foreground": "#ffb4ab"}}, {"name": "java modifier.import", "scope": "punctuation.section.block.begin.java,punctuation.section.block.end.java,punctuation.definition.method-parameters.begin.java,punctuation.definition.method-parameters.end.java,meta.method.identifier.java,punctuation.section.method.begin.java,punctuation.section.method.end.java,punctuation.terminator.java,punctuation.section.class.begin.java,punctuation.section.class.end.java,punctuation.section.inner-class.begin.java,punctuation.section.inner-class.end.java,meta.method-call.java,punctuation.section.class.begin.bracket.curly.java,punctuation.section.class.end.bracket.curly.java,punctuation.section.method.begin.bracket.curly.java,punctuation.section.method.end.bracket.curly.java,punctuation.separator.period.java,punctuation.bracket.angle.java,punctuation.definition.annotation.java,meta.method.body.java", "settings": {"foreground": "#e4e1eb"}}, {"name": "java modifier.import", "scope": "meta.method.java", "settings": {"foreground": "#bfc2ff"}}, {"name": "java modifier.import", "scope": "storage.modifier.import.java,storage.type.java,storage.type.generic.java", "settings": {"foreground": "#c1c3f3"}}, {"name": "java instanceof", "scope": "keyword.operator.instanceof.java", "settings": {"foreground": "#bfc2ff"}}, {"name": "java variable.name", "scope": "meta.definition.variable.name.java", "settings": {"foreground": "#ffb4ab"}}, {"name": "operator logical", "scope": "keyword.operator.logical", "settings": {"foreground": "#bfc2ff"}}, {"name": "operator bitwise", "scope": "keyword.operator.bitwise", "settings": {"foreground": "#bfc2ff"}}, {"name": "operator channel", "scope": "keyword.operator.channel", "settings": {"foreground": "#bfc2ff"}}, {"name": "support.constant.property-value.scss", "scope": "support.constant.property-value.scss,support.constant.property-value.css", "settings": {"foreground": "#ffaaf7"}}, {"name": "CSS/SCSS/LESS Operators", "scope": "keyword.operator.css,keyword.operator.scss,keyword.operator.less", "settings": {"foreground": "#bfc2ff"}}, {"name": "keyword.operator", "scope": "keyword.operator.arithmetic,keyword.operator.comparison,keyword.operator.decrement,keyword.operator.increment,keyword.operator.relational", "settings": {"foreground": "#bfc2ff"}}, {"name": "C operator assignment", "scope": "keyword.operator.assignment.c,keyword.operator.comparison.c,keyword.operator.c,keyword.operator.increment.c,keyword.operator.decrement.c,keyword.operator.bitwise.shift.c,keyword.operator.assignment.cpp,keyword.operator.comparison.cpp,keyword.operator.cpp,keyword.operator.increment.cpp,keyword.operator.decrement.cpp,keyword.operator.bitwise.shift.cpp", "settings": {"foreground": "#bfc2ff"}}, {"name": "Punctuation", "scope": "punctuation.separator.delimiter", "settings": {"foreground": "#e4e1eb"}}, {"name": "Other punctuation .c", "scope": "punctuation.separator.c,punctuation.separator.cpp", "settings": {"foreground": "#bfc2ff"}}, {"name": "C type posix-reserved", "scope": "support.type.posix-reserved.c,support.type.posix-reserved.cpp", "settings": {"foreground": "#bfc2ff"}}, {"name": "keyword.operator.sizeof.c", "scope": "keyword.operator.sizeof.c,keyword.operator.sizeof.cpp", "settings": {"foreground": "#bfc2ff"}}, {"name": "python parameter", "scope": "variable.parameter.function.language.python", "settings": {"foreground": "#ffaaf7"}}, {"name": "python type", "scope": "support.type.python", "settings": {"foreground": "#bfc2ff"}}, {"name": "python logical", "scope": "keyword.operator.logical.python", "settings": {"foreground": "#bfc2ff"}}, {"name": "Meta tag", "scope": "meta.tag", "settings": {"foreground": "#e4e1eb"}}, {"name": "Strings", "scope": "string", "settings": {"foreground": "#c1c3f3"}}, {"name": "Inherited Class", "scope": "entity.other.inherited-class", "settings": {"foreground": "#c1c3f3"}}, {"name": "Constant other symbol", "scope": "constant.other.symbol", "settings": {"foreground": "#bfc2ff"}}, {"name": "Integers", "scope": "constant.numeric", "settings": {"foreground": "#ffaaf7"}}, {"name": "Constants", "scope": "constant", "settings": {"foreground": "#ffaaf7"}}, {"name": "Constants", "scope": "punctuation.definition.constant", "settings": {"foreground": "#ffaaf7"}}, {"name": "Tags", "scope": "entity.name.tag", "settings": {"foreground": "#ffb4ab"}}, {"name": "Attributes", "scope": "entity.other.attribute-name", "settings": {"foreground": "#ffaaf7"}}, {"name": "Attribute IDs", "scope": "entity.other.attribute-name.id", "settings": {"fontStyle": "normal", "foreground": "#bfc2ff"}}, {"name": "Attribute class", "scope": "entity.other.attribute-name.class.css", "settings": {"fontStyle": "normal", "foreground": "#ffaaf7"}}, {"name": "Selector", "scope": "meta.selector", "settings": {"foreground": "#bfc2ff"}}, {"name": "Headings", "scope": "markup.heading", "settings": {"foreground": "#ffb4ab"}}, {"name": "Headings", "scope": "markup.heading punctuation.definition.heading, entity.name.section", "settings": {"foreground": "#bfc2ff"}}, {"name": "Units", "scope": "keyword.other.unit", "settings": {"foreground": "#ffb4ab"}}, {"name": "Bold", "scope": "markup.bold,todo.bold", "settings": {"foreground": "#ffaaf7"}}, {"name": "Bold", "scope": "punctuation.definition.bold", "settings": {"foreground": "#c1c3f3"}}, {"name": "markup Italic", "scope": "markup.italic, punctuation.definition.italic,todo.emphasis", "settings": {"foreground": "#bfc2ff"}}, {"name": "emphasis md", "scope": "emphasis md", "settings": {"foreground": "#bfc2ff"}}, {"name": "[VSCODE-CUSTOM] Markdown headings", "scope": "entity.name.section.markdown", "settings": {"foreground": "#ffb4ab"}}, {"name": "[VSCODE-CUSTOM] Markdown heading Punctuation Definition", "scope": "punctuation.definition.heading.markdown", "settings": {"foreground": "#ffb4ab"}}, {"name": "punctuation.definition.list.begin.markdown", "scope": "punctuation.definition.list.begin.markdown", "settings": {"foreground": "#ffb4ab"}}, {"name": "[VSCODE-CUSTOM] Markdown heading setext", "scope": "markup.heading.setext", "settings": {"foreground": "#e4e1eb"}}, {"name": "[VSCODE-CUSTOM] Markdown Punctuation Definition Bold", "scope": "punctuation.definition.bold.markdown", "settings": {"foreground": "#ffaaf7"}}, {"name": "[VSCODE-CUSTOM] Markdown Inline Raw", "scope": "markup.inline.raw.markdown", "settings": {"foreground": "#c1c3f3"}}, {"name": "[VSCODE-CUSTOM] Markdown Inline Raw", "scope": "markup.inline.raw.string.markdown", "settings": {"foreground": "#c1c3f3"}}, {"name": "[VSCODE-CUSTOM] Markdown Punctuation Definition Italic", "scope": "punctuation.definition.italic.markdown", "settings": {"foreground": "#bfc2ff"}}, {"name": "[VSCODE-CUSTOM] Markdown List Punctuation Definition", "scope": "beginning.punctuation.definition.list.markdown", "settings": {"foreground": "#ffb4ab"}}, {"name": "[VSCODE-CUSTOM] Markdown Quote", "scope": "markup.quote.markdown", "settings": {"foreground": "#9695a4", "fontStyle": "italic"}}, {"name": "[VSCODE-CUSTOM] Markdown Punctuation Definition String", "scope": ["punctuation.definition.string.begin.markdown", "punctuation.definition.string.end.markdown", "punctuation.definition.metadata.markdown"], "settings": {"foreground": "#e4e1eb"}}, {"name": "[VSCODE-CUSTOM] Markdown Punctuation Definition Link", "scope": "punctuation.definition.metadata.markdown", "settings": {"foreground": "#bfc2ff"}}, {"name": "[VSCODE-CUSTOM] Markdown Underline Link/Image", "scope": "markup.underline.link.markdown,markup.underline.link.image.markdown", "settings": {"foreground": "#bfc2ff"}}, {"name": "[VSCODE-CUSTOM] Markdown Link Title/Description", "scope": "string.other.link.title.markdown,string.other.link.description.markdown", "settings": {"foreground": "#bfc2ff"}}, {"name": "Escape Characters", "scope": "constant.character.escape", "settings": {"foreground": "#bfc2ff"}}, {"name": "Embedded", "scope": "punctuation.section.embedded, variable.interpolation", "settings": {"foreground": "#ffb4ab"}}, {"name": "Embedded", "scope": "punctuation.section.embedded.begin,punctuation.section.embedded.end", "settings": {"foreground": "#bfc2ff"}}, {"name": "illegal", "scope": "invalid.illegal", "settings": {"foreground": "#e4e1eb"}}, {"name": "illegal", "scope": "invalid.illegal.bad-ampersand.html", "settings": {"foreground": "#e4e1eb"}}, {"name": "Broken", "scope": "invalid.broken", "settings": {"foreground": "#e4e1eb"}}, {"name": "Deprecated", "scope": "invalid.deprecated", "settings": {"foreground": "#e4e1eb"}}, {"name": "Unimplemented", "scope": "invalid.unimplemented", "settings": {"foreground": "#e4e1eb"}}, {"name": "Source Json Meta Structure Dictionary Json > String Quoted <PERSON><PERSON>", "scope": "source.json meta.structure.dictionary.json > string.quoted.json", "settings": {"foreground": "#ffb4ab"}}, {"name": "Source Json Meta Structure Dictionary Json > String Quoted J<PERSON> > Punctuation String", "scope": "source.json meta.structure.dictionary.json > string.quoted.json > punctuation.string", "settings": {"foreground": "#ffb4ab"}}, {"name": "Source Json Meta Structure Dictionary Json > Value Json > String Quoted Json,source Json Meta Structure Array Json > Value Json > String Quoted Json,source Json Meta Structure Dictionary Json > Value Json > String Quoted Json > Punctuation,source Json Meta Structure Array Json > Value Json > String Quoted Json > Punctuation", "scope": "source.json meta.structure.dictionary.json > value.json > string.quoted.json,source.json meta.structure.array.json > value.json > string.quoted.json,source.json meta.structure.dictionary.json > value.json > string.quoted.json > punctuation,source.json meta.structure.array.json > value.json > string.quoted.json > punctuation", "settings": {"foreground": "#c1c3f3"}}, {"name": "Source Json Meta Structure Dictionary Json > Constant Language Json,source Json Meta Structure Array Json > Constant Language Json", "scope": "source.json meta.structure.dictionary.json > constant.language.json,source.json meta.structure.array.json > constant.language.json", "settings": {"foreground": "#bfc2ff"}}, {"name": "ts primitive/builtin types", "scope": "support.type.primitive.ts,support.type.builtin.ts,support.type.primitive.tsx,support.type.builtin.tsx", "settings": {"foreground": "#c1c3f3"}}, {"name": "block scope", "scope": "block.scope.end,block.scope.begin", "settings": {"foreground": "#e4e1eb"}}, {"name": "cs storage type", "scope": "storage.type.cs", "settings": {"foreground": "#c1c3f3"}}, {"name": "cs local variable", "scope": "entity.name.variable.local.cs", "settings": {"foreground": "#ffb4ab"}}, {"scope": "token.info-token", "settings": {"foreground": "#bfc2ff"}}, {"scope": "token.warn-token", "settings": {"foreground": "#ffaaf7"}}, {"scope": "token.error-token", "settings": {"foreground": "#ffb4ab"}}, {"scope": "token.debug-token", "settings": {"foreground": "#bfc2ff"}}, {"name": "String interpolation", "scope": ["punctuation.definition.template-expression.begin", "punctuation.definition.template-expression.end", "punctuation.section.embedded"], "settings": {"foreground": "#bfc2ff"}}, {"name": "Reset JavaScript string interpolation expression", "scope": ["meta.template.expression"], "settings": {"foreground": "#e4e1eb"}}, {"name": "Import module JS", "scope": ["keyword.operator.module"], "settings": {"foreground": "#bfc2ff"}}, {"name": "js Flowtype", "scope": ["support.type.type.flowtype"], "settings": {"foreground": "#bfc2ff"}}, {"name": "js Flow", "scope": ["support.type.primitive"], "settings": {"foreground": "#c1c3f3"}}, {"name": "js class prop", "scope": ["meta.property.object"], "settings": {"foreground": "#ffb4ab"}}, {"name": "js func parameter", "scope": ["variable.parameter.function.js"], "settings": {"foreground": "#ffb4ab"}}, {"name": "js template literals begin", "scope": ["keyword.other.template.begin"], "settings": {"foreground": "#c1c3f3"}}, {"name": "js template literals end", "scope": ["keyword.other.template.end"], "settings": {"foreground": "#c1c3f3"}}, {"name": "js template literals variable braces begin", "scope": ["keyword.other.substitution.begin"], "settings": {"foreground": "#c1c3f3"}}, {"name": "js template literals variable braces end", "scope": ["keyword.other.substitution.end"], "settings": {"foreground": "#c1c3f3"}}, {"name": "js operator.assignment", "scope": ["keyword.operator.assignment"], "settings": {"foreground": "#bfc2ff"}}, {"name": "go operator", "scope": ["keyword.operator.assignment.go"], "settings": {"foreground": "#c1c3f3"}}, {"name": "go operator", "scope": ["keyword.operator.arithmetic.go", "keyword.operator.address.go"], "settings": {"foreground": "#bfc2ff"}}, {"name": "js/ts italic", "scope": "entity.other.attribute-name.js,entity.other.attribute-name.ts,entity.other.attribute-name.jsx,entity.other.attribute-name.tsx,variable.parameter,variable.language.super", "settings": {"fontStyle": "italic"}}, {"name": "comment", "scope": "comment.line.double-slash,comment.block.documentation", "settings": {"fontStyle": "italic"}}, {"name": "Python Keyword Control", "scope": "keyword.control.import.python,keyword.control.flow.python", "settings": {"fontStyle": "italic"}}, {"name": "markup.italic.markdown", "scope": "markup.italic.markdown", "settings": {"fontStyle": "italic"}}, {"name": "css color standard name", "scope": "support.constant.color.w3c-standard-color-name.css,support.constant.color.w3c-standard-color-name.scss", "settings": {"foreground": "#ffaaf7"}}, {"name": "css comma", "scope": "punctuation.separator.list.comma.css", "settings": {"foreground": "#e4e1eb"}}, {"name": "css attribute-name.id", "scope": "support.constant.color.w3c-standard-color-name.css", "settings": {"foreground": "#ffaaf7"}}, {"name": "css property-name", "scope": "support.type.vendored.property-name.css", "settings": {"foreground": "#bfc2ff"}}, {"name": "js/ts module", "scope": "support.module.node,support.type.object.module,support.module.node", "settings": {"foreground": "#c1c3f3"}}, {"name": "entity.name.type.module", "scope": "entity.name.type.module", "settings": {"foreground": "#c1c3f3"}}, {"name": "js variable readwrite", "scope": "variable.other.readwrite,meta.object-literal.key,support.variable.property,support.variable.object.process,support.variable.object.node", "settings": {"foreground": "#ffb4ab"}}, {"name": "js/ts json", "scope": "support.constant.json", "settings": {"foreground": "#ffaaf7"}}, {"name": "js/ts Keyword", "scope": ["keyword.operator.expression.instanceof", "keyword.operator.new", "keyword.operator.ternary", "keyword.operator.optional", "keyword.operator.expression.keyof"], "settings": {"foreground": "#bfc2ff"}}, {"name": "js/ts console", "scope": "support.type.object.console", "settings": {"foreground": "#ffb4ab"}}, {"name": "js/ts support.variable.property.process", "scope": "support.variable.property.process", "settings": {"foreground": "#ffaaf7"}}, {"name": "js console function", "scope": "entity.name.function,support.function.console", "settings": {"foreground": "#bfc2ff"}}, {"name": "keyword.operator.misc.rust", "scope": "keyword.operator.misc.rust", "settings": {"foreground": "#e4e1eb"}}, {"name": "keyword.operator.sigil.rust", "scope": "keyword.operator.sigil.rust", "settings": {"foreground": "#bfc2ff"}}, {"name": "operator", "scope": "keyword.operator.delete", "settings": {"foreground": "#bfc2ff"}}, {"name": "js dom", "scope": "support.type.object.dom", "settings": {"foreground": "#bfc2ff"}}, {"name": "js dom variable", "scope": "support.variable.dom,support.variable.property.dom", "settings": {"foreground": "#ffb4ab"}}, {"name": "python placeholder reset to normal string", "scope": "constant.character.format.placeholder.other.python", "settings": {"foreground": "#ffaaf7"}}, {"name": "Operators", "scope": "keyword.operator", "settings": {"foreground": "#e4e1eb"}}, {"name": "Compound Assignment Operators", "scope": "keyword.operator.assignment.compound", "settings": {"foreground": "#bfc2ff"}}, {"name": "Compound Assignment Operators js/ts", "scope": "keyword.operator.assignment.compound.js,keyword.operator.assignment.compound.ts", "settings": {"foreground": "#bfc2ff"}}, {"name": "Keywords", "scope": "keyword", "settings": {"foreground": "#bfc2ff"}}, {"name": "Namespaces", "scope": "entity.name.namespace", "settings": {"foreground": "#c1c3f3"}}, {"name": "Variables", "scope": "variable", "settings": {"foreground": "#ffb4ab"}}, {"name": "Variables", "scope": "variable.c", "settings": {"foreground": "#e4e1eb"}}, {"name": "Language variables", "scope": "variable.language", "settings": {"foreground": "#c1c3f3"}}, {"name": "Functions", "scope": ["entity.name.function", "meta.require", "support.function.any-method", "variable.function"], "settings": {"foreground": "#bfc2ff"}}, {"name": "Classes", "scope": "entity.name.type.namespace", "settings": {"foreground": "#c1c3f3"}}, {"name": "Classes", "scope": "support.class, entity.name.type.class", "settings": {"foreground": "#c1c3f3"}}, {"name": "Class name", "scope": "entity.name.class.identifier.namespace.type", "settings": {"foreground": "#c1c3f3"}}, {"name": "Class name", "scope": ["entity.name.class", "variable.other.class.js", "variable.other.class.ts"], "settings": {"foreground": "#c1c3f3"}}, {"name": "Storage", "scope": "storage", "settings": {"foreground": "#bfc2ff"}}, {"name": "Storage JS TS", "scope": "token.storage", "settings": {"foreground": "#bfc2ff"}}, {"name": "Source Js Keyword Operator Delete,source Js Keyword Operator In,source Js Keyword Operator Of,source Js Keyword Operator Instanceof,source Js Keyword Operator New,source Js Keyword Operator Typeof,source Js Keyword Operator Void", "scope": "keyword.operator.expression.delete,keyword.operator.expression.in,keyword.operator.expression.of,keyword.operator.expression.instanceof,keyword.operator.new,keyword.operator.expression.typeof,keyword.operator.expression.void", "settings": {"foreground": "#bfc2ff"}}, {"name": "Support type", "scope": "support.type.property-name", "settings": {"foreground": "#e4e1eb"}}, {"name": "Support type", "scope": "support.constant.property-value", "settings": {"foreground": "#e4e1eb"}}, {"name": "Support type", "scope": "support.constant.font-name", "settings": {"foreground": "#ffaaf7"}}, {"name": "Regular Expressions", "scope": "string.regexp", "settings": {"foreground": "#bfc2ff"}}, {"name": "Go package name", "scope": ["entity.name.package.go"], "settings": {"foreground": "#c1c3f3"}}, {"name": "elm prelude", "scope": ["support.type.prelude.elm"], "settings": {"foreground": "#bfc2ff"}}, {"name": "elm constant", "scope": ["support.constant.elm"], "settings": {"foreground": "#ffaaf7"}}, {"name": "template literal", "scope": ["punctuation.quasi.element"], "settings": {"foreground": "#bfc2ff"}}, {"name": "html/pug (jade) escaped characters and entities", "scope": ["constant.character.entity"], "settings": {"foreground": "#ffb4ab"}}, {"name": "styling css pseudo-elements/classes to be able to differentiate from classes which are the same colour", "scope": ["entity.other.attribute-name.pseudo-element", "entity.other.attribute-name.pseudo-class"], "settings": {"foreground": "#bfc2ff"}}, {"name": "[VSCODE-CUSTOM] JSON Property Name", "scope": "support.type.property-name.json", "settings": {"foreground": "#ffb4ab"}}, {"name": "[VSCODE-CUSTOM] JSON Punctuation for Property Name", "scope": "support.type.property-name.json punctuation", "settings": {"foreground": "#ffb4ab"}}, {"name": "Comments", "scope": "comment, punctuation.definition.comment", "settings": {"fontStyle": "italic", "foreground": "#9695a4"}}, {"name": "punctuation.definition.block.sequence.item.yaml", "scope": "punctuation.definition.block.sequence.item.yaml", "settings": {"foreground": "#e4e1eb"}}, {"scope": ["constant.language.symbol.elixir"], "settings": {"foreground": "#bfc2ff"}}, {"name": "python block", "scope": "punctuation.definition.arguments.begin.python,punctuation.definition.arguments.end.python,punctuation.separator.arguments.python,punctuation.definition.list.begin.python,punctuation.definition.list.end.python", "settings": {"foreground": "#e4e1eb"}}, {"name": "python function-call.generic", "scope": "meta.function-call.generic.python", "settings": {"foreground": "#bfc2ff"}}, {"name": "pyCs", "scope": "variable.parameter.function.python", "settings": {"foreground": "#ffaaf7"}}, {"name": "python function decorator @", "scope": "meta.function.decorator.python", "settings": {"foreground": "#bfc2ff"}}, {"name": "python function support", "scope": "support.token.decorator.python,meta.function.decorator.identifier.python", "settings": {"foreground": "#bfc2ff"}}, {"name": "parameter function js/ts", "scope": "function.parameter", "settings": {"foreground": "#e4e1eb"}}, {"name": "brace function", "scope": "function.brace", "settings": {"foreground": "#e4e1eb"}}, {"name": "parameter function ruby cs", "scope": "function.parameter.ruby, function.parameter.cs", "settings": {"foreground": "#e4e1eb"}}, {"name": "constant.language.symbol.ruby", "scope": "constant.language.symbol.ruby", "settings": {"foreground": "#bfc2ff"}}, {"name": "rgb-value", "scope": "rgb-value", "settings": {"foreground": "#bfc2ff"}}, {"name": "rgb value", "scope": "inline-color-decoration rgb-value", "settings": {"foreground": "#ffaaf7"}}, {"name": "rgb value less", "scope": "less rgb-value", "settings": {"foreground": "#ffaaf7"}}, {"name": "Clojure globals", "scope": ["entity.global.clojure"], "settings": {"foreground": "#c1c3f3"}}, {"name": "Clojure symbols", "scope": ["meta.symbol.clojure"], "settings": {"foreground": "#ffb4ab"}}, {"name": "Clojure constants", "scope": ["constant.keyword.clojure"], "settings": {"foreground": "#bfc2ff"}}, {"name": "CoffeeScript Function Argument", "scope": ["meta.arguments.coffee", "variable.parameter.function.coffee"], "settings": {"foreground": "#ffb4ab"}}, {"name": "<PERSON><PERSON> Default Text", "scope": ["source.ini"], "settings": {"foreground": "#c1c3f3"}}, {"name": "Makefile prerequisities", "scope": ["meta.scope.prerequisites.makefile"], "settings": {"foreground": "#ffb4ab"}}, {"name": "Makefile text colour", "scope": ["source.makefile"], "settings": {"foreground": "#c1c3f3"}}, {"name": "Groovy import names", "scope": ["storage.modifier.import.groovy"], "settings": {"foreground": "#c1c3f3"}}, {"name": "Groovy Methods", "scope": ["meta.method.groovy"], "settings": {"foreground": "#bfc2ff"}}, {"name": "Groovy Variables", "scope": ["meta.definition.variable.name.groovy"], "settings": {"foreground": "#ffb4ab"}}, {"name": "Groovy Inheritance", "scope": ["meta.definition.class.inherited.classes.groovy"], "settings": {"foreground": "#c1c3f3"}}, {"name": "HLSL Semantic", "scope": ["support.variable.semantic.hlsl"], "settings": {"foreground": "#c1c3f3"}}, {"name": "HLSL Types", "scope": ["support.type.texture.hlsl", "support.type.sampler.hlsl", "support.type.object.hlsl", "support.type.object.rw.hlsl", "support.type.fx.hlsl", "support.type.object.hlsl"], "settings": {"foreground": "#bfc2ff"}}, {"name": "SQL Variables", "scope": ["text.variable", "text.bracketed"], "settings": {"foreground": "#ffb4ab"}}, {"name": "types", "scope": ["support.type.swift", "support.type.vb.asp"], "settings": {"foreground": "#c1c3f3"}}, {"name": "Java Variables", "scope": "token.variable.parameter.java", "settings": {"foreground": "#e4e1eb"}}, {"name": "Java Imports", "scope": "import.storage.java", "settings": {"foreground": "#c1c3f3"}}, {"name": "Packages", "scope": "token.package.keyword", "settings": {"foreground": "#bfc2ff"}}, {"name": "Packages", "scope": "token.package", "settings": {"foreground": "#e4e1eb"}}, {"name": "Type Name", "scope": "entity.name.type", "settings": {"foreground": "#c1c3f3"}}, {"name": "Keyword Control", "scope": "keyword.control", "settings": {"foreground": "#bfc2ff"}}, {"name": "Control Elements", "scope": "control.elements, keyword.operator.less", "settings": {"foreground": "#ffaaf7"}}, {"name": "Methods", "scope": "keyword.other.special-method", "settings": {"foreground": "#bfc2ff"}}, {"name": "Java Storage", "scope": "token.storage.type.java", "settings": {"foreground": "#c1c3f3"}}, {"name": "Support", "scope": "support.function", "settings": {"foreground": "#bfc2ff"}}, {"name": "Class name php", "scope": "variable.other.class.php", "settings": {"foreground": "#ffb4ab"}}, {"name": "laravel blade tag", "scope": "text.html.laravel-blade source.php.embedded.line.html entity.name.tag.laravel-blade", "settings": {"foreground": "#bfc2ff"}}, {"name": "laravel blade @", "scope": "text.html.laravel-blade source.php.embedded.line.html support.constant.laravel-blade", "settings": {"foreground": "#bfc2ff"}}, {"name": "use statement for other classes", "scope": "support.other.namespace.use.php,support.other.namespace.use-as.php,support.other.namespace.php,entity.other.alias.php,meta.interface.php", "settings": {"foreground": "#c1c3f3"}}, {"name": "error suppression", "scope": "keyword.operator.error-control.php", "settings": {"foreground": "#bfc2ff"}}, {"name": "php instanceof", "scope": "keyword.operator.type.php", "settings": {"foreground": "#bfc2ff"}}, {"name": "style double quoted array index normal begin", "scope": "punctuation.section.array.begin.php", "settings": {"foreground": "#e4e1eb"}}, {"name": "style double quoted array index normal end", "scope": "punctuation.section.array.end.php", "settings": {"foreground": "#e4e1eb"}}, {"name": "php illegal.non-null-typehinted", "scope": "invalid.illegal.non-null-typehinted.php", "settings": {"foreground": "#ffb4ab"}}, {"name": "php types", "scope": "storage.type.php,meta.other.type.phpdoc.php,keyword.other.type.php,keyword.other.array.phpdoc.php", "settings": {"foreground": "#c1c3f3"}}, {"name": "php call-function", "scope": "meta.function-call.php,meta.function-call.object.php,meta.function-call.static.php", "settings": {"foreground": "#bfc2ff"}}, {"name": "php function-resets", "scope": "punctuation.definition.parameters.begin.bracket.round.php,punctuation.definition.parameters.end.bracket.round.php,punctuation.separator.delimiter.php,punctuation.section.scope.begin.php,punctuation.section.scope.end.php,punctuation.terminator.expression.php,punctuation.definition.arguments.begin.bracket.round.php,punctuation.definition.arguments.end.bracket.round.php,punctuation.definition.storage-type.begin.bracket.round.php,punctuation.definition.storage-type.end.bracket.round.php,punctuation.definition.array.begin.bracket.round.php,punctuation.definition.array.end.bracket.round.php,punctuation.definition.begin.bracket.round.php,punctuation.definition.end.bracket.round.php,punctuation.definition.begin.bracket.curly.php,punctuation.definition.end.bracket.curly.php,punctuation.definition.section.switch-block.end.bracket.curly.php,punctuation.definition.section.switch-block.start.bracket.curly.php,punctuation.definition.section.switch-block.begin.bracket.curly.php,punctuation.definition.section.switch-block.end.bracket.curly.php", "settings": {"foreground": "#e4e1eb"}}, {"name": "support php constants", "scope": "support.constant.core.rust", "settings": {"foreground": "#ffaaf7"}}, {"name": "support php constants", "scope": "support.constant.ext.php,support.constant.std.php,support.constant.core.php,support.constant.parser-token.php", "settings": {"foreground": "#ffaaf7"}}, {"name": "php goto", "scope": "entity.name.goto-label.php,support.other.php", "settings": {"foreground": "#bfc2ff"}}, {"name": "php logical/bitwise operator", "scope": "keyword.operator.logical.php,keyword.operator.bitwise.php,keyword.operator.arithmetic.php", "settings": {"foreground": "#bfc2ff"}}, {"name": "php regexp operator", "scope": "keyword.operator.regexp.php", "settings": {"foreground": "#bfc2ff"}}, {"name": "php comparison", "scope": "keyword.operator.comparison.php", "settings": {"foreground": "#bfc2ff"}}, {"name": "php heredoc/nowdoc", "scope": "keyword.operator.heredoc.php,keyword.operator.nowdoc.php", "settings": {"foreground": "#bfc2ff"}}], "colors": {"foreground": "#e4e1eb", "focusBorder": "#bfc2ff", "selection.background": "#bfc2ff66", "scrollbar.shadow": "#13131900", "activityBar.foreground": "#e8e5ef", "activityBar.background": "#131319", "activityBar.inactiveForeground": "#c6c5d5", "activityBarBadge.foreground": "#0a0d86", "activityBarBadge.background": "#bfc2ff", "sideBar.background": "#131319", "sideBar.foreground": "#e8e5ef", "sideBarSectionHeader.background": "#464653", "sideBarSectionHeader.foreground": "#c6c5d5", "sideBarTitle.foreground": "#bfc2ff", "list.inactiveSelectionBackground": "#464653", "list.inactiveSelectionForeground": "#c6c5d5", "list.hoverBackground": "#46465340", "list.hoverForeground": "#c6c5d5", "list.activeSelectionBackground": "#bfc2ff40", "list.activeSelectionForeground": "#e8e5ef", "tree.indentGuidesStroke": "#9695a4", "list.dropBackground": "#5159c440", "list.highlightForeground": "#bfc2ff", "list.focusBackground": "#bfc2ff30", "list.focusForeground": "#e8e5ef", "listFilterWidget.background": "#984297", "listFilterWidget.outline": "#9695a400", "listFilterWidget.noMatchesOutline": "#ffb4ab", "statusBar.foreground": "#c6c5d5", "statusBar.background": "#464653", "statusBarItem.hoverBackground": "#bfc2ff1f", "statusBar.debuggingBackground": "#ffaaf7", "statusBar.debuggingForeground": "#4c004f", "statusBar.noFolderBackground": "#131319", "statusBar.noFolderForeground": "#e8e5ef", "statusBarItem.remoteBackground": "#bfc2ff", "statusBarItem.remoteForeground": "#0a0d86", "titleBar.activeBackground": "#131319", "titleBar.activeForeground": "#e8e5ef", "titleBar.inactiveBackground": "#464653", "titleBar.inactiveForeground": "#c6c5d5", "titleBar.border": "#9695a400", "menubar.selectionForeground": "#e8e5ef", "menubar.selectionBackground": "#4646531a", "menu.foreground": "#e8e5ef", "menu.background": "#131319", "menu.selectionForeground": "#ffffff", "menu.selectionBackground": "#5159c4", "menu.selectionBorder": "#9695a400", "menu.separatorBackground": "#9695a4", "menu.border": "#9695a485", "button.background": "#bfc2ff", "button.foreground": "#0a0d86", "button.hoverBackground": "#5159c4", "button.secondaryForeground": "#21244b", "button.secondaryBackground": "#c1c3f3", "button.secondaryHoverBackground": "#5e618c", "checkbox.background": "#5159c4", "checkbox.foreground": "#ffffff", "dropdown.background": "#131319", "dropdown.foreground": "#e8e5ef", "input.background": "#464653", "input.foreground": "#c6c5d5", "input.placeholderForeground": "#c6c5d59e", "inputOption.activeBackground": "#bfc2ff30", "inputOption.activeBorder": "#bfc2ff30", "inputOption.activeForeground": "#bfc2ff", "inputValidation.errorBackground": "#c32220", "inputValidation.errorForeground": "#ffffff", "inputValidation.errorBorder": "#ffb4ab", "inputValidation.infoBackground": "#984297", "inputValidation.infoForeground": "#ffffff", "inputValidation.infoBorder": "#ffaaf7", "inputValidation.warningBackground": "#5e618c", "inputValidation.warningForeground": "#ffffff", "inputValidation.warningBorder": "#c1c3f3", "scrollbarSlider.activeBackground": "#e8e5ef80", "scrollbarSlider.background": "#e8e5ef30", "scrollbarSlider.hoverBackground": "#e8e5ef50", "badge.foreground": "#0a0d86", "badge.background": "#bfc2ff", "progressBar.background": "#bfc2ff", "editor.background": "#131319", "editor.foreground": "#e4e1eb", "editorLineNumber.foreground": "#9695a4", "editorLineNumber.activeForeground": "#bfc2ff", "editorCursor.background": "#131319", "editorCursor.foreground": "#bfc2ff", "editor.selectionBackground": "#bfc2ff40", "editor.selectionHighlightBackground": "#46465380", "editor.inactiveSelectionBackground": "#464653", "editor.wordHighlightBackground": "#464653", "editor.wordHighlightStrongBackground": "#5159c480", "editor.findMatchBackground": "#ffaaf780", "editor.findMatchHighlightBackground": "#ffaaf760", "editor.findRangeHighlightBackground": "#464653", "editor.hoverHighlightBackground": "#46465350", "editor.lineHighlightBackground": "#46465350", "editor.lineHighlightBorder": "#9695a400", "editorLink.activeForeground": "#bfc2ff", "editorIndentGuide.background": "#9695a420", "editorIndentGuide.activeBackground": "#9695a4", "editorRuler.foreground": "#9695a4", "editorBracketMatch.background": "#bfc2ff4d", "editorBracketMatch.border": "#bfc2ff", "editorBracketHighlight.foreground1": "#bfc2ff", "editorBracketHighlight.foreground2": "#c1c3f3", "editorBracketHighlight.foreground3": "#ffaaf7", "editorBracketHighlight.foreground4": "#5159c4", "editorBracketHighlight.foreground5": "#5e618c", "editorBracketHighlight.foreground6": "#984297", "editorBracketHighlight.unexpectedBracket.foreground": "#ffb4ab", "editorOverviewRuler.border": "#9695a4", "editorOverviewRuler.findMatchForeground": "#bfc2ff", "editorOverviewRuler.rangeHighlightForeground": "#bfc2ff", "editorOverviewRuler.selectionHighlightForeground": "#bfc2ff", "editorOverviewRuler.wordHighlightForeground": "#c1c3f3", "editorOverviewRuler.wordHighlightStrongForeground": "#c1c3f3", "editorOverviewRuler.modifiedForeground": "#bfc2ff80", "editorOverviewRuler.addedForeground": "#c1c3f380", "editorOverviewRuler.deletedForeground": "#ffb4ab80", "editorOverviewRuler.errorForeground": "#ffb4ab", "editorOverviewRuler.warningForeground": "#c1c3f3", "editorOverviewRuler.infoForeground": "#ffaaf7", "editorGutter.background": "#131319", "editorGutter.modifiedBackground": "#bfc2ff80", "editorGutter.addedBackground": "#c1c3f380", "editorGutter.deletedBackground": "#ffb4ab80", "editorCodeLens.foreground": "#9695a4", "editorGroup.border": "#9695a4", "editorGroup.dropBackground": "#5159c440", "editorGroupHeader.noTabsBackground": "#131319", "editorGroupHeader.tabsBackground": "#131319", "editorGroupHeader.tabsBorder": "#9695a400", "editorWidget.foreground": "#e8e5ef", "editorWidget.background": "#131319", "editorWidget.border": "#9695a4", "editorWidget.resizeBorder": "#9695a4", "editorSuggestWidget.background": "#131319", "editorSuggestWidget.border": "#9695a4", "editorSuggestWidget.foreground": "#e8e5ef", "editorSuggestWidget.highlightForeground": "#bfc2ff", "editorSuggestWidget.selectedBackground": "#464653", "editorHoverWidget.foreground": "#e8e5ef", "editorHoverWidget.background": "#131319", "editorHoverWidget.border": "#9695a4", "editorMarkerNavigation.background": "#131319", "editorMarkerNavigationError.background": "#ffb4ab40", "editorMarkerNavigationWarning.background": "#c1c3f340", "editorMarkerNavigationInfo.background": "#ffaaf740", "peekView.border": "#bfc2ff", "peekViewEditor.background": "#131319", "peekViewEditor.matchHighlightBackground": "#ffaaf799", "peekViewResult.background": "#464653", "peekViewResult.fileForeground": "#c6c5d5", "peekViewResult.lineForeground": "#c6c5d5", "peekViewResult.matchHighlightBackground": "#ffaaf740", "peekViewResult.selectionBackground": "#bfc2ff40", "peekViewResult.selectionForeground": "#ffffff", "peekViewTitle.background": "#464653", "peekViewTitleDescription.foreground": "#c6c5d5", "peekViewTitleLabel.foreground": "#bfc2ff", "merge.currentHeaderBackground": "#bfc2ff30", "merge.currentContentBackground": "#bfc2ff20", "merge.incomingHeaderBackground": "#ffaaf730", "merge.incomingContentBackground": "#ffaaf720", "merge.commonHeaderBackground": "#9695a430", "merge.commonContentBackground": "#9695a420", "merge.border": "#9695a400", "panel.background": "#131319", "panel.border": "#9695a4", "panel.dropBorder": "#bfc2ff", "panelTitle.activeBorder": "#bfc2ff", "panelTitle.activeForeground": "#bfc2ff", "panelTitle.inactiveForeground": "#c6c5d5", "tab.activeBackground": "#131319", "tab.activeForeground": "#bfc2ff", "tab.border": "#9695a400", "tab.activeBorder": "#bfc2ff", "tab.inactiveBackground": "#464653", "tab.inactiveForeground": "#c6c5d5", "tab.unfocusedActiveForeground": "#bfc2ff99", "tab.unfocusedInactiveForeground": "#c6c5d599", "tab.unfocusedActiveBorder": "#bfc2ff80", "tab.unfocusedActiveBackground": "#13131999", "tab.unfocusedInactiveBackground": "#46465399", "tab.activeModifiedBorder": "#ffaaf7", "tab.inactiveModifiedBorder": "#ffaaf780", "tab.unfocusedActiveModifiedBorder": "#ffaaf740", "tab.unfocusedInactiveModifiedBorder": "#ffaaf720", "terminal.background": "#131319", "terminal.foreground": "#e4e1eb", "terminal.ansiBlack": "#9695a4", "terminal.ansiRed": "#ffb4ab", "terminal.ansiGreen": "#c1c3f3", "terminal.ansiYellow": "#5e618c", "terminal.ansiBlue": "#bfc2ff", "terminal.ansiMagenta": "#ffaaf7", "terminal.ansiCyan": "#984297", "terminal.ansiWhite": "#e8e5ef", "terminal.ansiBrightBlack": "#c6c5d5", "terminal.ansiBrightRed": "#c32220", "terminal.ansiBrightGreen": "#5e618c", "terminal.ansiBrightYellow": "#c1c3f3", "terminal.ansiBrightBlue": "#5159c4", "terminal.ansiBrightMagenta": "#984297", "terminal.ansiBrightCyan": "#ffaaf7", "terminal.ansiBrightWhite": "#e4e1eb", "terminalCursor.background": "#131319", "terminalCursor.foreground": "#bfc2ff", "notificationCenter.border": "#9695a4", "notificationCenterHeader.background": "#464653", "notificationCenterHeader.foreground": "#c6c5d5", "notificationToast.border": "#9695a4", "notifications.foreground": "#e8e5ef", "notifications.background": "#131319", "notifications.border": "#9695a4", "notificationsErrorIcon.foreground": "#ffb4ab", "notificationsWarningIcon.foreground": "#c1c3f3", "notificationsInfoIcon.foreground": "#ffaaf7", "extensionButton.prominentForeground": "#0a0d86", "extensionButton.prominentBackground": "#bfc2ff", "extensionButton.prominentHoverBackground": "#5159c4", "pickerGroup.border": "#9695a4", "pickerGroup.foreground": "#bfc2ff", "debugToolBar.background": "#131319", "debugToolBar.border": "#9695a4", "welcomePage.buttonBackground": "#464653", "welcomePage.buttonHoverBackground": "#bfc2ff20", "walkThrough.embeddedEditorBackground": "#13131950", "gitDecoration.modifiedResourceForeground": "#bfc2ffc0", "gitDecoration.deletedResourceForeground": "#ffb4abc0", "gitDecoration.untrackedResourceForeground": "#c1c3f3c0", "gitDecoration.ignoredResourceForeground": "#9695a480", "gitDecoration.conflictingResourceForeground": "#ffaaf7c0", "gitDecoration.submoduleResourceForeground": "#c6c5d5c0", "settings.headerForeground": "#e4e1eb", "settings.modifiedItemIndicator": "#bfc2ff", "settings.dropdownBackground": "#464653", "settings.dropdownForeground": "#c6c5d5", "settings.dropdownBorder": "#9695a4", "settings.checkboxBackground": "#464653", "settings.checkboxForeground": "#c6c5d5", "settings.checkboxBorder": "#9695a4", "settings.textInputBackground": "#464653", "settings.textInputForeground": "#c6c5d5", "settings.textInputBorder": "#9695a4", "settings.numberInputBackground": "#464653", "settings.numberInputForeground": "#c6c5d5", "settings.numberInputBorder": "#9695a4", "breadcrumb.foreground": "#c6c5d5", "breadcrumb.background": "#131319", "breadcrumb.focusForeground": "#e8e5ef", "breadcrumb.activeSelectionForeground": "#bfc2ff", "breadcrumbPicker.background": "#131319", "diffEditor.insertedTextBackground": "#c1c3f320", "diffEditor.removedTextBackground": "#ffb4ab20", "diffEditor.diagonalFill": "#9695a440", "debugExceptionWidget.background": "#131319", "debugExceptionWidget.border": "#9695a4", "editorGutter.commentRangeForeground": "#c6c5d5", "icon.foreground": "#e4e1eb", "minimapGutter.addedBackground": "#c1c3f380", "minimapGutter.modifiedBackground": "#bfc2ff80", "minimapGutter.deletedBackground": "#ffb4ab80", "minimap.findMatchHighlight": "#bfc2ff80", "minimap.selectionHighlight": "#bfc2ff40", "minimap.errorHighlight": "#ffb4ab80", "minimap.warningHighlight": "#c1c3f380", "minimap.background": "#131319", "sideBar.dropBackground": "#46465340", "editorGroup.emptyBackground": "#131319", "panelSection.border": "#9695a4", "statusBarItem.activeBackground": "#bfc2ff25", "settings.focusedRowBackground": "#46465350", "editorGutter.foldingControlForeground": "#c6c5d5", "editor.foldBackground": "#bfc2ff4d", "editorError.foreground": "#ffb4ab", "editorError.background": "#c3222000", "editorError.border": "#9695a400", "editorWarning.foreground": "#c1c3f3", "editorWarning.background": "#5e618c00", "editorWarning.border": "#9695a400", "editorInfo.foreground": "#ffaaf7", "editorInfo.background": "#98429700", "editorInfo.border": "#9695a400", "editorWhitespace.foreground": "#9695a429", "widget.shadow": "#1313195c", "peekViewEditorGutter.background": "#131319", "peekViewEditor.matchHighlightBorder": "#ffaaf7", "input.border": "#9695a400", "textLink.foreground": "#bfc2ff", "textLink.activeForeground": "#5159c4"}}