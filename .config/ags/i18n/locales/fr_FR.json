{"No media": "Aucun média", "Powered by Google": "Propulsé par Google", "Uses gemini-pro.\nNot affiliated, endorsed, or sponsored by Google.\n\nPrivacy: Chat messages aren't linked to your account,\n    but will be read by human reviewers to improve the model.": "Utilise gemini-pro.\nNon affilié, approuvé ou sponsorisé par Google.\n\nConfidentialité : Les messages ne sont pas liés à votre compte,\n    mais seront lus par des réviseurs humains pour améliorer le modèle.", "Precise": "<PERSON><PERSON><PERSON>", "Balanced": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Creative": "<PERSON><PERSON><PERSON><PERSON>", "Gemini's temperature value.\n  Precise = 0\n  Balanced = 0.5\n  Creative = 1": "Valeur de température de Gemini.\n  Précis = 0\n  Équilibré = 0.5\n  Créatif = 1", "Enhancements": "Améliorations", "Tells Gemini:\n- It's a Linux sidebar assistant\n- Be brief and use bullet points": "Indique à Gemini :\n- C'est un assistant de barre latérale Linux\n- Soyez bref et utilisez des puces", "Safety": "Sécurité", "When turned off, tells the API (not the model) \nto not block harmful/explicit content": "Lorsque désactivé, indique à l'API (pas au modèle)\nde ne pas bloquer le contenu nocif/explicite", "History": "Historique", "Saves chat history\nMessages in previous chats won't show automatically, but they are there": "Enregistre l'historique du chat\nLes messages des chats précédents ne s'afficheront pas automatiquement, mais ils sont là", "Key stored in:": "Clé stockée dans :", "To update this key, type": "Pour mettre à jour cette clé, tapez", "Updated API Key at": "Clé API mise à jour à", "Currently using": "Utilise actuellement", "Select ChatGPT-compatible API provider": "Sélectionnez un fournisseur d'API compatible ChatGPT", "Official OpenAI API.\nPricing: Free for the first $5 or 3 months, whichever is less.": "API officielle OpenAI.\nTarification : Gratuit pour les premiers 5$ ou 3 mois, selon le plus court.", "Official Ollama API.\nPricing: Free.": "API officielle Ollama.\nTarification : Gratuit.", "A unified interface for LLMs": "Une interface unifiée pour les LLMs", "An API from Tornado Softwares\nPricing: Free: 100/day\nRequires you to join their Discord for a key": "Une API de Tornado Softwares\nTarification : Gratuit : 100/jour\nNécessite de rejoindre leur Discord pour une clé", "An API from @zukixa on GitHub.\nNote: Keys are IP-locked so it's buggy sometimes\nPricing: Free: 10/min, 800/day.\nRequires you to join their Discord for a key": "Une API de @zukixa sur GitHub.\nNote : Les clés sont liées à l'IP donc parfois bugué\nTarification : Gratuit : 10/min, 800/jour.\nNécessite de rejoindre leur Discord pour une clé", "Provider shown above": "Fournisseur affiché ci-dessus", "Uses gpt-3.5-turbo.\nNot affiliated, endorsed, or sponsored by OpenAI.\n\nPrivacy: OpenAI claims they do not use your data\nwhen you use their API. Idk about others.": "Utilise gpt-3.5-turbo.\nNon affilié, approuvé ou sponsorisé par OpenAI.\n\nConfidentialité : OpenAI affirme ne pas utiliser vos données\nlors de l'utilisation de leur API. Je ne sais pas pour les autres.", "The model's temperature value.\n  Precise = 0\n  Balanced = 0.5\n  Creative = 1": "Valeur de température du modèle.\n  Précis = 0\n  Équilibré = 0.5\n  Créatif = 1", "An API key is required\nYou can grab one <u>here</u>, then enter it below": "Une clé API est requise\nV<PERSON> pouvez en obtenir une <u>ici</u>, puis l'entrer ci-dessous", "Tells the model:\n- It's a Linux sidebar assistant\n- Be brief and use bullet points": "Indique au modèle :\n- C'est un assistant de barre latérale Linux\n- Soyez bref et utilisez des puces", "Type tags for a random pic.\nNSFW content will not be returned unless\nyou explicitly request such a tag.\n\nDisclaimer: Not affiliated with the providers\nnor responsible for any of their content.": "Tapez des tags pour une image aléatoire.\nLe contenu NSFW ne sera pas retourné sauf si\nvous demandez explicitement un tel tag.\n\nAvertissement : Non affilié aux fournisseurs\nni responsable de leur contenu.", "Tags →": "Tags →", "Invalid command.": "Commande invalide.", "Powered by yande.re and konachan": "Propulsé par yande.re et konachan", "Lewds": "Contenus explicites", "Shows naughty stuff when enabled.\nYa like those? Add this to user_options.js:\n\t'sidebar': {\n\t'image': {\n\t\t'allowNsfw': true,\n\t}\n}": "Affiche du contenu explicite quand activé.\nVous aimez ça ? Ajoutez ceci à user_options.js :\n\t'sidebar': {\n\t'image': {\n\t\t'allowNsfw': true,\n\t}\n}", "Save in folder by tags": "Sauvegarder dans un dossier par tags", "Saves images in folders by their tags": "Sauvegarde les images dans des dossiers selon leurs tags", "Message Gemini...": "Message à Gemini...", "Enter Google AI API Key...": "Entrez la clé API Google AI...", "Message the model...": "Message au modèle...", "Enter API Key...": "Entrez la clé API...", "Enter tags": "Entrez les tags", "Quick scripts": "Scripts rapides", "Change screen resolution": "Changer la résolution d'écran", "Update packages": "Mettre à jour les paquets", "Trim system generations to 5": "Réduire les générations système à 5", "Trim home manager generations to 5": "Réduire les générations du gestionnaire personnel à 5", "Remove orphan packages": "Supp<PERSON>er les paquets orphelins", "Uninstall unused flatpak packages": "Désinstaller les paquets flatpak inutilisés", "<span strikethrough=\"true\">Inaccurate</span> Color picker": "<span strikethrough=\"true\">Imprécis</span> Sélecteur de couleur", "Result": "Résultat", "Type to search": "Tapez pour rechercher", "illogical-impulse": "impulsion-illogique", "RAM Usage": "Utilisation RAM", "Swap Usage": "Utilisation Swap", "CPU Usage": "Utilisation CPU", "Uptime:": "Temps de fonctionnement :", "Screen snip": "Capture d'écran", "Color picker": "<PERSON><PERSON><PERSON><PERSON> de couleur", "Toggle on-screen keyboard": "Activer/dés<PERSON>r le clavier virtuel", "Night Light": "Lumière nocturne", "Keep system awake": "Garder le système éveillé", "Cloudflare WARP": "Cloudflare WARP", "Session": "Session", "Bluetooth | Right-click to configure": "Bluetooth | Clic droit pour configurer", "Wifi | Right-click to configure": "Wifi | Clic droit pour configurer", "Right-click to configure": "Clic droit pour configurer", "Unknown": "Inconnu", "Reload Environment config": "Recharger la configuration d'environnement", "Open Settings": "<PERSON><PERSON><PERSON><PERSON><PERSON> les paramètres", "Notifications": "Notifications", "Audio controls": "Contrôles audio", "Bluetooth": "Bluetooth", "Wifi networks": "Réseaux Wifi", "Live config": "Configuration en direct", "Silence": "Silence", "Clear": "<PERSON><PERSON><PERSON><PERSON>", "No notifications": "Aucune notification", "notifications": "notifications", "Close": "<PERSON><PERSON><PERSON>", "Now": "Maintenant", "Yesterday": "<PERSON>er", "No audio source": "Aucune source audio", "Remove device": "Supprimer l'appareil", "Connected": "Connecté", "Paired": "<PERSON><PERSON><PERSON><PERSON>", "More": "Plus", "Selected": "Sélectionné", "Current network": "Réseau actuel", "Authentication": "Authentification", "Effects": "<PERSON><PERSON><PERSON>", "Transparency": "Transparence", "[AGS]\nMake shell elements transparent\nBlur is also recommended if you enable this": "[AGS]\nRendre les éléments du shell transparents\nLe flou est également recommandé si vous activez ceci", "Blur": "<PERSON><PERSON>", "[Hyprland]\nEnable blur on transparent elements\nDoesn't affect performance/power consumption unless you have transparent windows.": "[Hy<PERSON><PERSON>]\nActiver le flou sur les éléments transparents\nN'affecte pas les performances/consommation d'énergie sauf si vous avez des fenêtres transparentes.", "X-ray": "Rayons X", "[Hyprland]\nMake everything behind a window/layer except the wallpaper not rendered on its blurred surface\nRecommended to improve performance (if you don't abuse transparency/blur) ": "[Hyprland]\nNe pas rendre tout ce qui est derrière une fenêtre/couche sauf le fond d'écran sur sa surface floue\nRecommandé pour améliorer les performances (si vous n'abusez pas de la transparence/flou) ", "Size": "<PERSON><PERSON>", "[Hyprland]\nAdjust the blur radius. Generally doesn't affect performance\nHigher = more color spread": "[Hyprland]\nAjuster le rayon de flou. N'affecte généralement pas les performances\nPlus élevé = plus de diffusion de couleur", "Passes": "Passes", "[Hyprland] Adjust the number of runs of the blur algorithm\nMore passes = more spread and power consumption\n4 is recommended\n2- would look weird and 6+ would look lame.": "[Hyp<PERSON>] Ajuster le nombre d'exécutions de l'algorithme de flou\nPlus de passes = plus de diffusion et de consommation d'énergie\n4 est recommandé\n2- aurait l'air bizarre et 6+ aurait l'air fade.", "Animations": "Animations", "[Hyprland] [GTK]\nEnable animations": "[Hyprland] [GTK]\nActiver les animations", "Choreography delay": "<PERSON><PERSON><PERSON> chorégraphie", "In milliseconds, the delay between animations of a series": "En millisecondes, le délai entre les animations d'une série", "Developer": "Développeur", "Show FPS": "Afficher les FPS", "[Hyprland]\nShow FPS overlay on top-left corner": "[Hy<PERSON><PERSON>]\nAfficher l'overlay FPS dans le coin supérieur gauche", "Log to stdout": "Journal vers stdout", "Finished tasks will go here": "Les tâches terminées apparaîtront ici", "Nothing here!": "<PERSON>ien ici !", "+ New task": "+ Nouvelle tâche", "Add a task...": "Ajouter une tâche...", "Color scheme": "Schéma de couleurs", "Options": "Options", "Dark Mode": "Mode sombre", "Ya should go to sleep!": "Tu devrais aller dormir !", "Use Gradience": "Utiliser Gradience", "Theme GTK apps using accent color\n(drawback: dark/light mode switching requires restart)": "Thème des applications GTK utilisant la couleur d'accent\n(inconvénient : le changement de mode sombre/clair nécessite un redémarrage)", "Scheme styles": "<PERSON> de sch<PERSON>ma", "Vibrant": "Vibrant", "Vibrant+": "Vibrant+", "Expressive": "Expressif", "Monochrome": "Monochrome", "Rainbow": "Arc-en-ciel", "Fidelity": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Fruit Salad": "Salade de fruits", "Tonal Spot": "Point tonal", "Content": "Contenu", "Use arrow keys to navigate.\nEnter to select, Esc to cancel.": "Utilisez les flèches pour naviguer.\nEntrée pour sélectionner, <PERSON>chap pour annuler.", "Lock": "Verrouiller", "Logout": "Déconnexion", "Sleep": "<PERSON><PERSON> en veille", "Hibernate": "Hibernation", "Shutdown": "<PERSON><PERSON><PERSON><PERSON>", "Reboot": "<PERSON><PERSON><PERSON><PERSON>", "Cancel": "Annuler", "Cheat sheet": "Aide-mémoire", "Keybinds": "<PERSON><PERSON><PERSON><PERSON> clav<PERSON>", "Essentials for beginners": "Essentiels pour débutants", "Make shell elements transparent": "Rendre les éléments du shell transparents", "Actions": "Actions", "Window management": "Gestion des fenêtres", "Window arrangement": "Arrangement des fenêtres", "Workspace management": "Gestion des espaces de travail", "Workspace navigation": "Navigation des espaces de travail", "Widgets": "Widgets", "Media": "Média", "Apps": "Applications", "Neutral": "Neutre", "Launch foot (terminal)": "Lancer foot (terminal)", "Open app launcher": "<PERSON><PERSON><PERSON><PERSON><PERSON> le lanceur d'applications", "Change wallpaper": "Changer le fond d'écran", "Clipboard history >> clipboard": "Historique du presse-papiers >> presse-papiers", "Pick emoji >> clipboard": "Choisir emoji >> presse-papiers", "Screen snip >> edit": "Capture d'écran >> éditer", "Screen snip to text >> clipboard": "Capture d'écran vers texte >> presse-papiers", "Pick color (Hex) >> clipboard": "<PERSON><PERSON> couleur (Hex) >> presse-papiers", "Screenshot >> clipboard": "Capture d'écran >> presse-papiers", "Screenshot >> clipboard & file": "Capture d'écran >> presse-papiers & fichier", "Record region (no sound)": "Enregistrer une région (sans son)", "Record screen (with sound)": "<PERSON>re<PERSON><PERSON><PERSON> <PERSON>'<PERSON>ran (avec son)", "Suspend system": "Su<PERSON><PERSON><PERSON> le système", "Move focus in direction": "<PERSON><PERSON><PERSON><PERSON> le focus dans la direction", "Move window": "<PERSON><PERSON><PERSON>r la fenêtre", "Resize window": "Redimensionner la fenêtre", "Close window": "<PERSON><PERSON><PERSON> la fenêtre", "Pick and kill a window": "Sélectionner et tuer une fenêtre", "Window: move in direction": "Fenêtre : <PERSON><PERSON><PERSON><PERSON> dans la direction", "Window: split ratio +/- 0.1": "Fenêtre : ratio de division +/- 0.1", "Float/unfloat window": "Flotter/défloter la fenêtre", "Toggle fake fullscreen": "Basculer le faux plein écran", "Toggle fullscreen": "Basculer le plein écran", "Toggle maximization": "Basculer la maximisation", "Focus workspace # (1, 2, 3, 4, ...)": "Focus sur l'espace de travail # (1, 2, 3, 4, ...)", "Workspace: focus left/right": "Espace de travail : focus gauche/droite", "Workspace: toggle special": "Espace de travail : basculer spécial", "Window: move to workspace # (1, 2, 3, 4, ...)": "Fenêtre : d<PERSON><PERSON><PERSON> vers l'espace de travail # (1, 2, 3, 4, ...)", "Window: move to workspace left/right": "Fenêtre : d<PERSON><PERSON><PERSON> vers l'espace de travail gauche/droite", "Window: move to workspace special": "Fenêtre : d<PERSON><PERSON><PERSON> vers l'espace de travail spécial", "Window: pin (show on all workspaces)": "Fenêtre : <PERSON><PERSON><PERSON> (afficher sur tous les espaces de travail)", "Restart widgets": "<PERSON><PERSON><PERSON><PERSON> les widgets", "Cycle bar mode (normal, focus)": "Cycler le mode de barre (normal, focus)", "Toggle overview/launcher": "Basculer aperçu/lanceur", "Show cheatsheet": "<PERSON><PERSON><PERSON><PERSON> l'aide-m<PERSON><PERSON><PERSON>", "Toggle left sidebar": "Basculer la barre latérale gauche", "Toggle right sidebar": "Basculer la barre latérale droite", "Toggle music controls": "Basculer les contrôles de musique", "View color scheme and options": "Voir le schéma de couleurs et les options", "Toggle power menu": "Basculer le menu d'alimentation", "Toggle crosshair": "<PERSON><PERSON><PERSON><PERSON> le viseur", "Next track": "Piste suivante", "Previous track": "Piste précédent<PERSON>", "Play/pause media": "Lecture/pause média", "Launch VSCode (editor)": "<PERSON><PERSON> (éditeur)", "Launch Nautilus (file manager)": "<PERSON><PERSON> (gestionnaire de fichiers)", "Launch Firefox (browser)": "Lance<PERSON>fo<PERSON> (navigateur)", "Launch GNOME Text Editor": "Lancer l'éditeur de texte GNOME", "Launch WPS Office": "Lancer WPS Office", "Launch GNOME Settings": "Lancer les paramètres GNOME", "Launch pavucontrol (volume mixer)": "<PERSON><PERSON> (mixeur de volume)", "Launch EasyEffects (equalizer & other audio effects)": "Lancer EasyEffects (égaliseur et autres effets audio)", "Launch GNOME System monitor": "Lancer le moniteur système GNOME", "Toggle fallback launcher: anyrun": "Basculer le lanceur de secours : anyrun", "Toggle fallback launcher: fuzzel": "Basculer le lanceur de secours : fuzzel", "Initialization complete!": "Initialisation terminée !", "Not found": "Non trouvé :", "Calling API": "Appel de l'API", "Downloading image": "Téléchargement de l'image", "Finished!": "Terminé !", "Error": "<PERSON><PERSON><PERSON>", "Not found!": "Non trouvé !", "Go to file url": "Aller à l'URL du fichier", "Save image": "Enregistrer l'image", "Hoard": "Stocker", "Open externally": "Ouvrir en externe", "Failed to load config": "Échec du chargement de la configuration", "You are an assistant on a sidebar of a Wayland Linux desktop. Please always use a casual tone when answering your questions, unless requested otherwise or making writing suggestions. These are the steps you should take to respond to the user's queries:\n1. If it's a writing- or grammar-related question or a sentence in quotation marks, Please point out errors and correct when necessary using underlines, and make the writing more natural where appropriate without making too major changes. If you're given a sentence in quotes but is grammatically correct, explain briefly concepts that are uncommon.\n2. If it's a question about system tasks, give a bash command in a code block with brief explanation.\n3. Otherwise, when asked to summarize information or explaining concepts, you are should use bullet points and headings. For mathematics expressions, you *have to* use LaTeX within a code block with the language set as \"latex\". \nNote: Use casual language, be short, while ensuring the factual correctness of your response. If you are unsure or don't have enough information to provide a confident answer, simply say \"I don't know\" or \"I'm not sure.\". \nThanks!": "Vous êtes un assistant sur une barre latérale d'un bureau Linux Wayland. Veuillez toujours utiliser un ton décontracté en répondant à vos questions, sauf demande contraire ou lors de suggestions d'écriture. Voici les étapes à suivre pour répondre aux questions de l'utilisateur :\n1. S'il s'agit d'une question liée à l'écriture ou à la grammaire ou d'une phrase entre guillemets, veuillez signaler les erreurs et corriger si nécessaire en utilisant des soulignements, et rendre l'écriture plus naturelle le cas échéant sans faire de changements trop importants. Si on vous donne une phrase entre guillemets mais grammaticalement correcte, expliquez brièvement les concepts peu communs.\n2. S'il s'agit d'une question sur les tâches système, donnez une commande bash dans un bloc de code avec une brève explication.\n3. Sinon, lorsqu'on vous demande de résumer des informations ou d'expliquer des concepts, vous devez utiliser des puces et des en-têtes. Pour les expressions mathématiques, vous *devez* utiliser LaTeX dans un bloc de code avec la langue définie comme \"latex\".\nNote : Utilisez un langage décontracté, soyez bref, tout en assurant l'exactitude factuelle de votre réponse. Si vous n'êtes pas sûr ou n'avez pas assez d'informations pour fournir une réponse confiante, dites simplement \"Je ne sais pas\" ou \"Je ne suis pas sûr\".\nMerci !"}