{"No media": "لا توجد وسائط", "Powered by Google": "مدعوم من جوجل", "Uses gemini-pro.\nNot affiliated, endorsed, or sponsored by Google.\n\nPrivacy: Chat messages aren't linked to your account,\n    but will be read by human reviewers to improve the model.": "يستخدم gemini-pro.\nغير مرتبط، معتمد، أو مدعوم من جوجل.\n\nالخصوصية: رسائل الدردشة غير مرتبطة بحسابك،\n    لكنها ستتم قراءتها من قبل مراجعين لتحسين النموذج.", "Precise": "دقيق", "Balanced": "متوازن", "Creative": "إبداعي", "Gemini's temperature value.\n  Precise = 0\n  Balanced = 0.5\n  Creative = 1": "قيمة حرارة Gemini.\n  دقيق = 0\n  متوازن = 0.5\n  إبداعي = 1", "Enhancements": "تحسينات", "Tells Gemini:\n- It's a Linux sidebar assistant\n- Be brief and use bullet points": "يخبر Gemini:\n- إنه مساعد شريط جانبي لـ Linux\n- كن مختصرًا واستخدم النقاط.", "Safety": "السلامة", "When turned off, tells the API (not the model) \nto not block harmful/explicit content": "عند الإيقاف، يخبر الـ API (وليس النموذج)\nبعدم حجب المحتوى الضار/الصريح", "History": "السجل", "Saves chat history\nMessages in previous chats won't show automatically, but they are there": "يحفظ سجل الدردشة\nالرسائل من الدردشات السابقة لن تظهر تلقائيًا، لكنها موجودة.", "Key stored in:": "المفتاح محفوظ في:", "To update this key, type": "لتحديث هذا المفتاح، اكتب", "Updated API Key at": "تم تحديث مفتاح الـ API في", "Currently using": "حاليًا يستخدم", "Select ChatGPT-compatible API provider": "اختر مزود API متوافق مع ChatGPT", "Official OpenAI API.\nPricing: Free for the first $5 or 3 months, whichever is less.": "الـ API الرسمي لـ OpenAI.\nالتكلفة: مجانًا لأول $5 أو 3 أشهر، أيهما أقل.", "Official Ollama API.\nPricing: Free.": "الـ API الرسمي لـ Ollama.\nالتكلفة: مجاني.", "A unified interface for LLMs": "واجهة موحدة لنماذج اللغة الكبيرة", "An API from Tornado Softwares\nPricing: Free: 100/day\nRequires you to join their Discord for a key": "API من Tornado Softwares\nالتكلفة: مجاني: 100/يوم\nيتطلب الانضمام إلى Discord للحصول على مفتاح.", "An API from @zukixa on GitHub.\nNote: Keys are IP-locked so it's buggy sometimes\nPricing: Free: 10/min, 800/day.\nRequires you to join their Discord for a key": "API من @zukixa على GitHub.\nملاحظة: المفاتيح مقيدة بـ IP لذا قد تحدث أخطاء أحيانًا.\nالتكلفة: مجاني: 10/دقيقة، 800/يوم.\nيتطلب الانضمام إلى Discord للحصول على مفتاح.", "Provider shown above": "المزود المعروض أعلاه", "Uses gpt-3.5-turbo.\nNot affiliated, endorsed, or sponsored by OpenAI.\n\nPrivacy: OpenAI claims they do not use your data\nwhen you use their API. Idk about others.": "يستخدم gpt-3.5-turbo.\nغير مرتبط، معتمد، أو مدعوم من OpenAI.\n\nالخصوصية: تدعي OpenAI أنها لا تستخدم بياناتك\nعند استخدامك للـ API. لا أعلم عن الآخرين.", "The model's temperature value.\n  Precise = 0\n  Balanced = 0.5\n  Creative = 1": "قيمة حرارة النموذج.\n  دقيق = 0\n  متوازن = 0.5\n  إبداعي = 1", "An API key is required\nYou can grab one <u>here</u>, then enter it below": "مطلوب مفتاح API\nيمكنك الحصول عليه <u>هنا</u>، ثم إدخاله أدناه.", "Tells the model:\n- It's a Linux sidebar assistant\n- Be brief and use bullet points": "يخبر النموذج:\n- إنه مساعد شريط جانبي لـ Linux\n- كن مختصرًا واستخدم النقاط.", "Type tags for a random pic.\nNSFW content will not be returned unless\nyou explicitly request such a tag.\n\nDisclaimer: Not affiliated with the providers\nnor responsible for any of their content.": "أدخل علامات لصورة عشوائية.\nلن يتم عرض المحتوى غير المناسب ما لم\nتطلب مثل هذا العلامة صراحة.\n\nتنويه: غير مرتبط بالمزودين\nولا يتحمل أي مسؤولية عن محتواهم.", "Tags →": "علامات →", "Invalid command.": "أمر غير صالح.", "Powered by yande.re and konachan": "مدعوم من yande.re <PERSON><PERSON><PERSON>an", "Lewds": "محتوى للبالغين", "Shows naughty stuff when enabled.\nYa like those? Add this to user_options.js:\n\t'sidebar': {\n\t'image': {\n\t\t'allowNsfw': true,\n\t}\n}": "يعرض محتوى للبالغين عند التفعيل.\nهل يعجبك؟ أضف هذا إلى user_options.js:\n\t'sidebar': {\n\t'image': {\n\t\t'allowNsfw': true,\n\t}\n}", "Save in folder by tags": "احفظ في مجلد حسب العلامات", "Saves images in folders by their tags": "يحفظ الصور في مجلدات حسب العلامات.", "Message Gemini...": "أرسل رسالة لـ Gemini...", "Enter Google AI API Key...": "أدخل مفتاح Google AI API...", "Message the model...": "أرسل رسالة للنموذج...", "Enter API Key...": "أدخل مفتاح API...", "Enter tags": "أد<PERSON>ل العلامات", "Quick scripts": "سكربتات سريعة", "Change screen resolution": "تغيير دقة الشاشة", "Update packages": "تحديث الحزم", "Trim system generations to 5": "تقليل أجيال النظام إلى 5", "Trim home manager generations to 5": "تقليل أجيال مدير المنزل إلى 5", "Remove orphan packages": "إزالة الحزم اليتيمة", "Uninstall unused flatpak packages": "إلغاء تثبيت حزم flatpak غير المستخدمة", "<span strikethrough=\"true\">Inaccurate</span> Color picker": "<span strikethrough=\"true\">غير دقيق</span> اختيار الألوان", "Result": "النتيجة", "Type to search": "اكتب للبحث", "illogical-impulse": "دافع غير منطقي", "RAM Usage": "استخدام RAM", "Swap Usage": "استخدام <PERSON>wap", "CPU Usage": "استخدام CPU", "Uptime:": "مدة التشغيل:", "Screen snip": "قص الشاشة", "Color picker": "اختيار الألوان", "Toggle on-screen keyboard": "تبديل لوحة المفاتيح على الشاشة", "Night Light": "الضوء الليلي", "Keep system awake": "إبقاء النظام نشطًا", "Cloudflare WARP": "Cloudflare WARP", "Session": "الجلسة", "Bluetooth | Right-click to configure": "بلوتوث | انقر بزر الماوس الأيمن للإعداد", "Wifi | Right-click to configure": "واي فاي | انقر بزر الماوس الأيمن للإعداد", "Right-click to configure": "انقر بزر الماوس الأيمن للإعداد", "Unknown": "غير معروف", "Reload Environment config": "إعادة تحميل إعدادات البيئة", "Open Settings": "فتح الإعدادات", "Notifications": "الإشعارات", "Audio controls": "التحكم بالصوت", "Bluetooth": "بلوتوث", "Wifi networks": "شبكات Wifi", "Live config": "إعدادات مباشرة", "Silence": "الصمت", "Clear": "م<PERSON><PERSON>", "No notifications": "لا توجد إشعارات", "notifications": "الإشعارات", "Close": "إغلاق", "Now": "الآن", "Yesterday": "الأمس", "No audio source": "لا يوجد مصدر صوت", "Remove device": "إزالة الجهاز", "Connected": "متصل", "Paired": "مقترن", "More": "المزيد", "Selected": "<PERSON><PERSON><PERSON><PERSON>", "Current network": "الشبكة الحالية", "IP": "IP", "Name": "الاسم", "Toggle notifications": "تبديل الإشعارات"}