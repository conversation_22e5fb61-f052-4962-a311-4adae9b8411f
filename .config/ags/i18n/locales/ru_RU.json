{
  "No media": "Нет медиа",
  "Powered by Google": "Работает на Google",
  "Uses gemini-pro.\nNot affiliated, endorsed, or sponsored by Google.\n\nPrivacy: Chat messages aren't linked to your account,\n    but will be read by human reviewers to improve the model.": "Использует gemini-pro.\nНе связан, не одобрен и не спонсируется Google.\n\nКонфиденциальность: Сообщения чата не привязаны к вашему аккаунту,\n    но будут прочитаны людьми для улучшения модели.",
  "Precise": "Точный",
  "Balanced": "Сбалансированный",
  "Creative": "Творческий",
  "Gemini's temperature value.\n  Precise = 0\n  Balanced = 0.5\n  Creative = 1": "Значение температуры Gemini.\n  Точный = 0\n  Сбалансированный = 0.5\n  Творческий = 1",
  "Enhancements": "Улучшения",
  "Tells Gemini:\n- It's a Linux sidebar assistant\n- Be brief and use bullet points": "Сообщает Gemini:\n- Это помощник боковой панели Linux\n- Будь кратким и используй маркированные списки",
  "Safety": "Безопасность",
  "When turned off, tells the API (not the model) \nto not block harmful/explicit content": "При отключении указывает API (не модели)\nне блокировать вредный/откровенный контент",
  "History": "История",
  "Saves chat history\nMessages in previous chats won't show automatically, but they are there": "Сохраняет историю чата\nСообщения из предыдущих чатов не показываются автоматически, но они там есть",
  "Key stored in:": "Ключ сохранен в:",
  "To update this key, type": "Чтобы обновить этот ключ, введите",
  "Updated API Key at": "API ключ обновлен в",
  "Currently using": "Сейчас используется",
  "Select ChatGPT-compatible API provider": "Выберите совместимого с ChatGPT API провайдера",
  "Official OpenAI API.\nPricing: Free for the first $5 or 3 months, whichever is less.": "Официальный API OpenAI.\nЦены: Бесплатно первые $5 или 3 месяца, что наступит раньше.",
  "Official Ollama API.\nPricing: Free.": "Официальный API Ollama.\nЦены: Бесплатно.",
  "A unified interface for LLMs": "Единый интерфейс для LLMs",
  "An API from Tornado Softwares\nPricing: Free: 100/day\nRequires you to join their Discord for a key": "API от Tornado Softwares\nЦены: Бесплатно: 100/день\nТребуется присоединиться к их Discord для получения ключа",
  "An API from @zukixa on GitHub.\nNote: Keys are IP-locked so it's buggy sometimes\nPricing: Free: 10/min, 800/day.\nRequires you to join their Discord for a key": "API от @zukixa на GitHub.\nПримечание: Ключи привязаны к IP, поэтому иногда бывают сбои\nЦены: Бесплатно: 10/мин, 800/день.\nТребуется присоединиться к их Discord для получения ключа",
  "Provider shown above": "Провайдер показан выше",
  "Uses gpt-3.5-turbo.\nNot affiliated, endorsed, or sponsored by OpenAI.\n\nPrivacy: OpenAI claims they do not use your data\nwhen you use their API. Idk about others.": "Использует gpt-3.5-turbo.\nНе связан, не одобрен и не спонсируется OpenAI.\n\nКонфиденциальность: OpenAI утверждает, что не использует ваши данные\nпри использовании их API. Насчет других не знаю.",
  "The model's temperature value.\n  Precise = 0\n  Balanced = 0.5\n  Creative = 1": "Значение температуры модели.\n  Точный = 0\n  Сбалансированный = 0.5\n  Творческий = 1",
  "An API key is required\nYou can grab one <u>here</u>, then enter it below": "Требуется API ключ\nВы можете получить его <u>здесь</u>, затем введите его ниже",
  "Tells the model:\n- It's a Linux sidebar assistant\n- Be brief and use bullet points": "Сообщает модели:\n- Это помощник боковой панели Linux\n- Будь кратким и используй маркированные списки",
  "Type tags for a random pic.\nNSFW content will not be returned unless\nyou explicitly request such a tag.\n\nDisclaimer: Not affiliated with the providers\nnor responsible for any of their content.": "Введите теги для случайного изображения.\nКонтент для взрослых не будет показан, если\nвы явно не запросите такой тег.\n\nОтказ от ответственности: Не связан с провайдерами\nи не несет ответственности за их контент.",
  "Tags →": "Теги →",
  "Invalid command.": "Неверная команда.",
  "Powered by yande.re and konachan": "Работает на yande.re и konachan",
  "Lewds": "18+",
  "Shows naughty stuff when enabled.\nYa like those? Add this to user_options.js:\n\t'sidebar': {\n\t'image': {\n\t\t'allowNsfw': true,\n\t}\n}": "Показывает контент для взрослых при включении.\nНравится? Добавьте это в user_options.js:\n\t'sidebar': {\n\t'image': {\n\t\t'allowNsfw': true,\n\t}\n}",
  "Save in folder by tags": "Сохранить в папку по тегам",
  "Saves images in folders by their tags": "Сохраняет изображения в папки по их тегам",
  "Message Gemini...": "Сообщение Gemini...",
  "Enter Google AI API Key...": "Введите API ключ Google AI...",
  "Message the model...": "Сообщение модели...",
  "Enter API Key...": "Введите API ключ...",
  "Enter tags": "Введите теги",
  "Quick scripts": "Быстрые скрипты",
  "Change screen resolution": "Изменить разрешение экрана",
  "Update packages": "Обновить пакеты",
  "Trim system generations to 5": "Сократить системные поколения до 5",
  "Trim home manager generations to 5": "Сократить поколения домашнего менеджера до 5",
  "Remove orphan packages": "Удалить пакеты-сироты",
  "Uninstall unused flatpak packages": "Удалить неиспользуемые пакеты flatpak",
  "<span strikethrough=\"true\">Inaccurate</span> Color picker": "<span strikethrough=\"true\">Неточный</span> Выбор цвета",
  "Result": "Результат",
  "Type to search": "Введите для поиска",
  "illogical-impulse": "нелогичный-импульс",
  "RAM Usage": "Использование RAM",
  "Swap Usage": "Использование Swap",
  "CPU Usage": "Использование CPU",
  "Uptime:": "Время работы:",
  "Screen snip": "Снимок экрана",
  "Color picker": "Выбор цвета",
  "Toggle on-screen keyboard": "Переключить экранную клавиатуру",
  "Night Light": "Ночной свет",
  "Keep system awake": "Держать систему активной",
  "Cloudflare WARP": "Cloudflare WARP",
  "Session": "Сессия",
  "Bluetooth | Right-click to configure": "Bluetooth | Правый клик для настройки",
  "Wifi | Right-click to configure": "Wifi | Правый клик для настройки",
  "Right-click to configure": "Правый клик для настройки",
  "Unknown": "Неизвестно",
  "Reload Environment config": "Перезагрузить конфигурацию окружения",
  "Open Settings": "Открыть настройки",
  "Notifications": "Уведомления",
  "Audio controls": "Управление звуком",
  "Bluetooth": "Bluetooth",
  "Wifi networks": "Сети Wifi",
  "Live config": "Живая конфигурация",
  "Silence": "Тишина",
  "Clear": "Очистить",
  "No notifications": "Нет уведомлений",
  "notifications": "уведомления",
  "Close": "Закрыть",
  "Now": "Сейчас",
  "Yesterday": "Вчера",
  "No audio source": "Нет источника звука",
  "Remove device": "Удалить устройство",
  "Connected": "Подключено",
  "Paired": "Сопряжено",
  "More": "Ещё",
  "Selected": "Выбрано",
  "Current network": "Текущая сеть",
  "Authentication": "Аутентификация",
  "Effects": "Эффекты",
  "Transparency": "Прозрачность",
  "[AGS]\nMake shell elements transparent\nBlur is also recommended if you enable this": "[AGS]\nСделать элементы оболочки прозрачными\nРекомендуется также включить размытие",
  "Blur": "Размытие",
  "[Hyprland]\nEnable blur on transparent elements\nDoesn't affect performance/power consumption unless you have transparent windows.": "[Hyprland]\nВключить размытие на прозрачных элементах\nНе влияет на производительность/энергопотребление, если у вас нет прозрачных окон.",
  "X-ray": "Рентген",
  "[Hyprland]\nMake everything behind a window/layer except the wallpaper not rendered on its blurred surface\nRecommended to improve performance (if you don't abuse transparency/blur)": "[Hyprland]\nНе отрисовывать всё за окном/слоем кроме обоев на размытой поверхности\nРекомендуется для улучшения производительности (если вы не злоупотребляете прозрачностью/размытием)",
  "Size": "Размер",
  "[Hyprland]\nAdjust the blur radius. Generally doesn't affect performance\nHigher = more color spread": "[Hyprland]\nНастройка радиуса размытия. Обычно не влияет на производительность\nБольше = сильнее размытие цветов",
  "Passes": "Проходы",
  "[Hyprland] Adjust the number of runs of the blur algorithm\nMore passes = more spread and power consumption\n4 is recommended\n2- would look weird and 6+ would look lame.": "[Hyprland] Настройка количества проходов алгоритма размытия\nБольше проходов = сильнее размытие и энергопотребление\nРекомендуется 4\n2- будет выглядеть странно, а 6+ будет выглядеть плохо.",
  "Animations": "Анимации",
  "[Hyprland] [GTK]\nEnable animations": "[Hyprland] [GTK]\nВключить анимации",
  "Choreography delay": "Задержка хореографии",
  "In milliseconds, the delay between animations of a series": "В миллисекундах, задержка между анимациями серии",
  "Developer": "Разработчик",
  "Show FPS": "Показать FPS",
  "[Hyprland]\nShow FPS overlay on top-left corner": "[Hyprland]\nПоказать оверлей FPS в верхнем левом углу",
  "Log to stdout": "Лог в stdout",
  "[Hyprland]\nPrint LOG, ERR, WARN, etc. messages to the console": "[Hyprland]\nВывод сообщений LOG, ERR, WARN и т.д. в консоль",
  "Damage tracking": "Отслеживание изменений",
  "[Hyprland]\nEnable damage tracking\nGenerally, leave it on.\nTurn off only when a shader doesn't work": "[Hyprland]\nВключить отслеживание изменений\nОбычно оставляйте включенным.\nОтключайте только если шейдер не работает",
  "Damage blink": "Мигание изменений",
  "[Hyprland] [Epilepsy warning!]\nShow screen damage flashes": "[Hyprland] [Осторожно, эпилепсия!]\nПоказывать вспышки изменений экрана",
  "Not all changes are saved": "Не все изменения сохранены",
  "Mo": "Пн",
  "Tu": "Вт",
  "We": "Ср",
  "Th": "Чт",
  "Fr": "Пт",
  "Sa": "Сб",
  "Su": "Вс",
  "Calendar": "Календарь",
  "To Do": "Задачи",
  "Unfinished": "Незавершенные",
  "Done": "Выполнено",
  "Finished tasks will go here": "Завершенные задачи появятся здесь",
  "Nothing here!": "Здесь пусто!",
  "+ New task": "+ Новая задача",
  "Add a task...": "Добавить задачу...",
  "Color scheme": "Цветовая схема",
  "Options": "Опции",
  "Dark Mode": "Темный режим",
  "Ya should go to sleep!": "Пора спать!",
  "Use Gradience": "Использовать Gradience",
  "Theme GTK apps using accent color\n(drawback: dark/light mode switching requires restart)": "Оформление GTK приложений с использованием акцентного цвета\n(недостаток: переключение темного/светлого режима требует перезапуска)",
  "Scheme styles": "Стили схемы",
  "Vibrant": "Яркий",
  "Vibrant+": "Яркий+",
  "Expressive": "Выразительный",
  "Monochrome": "Монохромный",
  "Rainbow": "Радуга",
  "Fidelity": "Точность",
  "Fruit Salad": "Фруктовый салат",
  "Tonal Spot": "Тональное пятно",
  "Content": "Содержимое",
  "Use arrow keys to navigate.\nEnter to select, Esc to cancel.": "Используйте стрелки для навигации.\nEnter для выбора, Esc для отмены.",
  "Lock": "Блокировка",
  "Logout": "Выход",
  "Sleep": "Сон",
  "Hibernate": "Гибернация",
  "Shutdown": "Выключение",
  "Reboot": "Перезагрузка",
  "Cancel": "Отмена",
  "Cheat sheet": "Шпаргалка",
  "Keybinds": "Горячие клавиши",
  "Essentials for beginners": "Основы для начинающих",
  "Make shell elements transparent": "Сделать элементы оболочки прозрачными",
  "Actions": "Действия",
  "Window management": "Управление окнами",
  "Window arrangement": "Расположение окон",
  "Workspace management": "Управление рабочими пространствами",
  "Workspace navigation": "Навигация по рабочим пространствам",
  "Widgets": "Виджеты",
  "Media": "Медиа",
  "Apps": "Приложения",
  "Neutral": "Нейтральный",
  "Launch foot (terminal)": "Запустить foot (терминал)",
  "Open app launcher": "Открыть запуск приложений",
  "Change wallpaper": "Изменить обои",
  "Clipboard history >> clipboard": "История буфера обмена >> буфер",
  "Pick emoji >> clipboard": "Выбрать эмодзи >> буфер",
  "Screen snip >> edit": "Снимок экрана >> редактировать",
  "Screen snip to text >> clipboard": "Снимок экрана в текст >> буфер",
  "Pick color (Hex) >> clipboard": "Выбрать цвет (Hex) >> буфер",
  "Screenshot >> clipboard": "Скриншот >> буфер",
  "Screenshot >> clipboard & file": "Скриншот >> буфер и файл",
  "Record region (no sound)": "Запись области (без звука)",
  "Record screen (with sound)": "Запись экрана (со звуком)",
  "Suspend system": "Приостановить систему",
  "Move focus in direction": "Переместить фокус в направлении",
  "Move window": "Переместить окно",
  "Resize window": "Изменить размер окна",
  "Close window": "Закрыть окно",
  "Pick and kill a window": "Выбрать и закрыть окно",
  "Window: move in direction": "Окно: переместить в направлении",
  "Window: split ratio +/- 0.1": "Окно: соотношение разделения +/- 0.1",
  "Float/unfloat window": "Плавающее/закрепленное окно",
  "Toggle fake fullscreen": "Переключить псевдополноэкранный режим",
  "Toggle fullscreen": "Переключить полноэкранный режим",
  "Toggle maximization": "Переключить развернутое состояние",
  "Focus workspace # (1, 2, 3, 4, ...)": "Фокус на рабочее пространство # (1, 2, 3, 4, ...)",
  "Workspace: focus left/right": "Рабочее пространство: фокус влево/вправо",
  "Workspace: toggle special": "Рабочее пространство: переключить специальное",
  "Window: move to workspace # (1, 2, 3, 4, ...)": "Окно: переместить на рабочее пространство # (1, 2, 3, 4, ...)",
  "Window: move to workspace left/right": "Окно: переместить на рабочее пространство влево/вправо",
  "Window: move to workspace special": "Окно: переместить на специальное рабочее пространство",
  "Window: pin (show on all workspaces)": "Окно: закрепить (показывать на всех рабочих пространствах)",
  "Restart widgets": "Перезапустить виджеты",
  "Cycle bar mode (normal, focus)": "Циклический режим панели (нормальный, фокус)",
  "Toggle overview/launcher": "Переключить обзор/запуск",
  "Show cheatsheet": "Показать шпаргалку",
  "Toggle left sidebar": "Переключить левую боковую панель",
  "Toggle right sidebar": "Переключить правую боковую панель",
  "Toggle music controls": "Переключить управление музыкой",
  "View color scheme and options": "Просмотр цветовой схемы и опций",
  "Toggle power menu": "Переключить меню питания",
  "Toggle crosshair": "Переключить перекрестие",
  "Next track": "Следующий трек",
  "Previous track": "Предыдущий трек",
  "Play/pause media": "Воспроизведение/пауза медиа",
  "Launch VSCode (editor)": "Запустить VSCode (редактор)",
  "Launch Nautilus (file manager)": "Запустить Nautilus (файловый менеджер)",
  "Launch Firefox (browser)": "Запустить Firefox (браузер)",
  "Launch GNOME Text Editor": "Запустить текстовый редактор GNOME",
  "Launch WPS Office": "Запустить WPS Office",
  "Launch GNOME Settings": "Запустить настройки GNOME",
  "Launch pavucontrol (volume mixer)": "Запустить pavucontrol (микшер громкости)",
  "Launch EasyEffects (equalizer & other audio effects)": "Запустить EasyEffects (эквалайзер и другие аудиоэффекты)",
  "Launch GNOME System monitor": "Запустить системный монитор GNOME",
  "Toggle fallback launcher: anyrun": "Переключить резервный запуск: anyrun",
  "Toggle fallback launcher: fuzzel": "Переключить резервный запуск: fuzzel",
  "Initialization complete!": "Инициализация завершена!",
  "Not found": "Не найдено:",
  "Calling API": "Вызов API",
  "Downloading image": "Загрузка изображения",
  "Finished!": "Готово!",
  "Error": "Ошибка",
  "Not found!": "Не найдено!",
  "Go to file url": "Перейти к URL файла",
  "Save image": "Сохранить изображение",
  "Hoard": "Сохранить",
  "Open externally": "Открыть внешне",
  "Failed to load config": "Не удалось загрузить конфигурацию",
  "You are an assistant on a sidebar of a Wayland Linux desktop. Please always use a casual tone when answering your questions, unless requested otherwise or making writing suggestions. These are the steps you should take to respond to the user's queries:\n1. If it's a writing- or grammar-related question or a sentence in quotation marks, Please point out errors and correct when necessary using underlines, and make the writing more natural where appropriate without making too major changes. If you're given a sentence in quotes but is grammatically correct, explain briefly concepts that are uncommon.\n2. If it's a question about system tasks, give a bash command in a code block with brief explanation.\n3. Otherwise, when asked to summarize information or explaining concepts, you are should use bullet points and headings. For mathematics expressions, you *have to* use LaTeX within a code block with the language set as \"latex\". \nNote: Use casual language, be short, while ensuring the factual correctness of your response. If you are unsure or don't have enough information to provide a confident answer, simply say "I don't know" or "I'm not sure.". \nThanks!": "Вы помощник на боковой панели рабочего стола Linux Wayland. Пожалуйста, всегда используйте непринужденный тон при ответе на вопросы, если не указано иное или не даются рекомендации по написанию. Вот шаги, которые вы должны предпринять для ответа на запросы пользователя:\n1. Если это вопрос, связанный с написанием или грамматикой, или предложение в кавычках, пожалуйста, укажите ошибки и исправьте при необходимости, используя подчеркивания, и сделайте написание более естественным, где это уместно, без внесения слишком серьезных изменений. Если вам дано предложение в кавычках, но оно грамматически правильное, кратко объясните необычные концепции.\n2. Если это вопрос о системных задачах, дайте команду bash в блоке кода с кратким объяснением.\n3. В остальных случаях, когда вас просят обобщить информацию или объяснить концепции, вы должны использовать маркированные списки и заголовки. Для математических выражений вы *должны* использовать LaTeX в блоке кода с указанием языка как \"latex\".\nПримечание: Используйте разговорный язык, будьте кратки, обеспечивая при этом фактическую правильность вашего ответа. Если вы не уверены или у вас недостаточно информации для уверенного ответа, просто скажите \"Я не знаю\" или \"Я не уверен\".\nСпасибо!"
}