{"No media": "مفيش وسائط", "Powered by Google": "مدعوم من جوجل", "Uses gemini-pro.\nNot affiliated, endorsed, or sponsored by Google.\n\nPrivacy: Chat messages aren't linked to your account,\n    but will be read by human reviewers to improve the model.": "بيستخدم gemini-pro.\nمفيش ارتباط، تأييد، أو رعاية من جوجل.\n\nالخصوصية: رسائل الدردشة مش مرتبطة بحسابك،\n    لكن هتتقرأ من مراجعين عشان يحسنوا النموذج.", "Precise": "دقيق", "Balanced": "متوازن", "Creative": "إبداعي", "Gemini's temperature value.\n  Precise = 0\n  Balanced = 0.5\n  Creative = 1": "قيمة حرارة Gemini.\n  دقيق = 0\n  متوازن = 0.5\n  إبداعي = 1", "Enhancements": "تحسينات", "Tells Gemini:\n- It's a Linux sidebar assistant\n- Be brief and use bullet points": "بيقول لـ Gemini:\n- ده مساعد شريط جانبي لـ Linux\n- خليك مختصر واستخدم النقاط.", "Safety": "السلامة", "When turned off, tells the API (not the model) \nto not block harmful/explicit content": "لما يتقفّل، بيقول للـ API (مش للنموذج)\nإنه مايمنعش المحتوى الضار/الصريح", "History": "السجل", "Saves chat history\nMessages in previous chats won't show automatically, but they are there": "بيحفظ سجل الدردشة\nالرسائل في الدردشات السابقة مش هتظهر تلقائيًا، لكنها موجودة.", "Key stored in:": "المفتاح محفوظ في:", "To update this key, type": "عشان تحدث المفتاح ده، اكتب", "Updated API Key at": "تم تحديث مفتاح الـ API في", "Currently using": "حاليًا بيستخدم", "Select ChatGPT-compatible API provider": "اختار مزود API متوافق مع ChatGPT", "Official OpenAI API.\nPricing: Free for the first $5 or 3 months, whichever is less.": "الـ API الرسمي لـ OpenAI.\nالتكلفة: مجانًا لأول $5 أو 3 شهور، أيهما أقل.", "Official Ollama API.\nPricing: Free.": "الـ API الرسمي لـ Ollama.\nالتكلفة: مجاني.", "A unified interface for LLMs": "واجهة موحدة لنماذج اللغة الكبيرة", "An API from Tornado Softwares\nPricing: Free: 100/day\nRequires you to join their Discord for a key": "API من Tornado Softwares\nالتكلفة: مجاني: 100/يوم\nيتطلب الانضمام إلى Discord للحصول على مفتاح.", "An API from @zukixa on GitHub.\nNote: Keys are IP-locked so it's buggy sometimes\nPricing: Free: 10/min, 800/day.\nRequires you to join their Discord for a key": "API من @zukixa على GitHub.\nملاحظة: المفاتيح مقيدة بـ IP فممكن تحصل أخطاء أحيانًا.\nالتكلفة: مجاني: 10/دقيقة، 800/يوم.\nيتطلب الانضمام إلى Discord للحصول على مفتاح.", "Provider shown above": "المزود المعروض فوق", "Uses gpt-3.5-turbo.\nNot affiliated, endorsed, or sponsored by OpenAI.\n\nPrivacy: OpenAI claims they do not use your data\nwhen you use their API. Idk about others.": "بيستخدم gpt-3.5-turbo.\nمش مرتبط، معتمد، أو مدعوم من OpenAI.\n\nالخصوصية: OpenAI بتقول إنها مش هتستخدم بياناتك\nلما تستخدم الـ API بتاعهم. مش عارف عن الباقي.", "The model's temperature value.\n  Precise = 0\n  Balanced = 0.5\n  Creative = 1": "قيمة حرارة النموذج.\n  دقيق = 0\n  متوازن = 0.5\n  إبداعي = 1", "An API key is required\nYou can grab one <u>here</u>, then enter it below": "مف<PERSON><PERSON><PERSON> API مطلوب\nممكن تاخده <u>هنا</u>، وبعد كده تدخله تحت.", "Tells the model:\n- It's a Linux sidebar assistant\n- Be brief and use bullet points": "بيقول للنموذج:\n- ده مساعد شريط جانبي لـ Linux\n- خليك مختصر واستخدم النقاط.", "Type tags for a random pic.\nNSFW content will not be returned unless\nyou explicitly request such a tag.\n\nDisclaimer: Not affiliated with the providers\nnor responsible for any of their content.": "اكتب علامات لصورة عشوائية.\nالمحتوى الغير لائق مش هيتعرض إلا لو\nطلبت العلامة دي صراحة.\n\nتنويه: مش مرتبط بالمزودين\nومش مسؤول عن محتواهم.", "Tags →": "علامات →", "Invalid command.": "أمر غير صالح.", "Powered by yande.re and konachan": "مدعوم من yande.re <PERSON><PERSON><PERSON>an", "Shows naughty stuff when enabled.\nYa like those? Add this to user_options.js:\n\t'sidebar': {\n\t'image': {\n\t\t'allowNsfw': true,\n\t}\n}": "بيعرض حاجات مش لائقة لما يتفعل.\nبتحب ده؟ ضيف ده لـ user_options.js:\n\t'sidebar': {\n\t'image': {\n\t\t'allowNsfw': true,\n\t}\n}", "Saves images in folders by their tags": "بيحفظ الصور في مجلدات حسب العلامات.", "<span strikethrough=\"true\">Inaccurate</span> Color picker": "<span strikethrough=\"true\">غير دقيق</span> اختيار الألوان", "Bluetooth | Right-click to configure": "بلوتوث | انقر باليمين عشان تضبط", "Wifi | Right-click to configure": "واي فاي | انقر باليمين عشان تضبط", "Right-click to configure": "انقر باليمين عشان تضبط", "Unknown": "مجهول", "Reload Environment config": "إعادة تحميل إعدادات البيئة", "Open Settings": "اف<PERSON><PERSON> الإعدادات", "Notifications": "الإشعارات", "Audio controls": "التحكم في الصوت", "Bluetooth": "بلوتوث", "Wifi networks": "شبكات واي فاي", "Live config": "إعدادات مباشرة", "Silence": "صمت", "Clear": "م<PERSON><PERSON>", "No notifications": "مفيش إشعارات", "notifications": "الإشعارات", "Close": "إغلاق", "Now": "الآن", "Yesterday": "امبارح", "No audio source": "مفيش مصدر صوت", "Remove device": "شيل الجهاز", "Connected": "متصل", "Paired": "مقترن", "More": "أكتر", "Selected": "<PERSON><PERSON><PERSON><PERSON>", "Current network": "الشبكة الحالية", "IP": "IP", "Name": "الاسم", "Toggle notifications": "فعل/إلغاء الإشعارات", "Cheat sheet": "ورقة الغش", "Essentials for beginners": "أساسيات للمبتدئين", "Launch foot (terminal)": "تشغيل Foot (المحطة الطرفية)", "Open app launcher": "فتح مشغل التطبيقات", "Change wallpaper": "تغيير الخلفية", "Random wallpaper": "خلفية عشوائية", "Actions": "الإجراءات", "Clipboard history >> clipboard": "تاريخ الحافظة >> الحافظة", "Pick emoji >> clipboard": "اختيار رمز تعبيري >> الحافظة", "Screen snip >> edit": "قص الشاشة >> تحرير", "Screen snip to text >> clipboard": "قص الشاشة إلى نص >> الحافظة", "Pick color (Hex) >> clipboard": "اختيار لون (Hex) >> الحافظة", "Screenshot": "لقطة شاشة", "Screenshot >> clipboard & file": "لقطة شاشة >> الحافظة والملف", "Record region (no sound)": "تسجيل منطقة (بدون صوت)", "Record screen (with sound)": "تسجيل الشاشة (مع صوت)", "Lock": "قفل", "Suspend system": "تعليق النظام", "Window management": "إدارة النوافذ", "Move focus in direction": "تحريك التركيز في الاتجاه", "Move window": "تحريك النافذة", "Resize window": "تغيير حجم النافذة", "Close window": "إغلاق النافذة", "Pick and kill a window": "اختيار وإغلاق نافذة", "Window arrangement": "ترتيب النوافذ", "Toggle fullscreen": "تبديل الشاشة الكاملة", "Toggle maximization": "تبديل التكبير", "Workspace navigation": "التنقل بين المساحات", "Focus workspace": "تركيز مساحة العمل", "Workspace management": "إدارة مساحات العمل", "Move to workspace": "نقل إلى مساحة العمل", "Pin (show on all workspaces)": "تثبيت (إظهار في جميع المساحات)", "Transparency": "شفافية", "Blur": "تشويش", "X-ray": "أشعة سينية", "Size": "حج<PERSON>", "Passes": "مرات العرض", "Animations": "رسوم متحركة", "Choreography delay": "تأخير التصميم", "Developer mode": "وضع المطور", "Show FPS": "عرض إطارات في الثانية", "Log to stdout": "تسجيل إلى الإخراج القياسي", "Damage tracking": "تتبع الأضرار", "Damage blink": "وميض الضرر", "Lewds": "محتوى للبالغين", "Save in folder by tags": "احفظ في مجلد حسب العلامات", "Message Gemini...": "أرسل رسالة إلى Gemini...", "Enter Google AI API Key...": "أدخل مفتاح Google AI API...", "Message the model...": "أرسل رسالة إلى النموذج...", "Enter API Key...": "أدخل مفتاح API...", "Enter tags": "أد<PERSON>ل العلامات", "Quick scripts": "سكربتات سريعة", "Change screen resolution": "غير دقة الشاشة", "Update packages": "حدّث الحزم", "Trim system generations to 5": "قص أجيال النظام لحد 5", "Trim home manager generations to 5": "قص أجيال مدير المنزل لحد 5", "Remove orphan packages": "شيل الحزم اليتيمة", "Uninstall unused flatpak packages": "افصل الحزم غير المستخدمة من flatpak", "Result": "النتيجة", "Type to search": "اكتب عشان تبحث", "illogical-impulse": "دافع غير منطقي", "RAM Usage": "استخدام الـ RAM", "Swap Usage": "استخدام <PERSON>wap", "CPU Usage": "استخدام الـ CPU", "Uptime:": "مدة التشغيل:", "Screen snip": "قص الشاشة", "Color picker": "اختيار الألوان", "Toggle on-screen keyboard": "فعل/إلغاء لوحة المفاتيح على الشاشة", "Night Light": "الضوء الليلي", "Keep system awake": "خلي النظام صاحي", "Cloudflare WARP": "Cloudflare WARP", "Session": "الجلسة", "Bluetooth | Right-click to": "بلوتوث | انقر باليمين عشان تضبط"}