{"No media": "<PERSON><PERSON>", "Powered by Google": "Bereitgestellt von Google", "Uses gemini-pro.\nNot affiliated, endorsed, or sponsored by Google.\n\nPrivacy: Chat messages aren't linked to your account,\n    but will be read by human reviewers to improve the model.": "Verwendet gemini-pro.\nNicht mit Google verbunden, unterstützt oder gesponsert.\n\nDatenschutz: Chat-Na<PERSON><PERSON>ten sind nicht mit Ihrem Konto verknü<PERSON>,\n    werden aber von menschlichen Prüfern gelesen, um das Modell zu verbessern.", "Precise": "Präzise", "Balanced": "Ausgewogen", "Creative": "K<PERSON><PERSON>v", "Gemini's temperature value.\n  Precise = 0\n  Balanced = 0.5\n  Creative = 1": "Geminis Temperaturwert.\n  Präzise = 0\n  Ausgewogen = 0.5\n  Kreativ = 1", "Enhancements": "Verbesserungen", "Tells Gemini:\n- It's a Linux sidebar assistant\n- Be brief and use bullet points": "Teilt Gemini mit:\n- Es ist ein Linux-Seitenleisten-Assistent\n- Sei kurz und verwende Aufzählungspunkte", "Safety": "Sicherheit", "When turned off, tells the API (not the model) \nto not block harmful/explicit content": "<PERSON><PERSON>, weist die API (nicht das Modell) an,\nschädliche/explizite Inhalte nicht zu blockieren", "History": "<PERSON><PERSON><PERSON><PERSON>", "Saves chat history\nMessages in previous chats won't show automatically, but they are there": "Speichert Chatverlauf\nNachrichten aus vorherigen Chats werden nicht automatisch angezeigt, sind aber vorhanden", "Key stored in:": "Schlüssel gespeichert in:", "To update this key, type": "Um diesen Schlüssel zu aktualisieren, geben <PERSON> ein", "Updated API Key at": "API-Schlüssel aktualisiert am", "Currently using": "Aktuell verwendet", "Select ChatGPT-compatible API provider": "Wählen Sie einen ChatGPT-kompatiblen API-Anbieter", "Official OpenAI API.\nPricing: Free for the first $5 or 3 months, whichever is less.": "Offizielle OpenAI-API.\nPreise: Kostenlos für die ersten $5 oder 3 <PERSON>te, je nachdem, was früher eintritt.", "Official Ollama API.\nPricing: Free.": "Offizielle Ollama-API.\nPreise: Ko<PERSON>los.", "A unified interface for LLMs": "Eine einheitliche Schnittstelle für LLMs", "An API from Tornado Softwares\nPricing: Free: 100/day\nRequires you to join their Discord for a key": "Eine API von Tornado Softwares\nPreise: Kostenlos: 100/Tag\nErfordert Discord-Beitritt für einen Schlüssel", "An API from @zukixa on GitHub.\nNote: Keys are IP-locked so it's buggy sometimes\nPricing: Free: 10/min, 800/day.\nRequires you to join their Discord for a key": "Eine API von @zukixa auf GitHub.\nHinweis: Schlüssel sind IP-gebunden, daher manchmal fehlerhaft\nPreise: Kostenlos: 10/Min, 800/Tag.\nErfordert Discord-Beitritt für einen Schlüssel", "Provider shown above": "<PERSON>ben angezeigter Anbieter", "Uses gpt-3.5-turbo.\nNot affiliated, endorsed, or sponsored by OpenAI.\n\nPrivacy: OpenAI claims they do not use your data\nwhen you use their API. Idk about others.": "Verwendet gpt-3.5-turbo.\nNicht mit OpenAI verbunden, unterstützt oder gesponsert.\n\nDatenschutz: OpenAI behauptet, sie verwenden Ihre Daten nicht,\nwenn Sie ihre API nutzen. Über andere weiß ich nichts.", "The model's temperature value.\n  Precise = 0\n  Balanced = 0.5\n  Creative = 1": "Der Temperaturwert des Modells.\n  Präzise = 0\n  Ausgewogen = 0.5\n  Kreativ = 1", "An API key is required\nYou can grab one <u>here</u>, then enter it below": "Ein API-<PERSON>hl<PERSON><PERSON> ist erforderlich\nSi<PERSON> können einen <u>hier</u> erhalten und dann unten eingeben", "Tells the model:\n- It's a Linux sidebar assistant\n- Be brief and use bullet points": "Teilt dem Modell mit:\n- Es ist ein Linux-Seitenleisten-Assistent\n- Sei kurz und verwende Aufzählungspunkte", "Type tags for a random pic.\nNSFW content will not be returned unless\nyou explicitly request such a tag.\n\nDisclaimer: Not affiliated with the providers\nnor responsible for any of their content.": "Geben Sie Tags für ein zufälliges Bild ein.\nNSFW-Inhalte werden nicht zurückgegeben, es sei denn,\n<PERSON>e fordern ausdrücklich einen solchen Tag an.\n\nHaftungsausschluss: Nicht mit den Anbietern verbunden\noder für deren Inhalte verantwortlich.", "Tags →": "Tags →", "Invalid command.": "Ung<PERSON><PERSON><PERSON> Befehl.", "Powered by yande.re and konachan": "Bereitgestellt von yande.re und konachan", "Lewds": "Freizügige Inhalte", "Shows naughty stuff when enabled.\nYa like those? Add this to user_options.js:\n\t'sidebar': {\n\t'image': {\n\t\t'allowNsfw': true,\n\t}\n}": "<PERSON><PERSON>gt freizügige Inhalte an, wenn aktiviert.\nMögen Sie das? Fügen Sie dies zu user_options.js hinzu:\n\t'sidebar': {\n\t'image': {\n\t\t'allowNsfw': true,\n\t}\n}", "Save in folder by tags": "In Ordner nach Tags speichern", "Saves images in folders by their tags": "Speichert Bilder in Ordnern nach ihren Tags", "Message Gemini...": "<PERSON><PERSON><PERSON><PERSON> an Gemini...", "Enter Google AI API Key...": "Google AI API-Schlüssel eingeben...", "Message the model...": "Na<PERSON><PERSON>t an das Modell...", "Enter API Key...": "API-Schlüssel eingeben...", "Enter tags": "Tags eingeben", "Quick scripts": "Schnellskripte", "Change screen resolution": "Bildschirmauflösung ändern", "Update packages": "Pakete aktualisieren", "Trim system generations to 5": "Systemgenerationen auf 5 kürzen", "Trim home manager generations to 5": "Home-Manager-Generation<PERSON> auf 5 kürzen", "Remove orphan packages": "Verwaiste Pakete entfernen", "Uninstall unused flatpak packages": "Unbenutzte Flatpak-Pakete deinstallieren", "<span strikethrough=\"true\">Inaccurate</span> Color picker": "<span strikethrough=\"true\">Ungenauer</span> Farbwähler", "Result": "<PERSON><PERSON><PERSON><PERSON>", "Type to search": "<PERSON><PERSON><PERSON> zum Suchen", "illogical-impulse": "unlogischer-impuls", "RAM Usage": "RAM-Nutzung", "Swap Usage": "Swap-Nutzung", "CPU Usage": "CPU-Nutzung", "Uptime:": "Betriebszeit:", "Screen snip": "Bildschirmausschnitt", "Color picker": "Farbwähler", "Toggle on-screen keyboard": "Bildschirmtastatur ein/aus", "Night Light": "<PERSON><PERSON><PERSON><PERSON>", "Keep system awake": "System wach halten", "Cloudflare WARP": "Cloudflare WARP", "Session": "Sit<PERSON>ng", "Bluetooth | Right-click to configure": "Bluetooth | Rechtsklick zum Konfigurieren", "Wifi | Right-click to configure": "WLAN | Rechtsklick zum Konfigurieren", "Right-click to configure": "Rechtsklick zum Konfigurieren", "Unknown": "Unbekannt", "Reload Environment config": "Umgebungskonfiguration neu laden", "Open Settings": "Einstellungen öffnen", "Notifications": "Benachrichtigungen", "Audio controls": "Audiosteuerung", "Bluetooth": "Bluetooth", "Wifi networks": "WLAN-Netzwerke", "Live config": "Live-Konfiguration", "Silence": "Stummschalten", "Clear": "Löschen", "No notifications": "<PERSON><PERSON>", "notifications": "Benachrichtigungen", "Close": "Schließen", "Now": "Jetzt", "Yesterday": "Gestern", "No audio source": "<PERSON><PERSON>", "Remove device": "Gerät entfernen", "Connected": "Verbunden", "Paired": "Gekoppelt", "More": "<PERSON><PERSON>", "Selected": "Ausgewählt", "Current network": "Aktuelles Netzwerk", "Authentication": "Authentifizierung", "Effects": "Effekte", "Transparency": "Transparenz", "[AGS]\nMake shell elements transparent\nBlur is also recommended if you enable this": "[AGS]\nShell-Elemente transparent machen\nUnschärfe wird ebenfalls empfohlen, wenn Sie dies aktivieren", "Blur": "Unschärfe", "[Hyprland]\nEnable blur on transparent elements\nDoesn't affect performance/power consumption unless you have transparent windows.": "[Hyprland]\nUnschärfe für transparente Elemente aktivieren\nBeeinflusst Leistung/Stromverbrauch nur bei transparenten Fenstern.", "X-ray": "<PERSON><PERSON><PERSON><PERSON>", "[Hyprland]\nMake everything behind a window/layer except the wallpaper not rendered on its blurred surface\nRecommended to improve performance (if you don't abuse transparency/blur) ": "[Hyprland]\nAlles hinter einem Fenster/einer Ebene außer dem Hintergrundbild nicht auf der unscharfen Oberfläche rendern\nEmpfohlen zur Leistungsverbesserung (wenn Sie Transparenz/Unschärfe nicht übermäßig nutzen) ", "Size": "Größe", "[Hyprland]\nAdjust the blur radius. Generally doesn't affect performance\nHigher = more color spread": "[Hyprland]\nUnschärferadius anpassen. Beeinflusst die Leistung normalerweise nicht\nHöher = mehr Farbverteilung", "Passes": "<PERSON><PERSON>g<PERSON><PERSON>", "[Hyprland] Adjust the number of runs of the blur algorithm\nMore passes = more spread and power consumption\n4 is recommended\n2- would look weird and 6+ would look lame.": "[Hyprland] An<PERSON><PERSON> der Durchläufe des Unschärfe-Algorithmus anpassen\nMehr Durchgänge = mehr Verteilung und Stromverbrauch\n4 wird empfohlen\n2- würde seltsam und 6+ würde schwach aussehen.", "Animations": "<PERSON><PERSON>", "[Hyprland] [GTK]\nEnable animations": "[Hyprland] [GTK]\nAnimationen aktivieren", "Choreography delay": "Choreografie-Verzögerung", "In milliseconds, the delay between animations of a series": "In Millisekunden, die Verzögerung zwischen Animationen einer Serie", "Developer": "<PERSON><PERSON><PERSON><PERSON>", "Show FPS": "FPS anzeigen", "[Hyprland]\nShow FPS overlay on top-left corner": "[Hyprland]\nFPS-Overlay in der oberen linken Ecke anzeigen", "Log to stdout": "In stdout protokollieren", "[Hyprland]\nPrint LOG, ERR, WARN, etc. messages to the console": "[Hyprland]\nLOG-, ERR-, WARN- usw. Nachrichten in der Konsole ausgeben", "Damage tracking": "Schadenverfolgung", "[Hyprland]\nEnable damage tracking\nGenerally, leave it on.\nTurn off only when a shader doesn't work": "[<PERSON>y<PERSON><PERSON>]\nSchadenverfolgung aktivieren\nGenerell eingeschaltet lassen.\n<PERSON><PERSON> ausschal<PERSON>, wenn ein <PERSON>r nicht funktioniert", "Damage blink": "Schadensblinken", "[Hyprland] [Epilepsy warning!]\nShow screen damage flashes": "[Hyprland] [Epilepsie<PERSON><PERSON>nung!]\nBildschirmschadenblitze anzeigen", "Not all changes are saved": "Nicht alle Änderungen sind gespeichert", "Mo": "Mo", "Tu": "Di", "We": "<PERSON>", "Th": "Do", "Fr": "Fr", "Sa": "Sa", "Su": "So", "Calendar": "<PERSON><PERSON><PERSON>", "To Do": "Aufgaben", "Unfinished": "Unfertig", "Done": "<PERSON><PERSON><PERSON><PERSON>", "Finished tasks will go here": "Erledigte Aufgaben erscheinen hier", "Nothing here!": "Nichts hier!", "+ New task": "+ Neue Aufgabe", "Add a task...": "Aufgabe hinzufügen...", "Color scheme": "Farbschema", "Options": "Optionen", "Dark Mode": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Ya should go to sleep!": "Du solltest schlafen gehen!", "Use Gradience": "Grad<PERSON> verwenden", "Theme GTK apps using accent color\n(drawback: dark/light mode switching requires restart)": "GTK-Apps mit Akzentfarbe gestalten\n(Nachteil: Umschalten zwischen Hell-/Dunkelmodus erfordert Neustart)", "Scheme styles": "Schema-Stile", "Vibrant": "Lebendig", "Vibrant+": "Lebendig+", "Expressive": "Ausdrucksstark", "Monochrome": "Einfarbig", "Rainbow": "Regenbogen", "Fidelity": "Genauigkeit", "Fruit Salad": "Obstsalat", "Tonal Spot": "<PERSON><PERSON><PERSON>", "Content": "Inhalt", "Use arrow keys to navigate.\nEnter to select, Esc to cancel.": "Pfeiltasten zum Navigieren verwenden.\nEnter zum Auswählen, Esc zum Abbrechen.", "Lock": "<PERSON><PERSON><PERSON>", "Logout": "Abmelden", "Sleep": "R<PERSON><PERSON>ust<PERSON>", "Hibernate": "R<PERSON><PERSON>ust<PERSON>", "Shutdown": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Reboot": "Neustart", "Cancel": "Abbrechen", "Cheat sheet": "Spickzettel", "Keybinds": "Tastenkombinationen", "Essentials for beginners": "Grundlagen für Anfänger", "Make shell elements transparent": "Shell-Elemente transparent machen", "Actions": "Aktionen", "Window management": "Fensterverwaltung", "Window arrangement": "Fensteranordnung", "Workspace management": "Arbeitsbereichsverwaltung", "Workspace navigation": "Arbeitsbereichsnavigation", "Widgets": "Widgets", "Media": "Medien", "Apps": "Anwendungen", "Neutral": "Neutral", "Launch foot (terminal)": "Foot (Terminal) starten", "Open app launcher": "App-<PERSON><PERSON>", "Change wallpaper": "Hintergrundbild ändern", "Clipboard history >> clipboard": "Zwischenablage-Verlauf >> Zwischenablage", "Pick emoji >> clipboard": "Emoji auswählen >> Zwischenablage", "Screen snip >> edit": "Bildschirmausschnitt >> bearbeiten", "Screen snip to text >> clipboard": "Bildschirmausschnitt zu Text >> Zwischenablage", "Pick color (Hex) >> clipboard": "Farbe auswählen (Hex) >> Zwischenablage", "Screenshot >> clipboard": "Screenshot >> Zwischenablage", "Screenshot >> clipboard & file": "Screenshot >> Zwischenablage & Datei", "Record region (no sound)": "Region aufnehmen (ohne Ton)", "Record screen (with sound)": "Bildschirm aufnehmen (mit Ton)", "Suspend system": "System aussetzen", "Move focus in direction": "Fokus in Richtung bewegen", "Move window": "Fenster verschieben", "Resize window": "Fenstergröße ändern", "Close window": "Fenster schließen", "Pick and kill a window": "Fenster auswählen und beenden", "Window: move in direction": "Fenster: in Richtung bewegen", "Window: split ratio +/- 0.1": "Fenster: Teilungsverhältnis +/- 0.1", "Float/unfloat window": "Fenster schweben/fixieren", "Toggle fake fullscreen": "Falschen Vollbildmodus umschalten", "Toggle fullscreen": "Vollbildmo<PERSON>", "Toggle maximization": "Maximierung umschalten", "Focus workspace # (1, 2, 3, 4, ...)": "Arbeitsbereich fokussieren # (1, 2, 3, 4, ...)", "Workspace: focus left/right": "Arbeitsbereich: links/rechts fokussieren", "Workspace: toggle special": "Arbeitsbereich: Spezial umschalten", "Window: move to workspace # (1, 2, 3, 4, ...)": "Fenster: zu Arbeitsbereich # verschieben (1, 2, 3, 4, ...)", "Window: move to workspace left/right": "Fenster: zu Arbeitsbereich links/rechts verschieben", "Window: move to workspace special": "Fenster: zu speziellem Arbeitsbereich verschieben", "Window: pin (show on all workspaces)": "Fenster: anheften (auf allen Arbeitsbereichen zeigen)", "Restart widgets": "Widgets neu starten", "Cycle bar mode (normal, focus)": "Leistenmo<PERSON> wechseln (normal, Fokus)", "Toggle overview/launcher": "Übersicht/Starter umschalten", "Show cheatsheet": "Spickzettel anzeigen", "Toggle left sidebar": "Linke Seitenleiste umschalten", "Toggle right sidebar": "Rechte Seitenleiste umschalten", "Toggle music controls": "Musiksteuerung umschalten", "View color scheme and options": "Farbschema und Optionen anzeigen", "Toggle power menu": "Energiemenü umschalten", "Toggle crosshair": "Fadenkreuz umschalten", "Next track": "Nächster Titel", "Previous track": "<PERSON><PERSON><PERSON><PERSON>", "Play/pause media": "Medien abspielen/pausieren", "Launch VSCode (editor)": "<PERSON><PERSON><PERSON> (Editor) starten", "Launch Nautilus (file manager)": "<PERSON><PERSON><PERSON> (Dateimanager) starten", "Launch Firefox (browser)": "Firefox (Browser) starten", "Launch GNOME Text Editor": "GNOME Texteditor starten", "Launch WPS Office": "WPS Office starten", "Launch GNOME Settings": "GNOME Einstellungen starten", "Launch pavucontrol (volume mixer)": "Pavucontrol (Lautstärkemixer) starten", "Launch EasyEffects (equalizer & other audio effects)": "EasyEffects (Equalizer & andere Audioeffekte) starten", "Launch GNOME System monitor": "GNOME Systemmonitor starten", "Toggle fallback launcher: anyrun": "Fallback-Starter <PERSON><PERSON><PERSON><PERSON>: anyrun", "Toggle fallback launcher: fuzzel": "Fallback-Starter <PERSON><PERSON><PERSON><PERSON>: fuzzel", "Initialization complete!": "Initialisierung abgeschlossen!", "Not found": "Nicht gefunden:", "Calling API": "API wird aufgerufen", "Downloading image": "Bild wird herunt<PERSON><PERSON><PERSON>n", "Finished!": "Fertig!", "Error": "<PERSON><PERSON>", "Not found!": "Nicht gefunden!", "Go to file url": "<PERSON><PERSON> Datei-URL gehen", "Save image": "Bild speichern", "Hoard": "Sammeln", "Open externally": "Extern ö<PERSON>nen", "Failed to load config": "Konfiguration konnte nicht geladen werden", "You are an assistant on a sidebar of a Wayland Linux desktop. Please always use a casual tone when answering your questions, unless requested otherwise or making writing suggestions. These are the steps you should take to respond to the user's queries:\n1. If it's a writing- or grammar-related question or a sentence in quotation marks, Please point out errors and correct when necessary using underlines, and make the writing more natural where appropriate without making too major changes. If you're given a sentence in quotes but is grammatically correct, explain briefly concepts that are uncommon.\n2. If it's a question about system tasks, give a bash command in a code block with brief explanation.\n3. Otherwise, when asked to summarize information or explaining concepts, you are should use bullet points and headings. For mathematics expressions, you *have to* use LaTeX within a code block with the language set as \"latex\". \nNote: Use casual language, be short, while ensuring the factual correctness of your response. If you are unsure or don't have enough information to provide a confident answer, simply say \"I don't know\" or \"I'm not sure.\". \nThanks!": "Du bist ein Assistent in einer Seitenleiste eines Wayland Linux-Desktops. Bitte verwende immer einen lockeren Ton beim <PERSON>t<PERSON>rten deiner Fragen, es sei denn, es wird anders gewünscht oder es geht um Schreibvorschläge. Dies sind die Schritte, die du bei der Beantwortung der Benutzeranfragen befolgen solltest:\n1. Wenn es sich um eine Frage zur Schreibweise oder Grammatik oder einen Satz in Anführungszeichen handelt, weise bitte auf Fehler hin und korrigiere sie bei Bedarf mit Unterstreichungen. Mache die Schreibweise natürlicher, wo es angebracht ist, ohne zu große Änderungen vorzunehmen. Wenn dir ein Satz in Anführungszeichen gegeben wird, der grammatikalisch korrekt ist, erkläre kurz ungewöhnliche Konzepte.\n2. Wenn es sich um eine Frage zu Systemaufgaben handelt, gib einen Bash-Be<PERSON>hl in einem Codeblock mit kurzer Erklärung.\n3. <PERSON><PERSON>falls, wenn du gebeten wirst, Informationen zusammenzufassen oder Konzepte zu erklären, solltest du Aufzählungspunkte und Überschriften verwenden. Für mathematische Ausdrücke *musst* du LaTeX innerhalb eines Codeblocks verwenden, wobei die Sprache als \"latex\" festgelegt ist.\nHinweis: Verwende eine lockere Sprache, sei kurz, während du die sachliche Richtigkeit deiner Antwort sicherstellst. Wenn du unsicher bist oder nicht genügend Informationen für eine sichere Antwort hast, sage einfach \"Ich weiß es nicht\" oder \"Ich bin mir nicht sicher\".\nDanke!"}