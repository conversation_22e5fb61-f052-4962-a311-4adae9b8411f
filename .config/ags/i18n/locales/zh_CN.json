{"No media": "无媒体活动", "Powered by Google": "由 Google 提供技术支持", "Uses gemini-pro.\nNot affiliated, endorsed, or sponsored by Google.\n\nPrivacy: Chat messages aren't linked to your account,\n    but will be read by human reviewers to improve the model.": "使用 Gemini Pro\n不隶属于、不受 Google 赞助或支持。\n\n隐私：聊天信息不会与你的账户关联，\n但会被人类审阅者阅读，用于改进模型。", "Precise": "精确", "Balanced": "平衡", "Creative": "创意", "Gemini's temperature value.\n  Precise = 0\n  Balanced = 0.5\n  Creative = 1": "Gemini 的 temperature 值\n  精确 = 0\n  平衡 = 0.5\n  创意 = 1", "Enhancements": "增强功能", "Tells Gemini:\n- It's a Linux sidebar assistant\n- Be brief and use bullet points": "告诉 Gemini：\n- 它是一个 Linux 侧边栏助手\n- 保持简洁并使用项目符号", "Safety": "安全", "When turned off, tells the API (not the model) \nto not block harmful/explicit content": "当关闭时，告诉 API（而不是模型）\n不要屏蔽有害/显露的内容", "History": "历史", "Saves chat history\nMessages in previous chats won't show automatically, but they are there": "保存聊天历史\n以前聊天中的消息不会自动显示，但它们仍然存在", "Key stored in:": "密钥值储存在：", "To update this key, type": "要更新此密钥，请输入", "Updated API Key at": "更新了 API 密钥于", "Currently using": "当前使用", "Select ChatGPT-compatible API provider": "选择与 ChatGPT 兼容的 API 提供商", "Official OpenAI API.\nPricing: Free for the first $5 or 3 months, whichever is less.": "官方 OpenAI API。\n定价：前 $5 或前 3 个月免费，取较小者。", "Official Ollama API.\nPricing: Free.": "官方 Ollama API。\n定价：免费。", "A unified interface for LLMs": "LLM 的统一接口", "An API from Tornado Softwares\nPricing: Free: 100/day\nRequires you to join their Discord for a key": "来自 Tornado Softwares 的 API\n定价：免费：每天 100 次请求\n需要加入他们的 Discord 以获取密钥", "An API from @zukixa on GitHub.\nNote: Keys are IP-locked so it's buggy sometimes\nPricing: Free: 10/min, 800/day.\nRequires you to join their Discord for a key": "来自 GitHub 上的 @zukixa 的 API。\n注意：密钥与 IP 绑定，所以有时会出错。\n定价：免费：每分钟 10 次，每天 800 次。\n需要加入他们的 Discord 才能获得密钥。", "Provider shown above": "上述显示的提供商", "Uses gpt-3.5-turbo.\nNot affiliated, endorsed, or sponsored by OpenAI.\n\nPrivacy: OpenAI claims they do not use your data\nwhen you use their API. Idk about others.": "使用 gpt-3.5-turbo。\n与 OpenAI 无关联，未获得其认可或赞助。\n\n隐私：OpenAI 声明，当您使用他们的 API 时，他们不会使用您的数据。\n我不清楚其他人的情况。", "The model's temperature value.\n  Precise = 0\n  Balanced = 0.5\n  Creative = 1": "模型的 temperature 值。\n  精确 = 0\n  平衡 = 0.5\n  创意 = 1", "An API key is required\nYou can grab one <u>here</u>, then enter it below": "需要 API 密钥\n您可以在<u>这里</u>获取一个，然后在下面输入", "Tells the model:\n- It's a Linux sidebar assistant\n- Be brief and use bullet points": "告诉模型：\n- 它是一个 Linux 侧边栏助手\n- 保持简洁并使用项目符号", "Type tags for a random pic.\nNSFW content will not be returned unless\nyou explicitly request such a tag.\n\nDisclaimer: Not affiliated with the providers\nnor responsible for any of their content.": "输入标签以获取随机图片。\n除非您明确请求，否则不会返回 NSFW 内容。\n\n免责声明：与提供商无关联\n我也不对他们的任何内容负责。", "Tags →": "标签 →", "Invalid command.": "无效命令。", "Powered by yande.re and konachan": "由 yande.re 和 konachan 提供支持", "Lewds": "不雅内容", "Shows naughty stuff when enabled.\nYa like those? Add this to user_options.js:\n\t'sidebar': {\n\t'image': {\n\t\t'allowNsfw': true,\n\t}\n}": "启用时显示不雅内容。\n你喜欢这些？添加到 user_options.js 中：\n'sidebar': {\n\t'image': {\n\t\t'allowNsfw': true,\n\t}\n}", "Save in folder by tags": "按标签保存到文件夹", "Saves images in folders by their tags": "按标签将图片保存到文件夹中", "Message Gemini...": "向 Gemini 发送消息...", "Enter Google AI API Key...": "输入 Google AI API 密钥...", "Message the model...": "向模型发送消息...", "Enter API Key...": "输入 API 密钥...", "Enter tags": "输入标签", "Quick scripts": "快速脚本", "Change screen resolution": "更改屏幕分辨率", "Update packages": "更新软件包", "Trim system generations to 5": "将系统代数修剪为 5", "Trim home manager generations to 5": "将 home manager 代数修剪为 5", "Remove orphan packages": "移除孤立软件包", "Uninstall unused flatpak packages": "卸载未使用的 Flatpak 软件包", "<span strikethrough=\"true\">Inaccurate</span> Color picker": "<span strikethrough=\"true\">不准确</span> 颜色选择器", "Result": "结果", "Type to search": "输入以搜索", "illogical-impulse": "illogical-impulse", "RAM Usage": "RAM 使用情况", "Swap Usage": "Swap 使用情况", "CPU Usage": "CPU 使用情况", "Uptime:": "运行时间:", "Screen snip": "屏幕截图", "Color picker": "颜色选择器", "Toggle on-screen keyboard": "切换屏幕键盘", "Night Light": "夜灯", "Keep system awake": "保持系统唤醒", "Cloudflare WARP": "Cloudflare WARP", "Session": "会话", "Bluetooth | Right-click to configure": "蓝牙 | 右键单击以配置", "Wifi | Right-click to configure": "Wi-Fi | 右键单击以配置", "Right-click to configure": "右键单击以配置", "Unknown": "未知", "Reload Environment config": "重新加载环境配置", "Open Settings": "打开设置", "Notifications": "通知", "Audio controls": "音频控制", "Bluetooth": "蓝牙", "Wifi networks": "Wi-Fi 网络", "Live config": "实时配置", "Silence": "静音", "Clear": "清除", "No notifications": "无通知", "notifications": "条通知", "Close": "关闭", "Now": "现在", "Yesterday": "昨天", "No audio source": "没有音频源", "Remove device": "移除设备", "Connected": "已连接", "Paired": "已配对", "More": "更多", "Selected": "已选中", "Current network": "当前网络", "Authentication": "身份验证", "Effects": "效果", "Transparency": "透明度", "[AGS]\nMake shell elements transparent\nBlur is also recommended if you enable this": "[AGS]\n使外壳元素透明\n如果启用此功能，也建议使用模糊效果", "Blur": "模糊", "[Hyprland]\nEnable blur on transparent elements\nDoesn't affect performance/power consumption unless you have transparent windows.": "[Hyprland]\n在透明元素上启用模糊效果\n除非您有透明窗口，否则不会影响性能/功耗。", "X-ray": "X-ray", "[Hyprland]\nMake everything behind a window/layer except the wallpaper not rendered on its blurred surface\nRecommended to improve performance (if you don't abuse transparency/blur) ": "[Hyprland]\n使窗口/图层后面的所有内容（除了壁纸）在其模糊表面上不渲染\n建议提高性能（如果您不滥用透明度/模糊）", "Size": "大小", "[Hyprland]\nAdjust the blur radius. Generally doesn't affect performance\nHigher = more color spread": "[Hyprland]\n调整模糊半径。通常不会影响性能\n数值越高 = 颜色扩散越大", "Passes": "次数", "[Hyprland] Adjust the number of runs of the blur algorithm\nMore passes = more spread and power consumption\n4 is recommended\n2- would look weird and 6+ would look lame.": "[Hyprland]\n调整模糊算法的运行次数\n次数越多 = 扩散越大，功耗越高\n建议使用 4 次\n2 次看起来很奇怪，6 次以上看起来很糟糕。", "Animations": "动画", "[Hyprland] [GTK]\nEnable animations": "[Hyprland] [GTK]\n启用动画", "Choreography delay": "间隔", "In milliseconds, the delay between animations of a series": "以毫秒为单位，一系列动画之间的延迟", "Developer": "开发者选项", "Show FPS": "显示 FPS", "[Hyprland]\nShow FPS overlay on top-left corner": "[Hyprland]\n在左上角显示 FPS 叠加层", "Log to stdout": "输出日志", "[Hyprland]\nPrint LOG, ERR, WARN, etc. messages to the console": "[Hyprland]\n将 LOG、ERR、WARN 等消息打印到控制台", "Damage tracking": "Damage tracking", "[Hyprland]\nEnable damage tracking\nGenerally, leave it on.\nTurn off only when a shader doesn't work": "[Hyprland]\n启用 Damage tracking \n通常情况下，保持启用状态\n仅当着色器无法正常工作时才禁用", "Damage blink": "显示视图更新", "[Hyprland] [Epilepsy warning!]\nShow screen damage flashes": "[Hyprland] [癫痫警告！]\n屏幕视图更新时闪烁", "Not all changes are saved": "并非所有更改都已保存", "Mo": "一", "Tu": "二", "We": "三", "Th": "四", "Fr": "五", "Sa": "六", "Su": "日", "Calendar": "日历", "To Do": "待办", "Unfinished": "未完成", "Done": "已完成", "Finished tasks will go here": "已完成的任务将显示在此处", "Nothing here!": "这里什么也没有！", "+ New task": "+ 新任务", "Add a task...": "添加任务...", "Color scheme": "配色方案", "Options": "选项", "Dark Mode": "深色模式", "Ya should go to sleep!": "你应该去睡觉！", "Use Gradience": "GTK 主题", "Theme GTK apps using accent color\n(drawback: dark/light mode switching requires restart)": "使用强调色对 GTK 应用程序进行主题化\n（缺点：深色/浅色模式切换需要重启）", "Scheme styles": "样式方案", "Vibrant": "鲜艳", "Vibrant+": "鲜艳+", "Expressive": "表现力", "Monochrome": "黑白", "Rainbow": "彩虹", "Fidelity": "保真度", "Fruit Salad": "水果沙拉", "Tonal Spot": "色调点", "Content": "内容", "Use arrow keys to navigate.\nEnter to select, Esc to cancel.": "使用箭头键导航。\n回车键选择，Esc 键取消。", "Lock": "锁屏", "Logout": "注销", "Sleep": "睡眠", "Hibernate": "休眠", "Shutdown": "关机", "Reboot": "重启", "Cancel": "取消", "Cheat sheet": "备忘单", "Keybinds": "按键绑定", "Essentials for beginners": "初学者必备", "Make shell elements transparent": "使外壳元素透明", "Actions": "操作", "Window management": "窗口管理", "Window arrangement": "窗口排列", "Workspace management": "工作区管理", "Workspace navigation": "工作区导航", "Widgets": "小部件", "Media": "媒体", "Apps": "应用程序", "Neutral": "中性", "Launch foot (terminal)": "启动终端（foot）", "Open app launcher": "打开应用程序启动器", "Change wallpaper": "更改壁纸", "Clipboard history >> clipboard": "剪贴板历史 >> 剪贴板", "Pick emoji >> clipboard": "选择表情符号 >> 剪贴板", "Screen snip >> edit": "屏幕截图 >> 编辑", "Screen snip to text >> clipboard": "屏幕截图转文字 >> 剪贴板", "Pick color (Hex) >> clipboard": "选择颜色（十六进制）>> 剪贴板", "Screenshot >> clipboard": "屏幕截图 >> 剪贴板", "Screenshot >> clipboard & file": "屏幕截图 >> 剪贴板和文件", "Record region (no sound)": "录制区域（无声音）", "Record screen (with sound)": "录制屏幕（带声音）", "Suspend system": "挂起系统", "Move focus in direction": "在方向上移动焦点", "Move window": "移动窗口", "Resize window": "调整窗口大小", "Close window": "关闭窗口", "Pick and kill a window": "选择并关闭一个窗口", "Window: move in direction": "窗口：在方向上移动", "Window: split ratio +/- 0.1": "窗口：分割比例 +/- 0.1", "Float/unfloat window": "浮动/取消浮动窗口", "Toggle fake fullscreen": "切换伪全屏", "Toggle fullscreen": "切换全屏", "Toggle maximization": "切换最大化", "Focus workspace # (1, 2, 3, 4, ...)": "聚焦工作区 #（1, 2, 3, 4, ...）", "Workspace: focus left/right": "工作区：聚焦左右", "Workspace: toggle special": "工作区：切换特殊工作区", "Window: move to workspace # (1, 2, 3, 4, ...)": "窗口：移动到工作区 #（1, 2, 3, 4, ...）", "Window: move to workspace left/right": "窗口：移动到左右工作区", "Window: move to workspace special": "窗口：移动到特殊工作区", "Window: pin (show on all workspaces)": "窗口：固定（在所有工作区显示）", "Restart widgets": "重启小部件", "Cycle bar mode (normal, focus)": "循环栏模式（正常，聚焦）", "Toggle overview/launcher": "切换概览/启动器", "Show cheatsheet": "显示快捷键表", "Toggle left sidebar": "切换左侧边栏", "Toggle right sidebar": "切换右侧边栏", "Toggle music controls": "切换音乐控制", "View color scheme and options": "查看配色方案和选项", "Toggle power menu": "切换电源菜单", "Toggle crosshair": "切换准星", "Next track": "下一曲目", "Previous track": "上一曲目", "Play/pause media": "播放/暂停媒体", "Launch VSCode (editor)": "启动 VSCode（编辑器）", "Launch Nautilus (file manager)": "启动 Nautilus（文件管理器）", "Launch Firefox (browser)": "启动 Firefox（浏览器）", "Launch GNOME Text Editor": "启动 GNOME 文本编辑器", "Launch WPS Office": "启动 WPS 办公软件", "Launch GNOME Settings": "启动 GNOME 设置", "Launch pavucontrol (volume mixer)": "启动 pavucontrol（音量混合器）", "Launch EasyEffects (equalizer & other audio effects)": "启动 EasyEffects（均衡器和其他音频效果）", "Launch GNOME System monitor": "启动 GNOME 系统监视器", "Toggle fallback launcher: anyrun": "切换备用启动器：anyrun", "Toggle fallback launcher: fuzzel": "切换备用启动器：fuzzel", "Initialization complete!": "初始化完成！", "Not found": "未找到", "Calling API": "调用 API", "Downloading image": "正在下载图片", "Finished!": "完成！", "Error": "错误", "Not found!": "未找到！", "Go to file url": "前往文件链接", "Save image": "保存图片", "Hoard": "保存", "Open externally": "在外部打开", "You are an assistant on a sidebar of a Wayland Linux desktop. Please always use a casual tone when answering your questions, unless requested otherwise or making writing suggestions. These are the steps you should take to respond to the user's queries:\n1. If it's a writing- or grammar-related question or a sentence in quotation marks, Please point out errors and correct when necessary using underlines, and make the writing more natural where appropriate without making too major changes. If you're given a sentence in quotes but is grammatically correct, explain briefly concepts that are uncommon.\n2. If it's a question about system tasks, give a bash command in a code block with brief explanation.\n3. Otherwise, when asked to summarize information or explaining concepts, you are should use bullet points and headings. For mathematics expressions, you *have to* use LaTeX within a code block with the language set as \"latex\". \nNote: Use casual language, be short, while ensuring the factual correctness of your response. If you are unsure or don’t have enough information to provide a confident answer, simply say “I don’t know” or “I’m not sure.”. \nThanks!": "你是 Wayland Linux 桌面侧边栏上的助手。除非有其他要求或提供建议，否则请始终保持轻松的语气回答问题。这是你回答用户查询的步骤：\n1. 如果是写作或语法相关的问题，或者引号中的句子，请指出错误并在必要时进行更正，使用下划线，并在适当的地方使写作更自然，不要进行太大更改。如果你给出的句子在引号中但语法正确，请简要解释不常见概念。\n2. 如果是关于系统任务的问题，请给出bash命令，并在代码块中简要说明。\n3. 否则，在总结信息或解释概念时，你应该使用项目符号和标题。对于数学表达式，你必须在代码块中使用 LaTeX，并将语言设置为\"latex\"。\n注意：使用轻松的语言，简洁，同时确保回答的事实正确性。如果你不确定或没有足够的信息来提供自信的答案，只需说“我不知道”或“我不确定”。\n谢谢！"}