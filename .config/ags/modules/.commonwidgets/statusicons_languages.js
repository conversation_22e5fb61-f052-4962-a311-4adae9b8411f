// For keyboard layout in statusicons.js
// This list is not exhaustive. It just includes known/possible languages of users of my dotfiles
// Add your language here if you use multi-lang xkb input. El<PERSON>, ignore
// Note that something like "French (Canada)" should go before "French"
//                      and "English (US)" should go before "English"
export const languages = [
  {
    layout: "us",
    name: "English (US)",
    flag: "🇺🇸",
  },
  {
    layout: "ru",
    name: "Russian",
    flag: "🇷🇺",
  },
  {
    layout: "pl",
    name: "Polish",
    flag: "🇷🇵🇵🇱",
  },
  {
    layout: "ro",
    name: "Romanian",
    flag: "🇷🇴",
  },
  {
    layout: "ca",
    name: "French (Canada)",
    flag: "🇫🇷",
  },
  {
    layout: "fr",
    name: "French",
    flag: "🇫🇷",
  },
  {
    layout: "tr",
    name: "Turkish",
    flag: "🇹🇷",
  },
  {
    layout: "jp",
    name: "Japanese",
    flag: "🇯🇵",
  },
  {
    layout: "cn",
    name: "Chinese",
    flag: "🇨🇳",
  },
  {
    layout: "vn",
    name: "Vietnamese",
    flag: "🇻🇳",
  },
  {
    layout: "undef",
    name: "Undefined",
    flag: "🧐",
  },
  {
    layout: "ara",
    name: "Arabic (EG)",
    flag: "🇪🇬",
  },
  {
    layout: "de",
    name: "German",
    flag: "🇩🇪",
  },
  {
    layout: "es",
    name: "Spanish",
    flag: "🇪🇸",
  },
  {
    layout: "pt",
    name: "Portuguese",
    flag: "🇵🇹",
  },
  {
    layout: "pt_br",
    name: "Portuguese (Brazil)",
    flag: "🇧🇷",
  },
  {
    layout: "it",
    name: "Italian",
    flag: "🇮🇹",
  },
  {
    layout: "nl",
    name: "Dutch",
    flag: "🇳🇱",
  },
  {
    layout: "se",
    name: "Swedish",
    flag: "🇸🇪",
  },
  {
    layout: "no",
    name: "Norwegian",
    flag: "🇳🇴",
  },
  {
    layout: "fi",
    name: "Finnish",
    flag: "🇫🇮",
  },
  {
    layout: "dk",
    name: "Danish",
    flag: "🇩🇰",
  },
  {
    layout: "gr",
    name: "Greek",
    flag: "🇬🇷",
  },
  {
    layout: "il",
    name: "Hebrew",
    flag: "🇮🇱",
  },
  {
    layout: "kr",
    name: "Korean",
    flag: "🇰🇷",
  },
  {
    layout: "ua",
    name: "Ukrainian",
    flag: "🇺🇦",
  },
  {
    layout: "bg",
    name: "Bulgarian",
    flag: "🇧🇬",
  },
  {
    layout: "th",
    name: "Thai",
    flag: "🇹🇭",
  },
  {
    layout: "ir",
    name: "Persian",
    flag: "🇮🇷",
  },
  {
    layout: "in",
    name: "Hindi",
    flag: "🇮🇳",
  },
  {
    layout: "cz",
    name: "Czech",
    flag: "🇨🇿",
  },
  {
    layout: "sk",
    name: "Slovak",
    flag: "🇸🇰",
  },
  {
    layout: "hu",
    name: "Hungarian",
    flag: "🇭🇺",
  },
  {
    layout: "be",
    name: "Belgian",
    flag: "🇧🇪",
  },
  {
    layout: "hr",
    name: "Croatian",
    flag: "🇭🇷",
  },
  {
    layout: "si",
    name: "Slovenian",
    flag: "🇸🇮",
  },
  {
    layout: "rs",
    name: "Serbian",
    flag: "🇷🇸",
  },
  {
    layout: "mk",
    name: "Macedonian",
    flag: "🇲🇰",
  },
  {
    layout: "ee",
    name: "Estonian",
    flag: "🇪🇪",
  },
  {
    layout: "lv",
    name: "Latvian",
    flag: "🇱🇻",
  },
  {
    layout: "lt",
    name: "Lithuanian",
    flag: "🇱🇹",
  },
  {
    layout: "am",
    name: "Armenian",
    flag: "🇦🇲",
  },
  {
    layout: "ge",
    name: "Georgian",
    flag: "🇬🇪",
  },
  {
    layout: "ie",
    name: "Irish",
    flag: "🇮🇪",
  },
  {
    layout: "za",
    name: "South African",
    flag: "🇿🇦",
  },
  {
    layout: "gb",
    name: "English (UK)",
    flag: "🇬🇧",
  },
  {
    layout: "epo",
    name: "Esperanto",
    flag: "🟢",
  },
];
