[package]
name = "lunactl"
version = "0.1.0"
edition = "2024"

[dependencies]
anyhow = "1.0.97"
clap = { version = "4.5.40", features = ["derive"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0.140"
wayland-client = "0.31"
wayland-protocols = { version = "0.31", features = ["unstable", "client"] }
chrono = "0.4"
xdg-user = "0.1.1"
notify-rust = "4.11.7"
shellexpand = "3.1.0"
regex = "1.10.5"
ctrlc = "3.4.4"
rand = "0.9.1"
