<?xml version="1.0" encoding="UTF-8"?>
<style-scheme id="custom-light" _name="Custom" version="1.0">
  <author>end_4</author>
  <_description>Catppuccin port but very random</_description>

  <style name="bracket-match"  background="#E3E6EB" bold="true"/>
  <style name="bracket-mismatch"  background="#E3E6EB" underline="true"/>
  <style name="c:preprocessor" foreground="#DF8E1D"/>
  <style name="css:at-rules" foreground="#8839EF"/>
  <style name="css:color" foreground="#DF8E1D"/>
  <style name="css:keyword" foreground="#256BF5"/>
  <style name="current-line"  background="#E3E6EB"/>
  <style name="cursor" foreground="#DC8A78"/>
  <style name="def:base-n-integer" foreground="#DF8E1D"/>
  <style name="def:boolean" foreground="#DF8E1D"/>
  <style name="def:builtin" foreground="#DF8E1D"/>
  <style name="def:character" foreground="#DF8E1D"/>
  <style name="def:comment" foreground="#9DA1B1"/>
  <style name="def:complex" foreground="#DF8E1D"/>
  <style name="def:decimal" foreground="#DF8E1D"/>
  <style name="def:doc-comment" foreground="#9DA1B1"/>
  <style name="def:doc-comment-element" foreground="#9DA1B1"/>
  <style name="def:error" foreground="#D53055" background="#EAEDF2"/>
  <style name="def:floating-point" foreground="#DF8E1D"/>
  <style name="def:function" foreground="#256BF5"/>
  <style name="def:identifier" foreground="#000000"/>
  <style name="def:keyword" foreground="#8839EF"/>
  <style name="def:note" foreground="#9DA1B1"/>
  <style name="def:number" foreground="#FE640B"/>
  <style name="def:operator" foreground="#8839EF"/>
  <style name="def:preprocessor" foreground="#256BF5"/>
  <style name="def:reserved" foreground="#8839EF"/>
  <style name="def:shebang" foreground="#9DA1B1"/>
  <style name="def:special-char" foreground="#256BF5"/>
  <style name="def:special-constant" foreground="#DF8E1D"/>
  <style name="def:statement" foreground="#8839EF"/>
  <style name="def:string" foreground="#4AA537"/>
  <style name="def:type" foreground="#256BF5" italic="true"/>
  <style name="diff:added-line" foreground="#282D32" background="#ACF2BD"/>
  <style name="diff:changed-line" foreground="#282D32" background="#F1F2C3"/>
  <style name="diff:location" foreground="#9DA1B1"/>
  <style name="diff:removed-line" foreground="#282D32" background="#FFEEF0"/>
  <style name="draw-spaces" foreground="#3b3a32"/>
  <style name="html:dtd" foreground="#4AA537"/>
  <style name="html:tag" foreground="#8839EF"/>
  <style name="js:function" foreground="#256BF5"/>
  <style name="line-numbers" foreground="#9699AA" background="#EAEDF2"/>
  <style name="perl:builtin" foreground="#256BF5"/>
  <style name="perl:include-statement" foreground="#8839EF"/>
  <style name="perl:special-variable" foreground="#DF8E1D"/>
  <style name="perl:variable" foreground="#000000"/>
  <style name="php:string" foreground="#4AA537"/>
  <style name="python:builtin-constant" foreground="#8839EF"/>
  <style name="python:builtin-function" foreground="#256BF5"/>
  <style name="python:module-handler" foreground="#8839EF"/>
  <style name="python:special-variable" foreground="#8839EF"/>
  <style name="ruby:attribute-definition" foreground="#8839EF"/>
  <style name="ruby:builtin" foreground="#000000"/>
  <style name="ruby:class-variable" foreground="#000000"/>
  <style name="ruby:constant" foreground="#000000"/>
  <style name="ruby:global-variable" foreground="#256BF5"/>
  <style name="ruby:instance-variable" foreground="#000000"/>
  <style name="ruby:module-handler" foreground="#8839EF"/>
  <style name="ruby:predefined-variable" foreground="#DF8E1D"/>
  <style name="ruby:regex" foreground="#f6aa11"/>
  <style name="ruby:special-variable" foreground="#8839EF"/>
  <style name="ruby:symbol" foreground="#DF8E1D"/>
  <style name="rubyonrails:attribute-definition" foreground="#8839EF"/>
  <style name="rubyonrails:block-parameter" foreground="#fd971f" italic="true"/>
  <style name="rubyonrails:builtin" foreground="#000000"/>
  <style name="rubyonrails:class-inherit" foreground="#256BF5" underline="true" italic="true"/>
  <style name="rubyonrails:class-name" foreground="#256BF5"/>
  <style name="rubyonrails:class-variable" foreground="#000000"/>
  <style name="rubyonrails:complex-interpolation" foreground="#DF8E1D"/>
  <style name="rubyonrails:constant" foreground="#000000"/>
  <style name="rubyonrails:global-variable" foreground="#256BF5"/>
  <style name="rubyonrails:instance-variable" foreground="#000000"/>
  <style name="rubyonrails:module-handler" foreground="#8839EF"/>
  <style name="rubyonrails:module-name" foreground="#256BF5"/>
  <style name="rubyonrails:predefined-variable" foreground="#DF8E1D"/>
  <style name="rubyonrails:rails" foreground="#000000"/>
  <style name="rubyonrails:regex" foreground="#f6aa11"/>
  <style name="rubyonrails:simple-interpolation" foreground="#DF8E1D"/>
  <style name="rubyonrails:special-variable" foreground="#8839EF"/>
  <style name="rubyonrails:symbol" foreground="#DF8E1D"/>
  <style name="search-match"  background="#E3E6EB" bold="true" underline="true"/>
  <style name="selection" foreground="#f8f8f2" background="#444444"/>
  <style name="text" foreground="#f8f8f2" background="#222222"/>
  <style name="xml:attribute-name" foreground="#256BF5"/>
  <style name="xml:element-name" foreground="#8839EF"/>
  <style name="xml:entity" foreground="#c8cecc"/>
  <style name="xml:namespace" foreground="#8839EF"/>
  <style name="xml:tag" foreground="#8839EF"/>

</style-scheme>
