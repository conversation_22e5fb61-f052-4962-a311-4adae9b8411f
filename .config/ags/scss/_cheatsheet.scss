.cheatsheet-bg {
    @include large-rounding;
    @include elevation-border;
    @include elevation2;
    background-color: $layer0;
    padding: 0.8rem;
}

.cheatsheet-title {
    color: $cheatsheetTitle;
}

.cheatsheet-bind-lineheight {
    min-height: 1.8rem;
}

.cheatsheet-key {
    @include techfont;
    min-height: 1.2rem;
    min-width: 1.2rem;
    margin: 0.1rem;
    padding: 0.1rem 0.15rem;
    -gtk-outline-radius: 0.3rem;
    color: $onPrimary;
    background-color: $primary;
    border-radius: 0.3rem;
    font-weight: 500;
    transition: all 0.2s ease;
}

.cheatsheet-key-notkey {
    min-height: 1.2rem;
    padding: 0.1rem 0.15rem;
    margin: 0.1rem;
    color: $onLayer0;
}

@for $i from 1 through 8 {
    .cheatsheet-color-#{$i} {
        color: $onPrimary;
        background-color: nth($cheatsheetColors, $i);
    }
}

// .cheatsheet-action {}

.cheatsheet-closebtn {
    @include element_decel;
    @include full-rounding;
    min-width: 2.386rem;
    min-height: 2.386rem;
}

.cheatsheet-closebtn:hover,
.cheatsheet-closebtn:focus {
    background-color: $layer0Hover;
}

.cheatsheet-closebtn:active {
    background-color: $layer0Active;
}

.cheatsheet-category-title {
    @include titlefont;
    font-size: 1.5rem;
    color: $primary;
    margin-bottom: 0.5rem;
    padding-bottom: 0.2rem;
    border-bottom: 1px solid $primary;
}

.cheatsheet-section {
    background-color: $layer1;
    border-radius: 0.8rem;
    padding: 0.8rem;
    margin-bottom: 0.5rem;
    @include elevation-border;
}

.cheatsheet-keybind-row {
    min-height: 1.8rem;
    padding: 0.2rem 0.5rem;
    border-radius: 0.5rem;

    &:hover {
        background-color: $layer2;
    }
}

// Search box styles
.cheatsheet-search-container {
    margin-bottom: 1rem;
}

.cheatsheet-search-entry {
    @include small-rounding;
    background-color: $layer2;
    color: $onLayer1;
    padding: 0.5rem 0.8rem;
    caret-color: $onLayer1;
    border: 1px solid $outliner;
    min-width: 25rem;

    selection {
        background-color: $secondary;
        color: $onSecondary;
    }
}

.cheatsheet-search-entry:focus {
    background-color: $layer2Hover;
    border: 1px solid $primary;
}

.cheatsheet-no-results {
    padding: 2rem;

    .txt-title {
        color: $primary;
        font-size: 1.3rem;
        margin-bottom: 0.5rem;
    }
}

@mixin cheatsheet-periodictable-element {
    min-width: 5.455rem;
    min-height: 5.455rem;
    @include small-rounding;
    background-color: $layer1;
    color: $onLayer1;
}

.cheatsheet-periodictable-elementsymbol {
    @include readingfont;
    font-size: 1.705rem;
    font-weight: bold;
}

.cheatsheet-periodictable-elementnum {
    @include full-rounding;
    min-width: 1.364rem;
    min-height: 1.364rem;
    background-color: $term0;
    color: $onBackground;
}

// Periodic table colors using Material You palette
$colormetal: $primaryContainer;      // Metals - using primary container for main elements
$colornonmetal: $tertiaryContainer;  // Non-metals - using tertiary for contrast
$colornoblegas: $secondaryContainer; // Noble gases - using secondary for distinction
$colorlanthanum: $surfaceContainerHighest; // Lanthanides - using surface container for subtle difference
$coloractinium: $surfaceContainerHigh;     // Actinides - using another surface container level

.cheatsheet-periodictable-empty {
    @include small-rounding;
    min-width: 5.455rem;
    min-height: 5.455rem;
}

.cheatsheet-periodictable-metal {
    @include cheatsheet-periodictable-element;
    background-color: $colormetal;
    color: $onPrimaryContainer;  // Better contrast for text
}

.cheatsheet-periodictable-nonmetal {
    @include cheatsheet-periodictable-element;
    background-color: $colornonmetal;
    color: $onPrimaryContainer;  // Better contrast for text
}

.cheatsheet-periodictable-noblegas {
    @include cheatsheet-periodictable-element;
    background-color: $colornoblegas;
    color: $onPrimaryContainer;  // Better contrast for text
}

.cheatsheet-periodictable-lanthanum {
    @include cheatsheet-periodictable-element;
    background-color: $colorlanthanum;
    color: $onPrimaryContainer;  // Better contrast for text
}

.cheatsheet-periodictable-actinium {
    @include cheatsheet-periodictable-element;
    background-color: $coloractinium;
    color: $onPrimaryContainer;  // Better contrast for text
}

.cheatsheet-periodictable-legend-color-wrapper {
    @include full-rounding;
    padding: 0.273rem;
    border: 0.136rem solid $onLayer0;
}

@mixin cheatsheet-periodictable-legend-color {
    @include full-rounding;
    min-width: 1.023rem;
    min-height: 1.023rem;
}

.cheatsheet-periodictable-legend-color-metal {
    @include cheatsheet-periodictable-legend-color;
    background-color: $colormetal;
}

.cheatsheet-periodictable-legend-color-nonmetal {
    @include cheatsheet-periodictable-legend-color;
    background-color: $colornonmetal;
}

.cheatsheet-periodictable-legend-color-noblegas {
    @include cheatsheet-periodictable-legend-color;
    background-color: $colornoblegas;
}

.cheatsheet-periodictable-legend-color-lanthanum {
    @include cheatsheet-periodictable-legend-color;
    background-color: $colorlanthanum;
}

.cheatsheet-periodictable-legend-color-actinium {
    @include cheatsheet-periodictable-legend-color;
    background-color: $coloractinium;
}
