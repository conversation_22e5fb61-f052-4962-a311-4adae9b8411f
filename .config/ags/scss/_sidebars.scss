$sidebar_chat_textboxareaColor: mix($onSurfaceVariant, $surfaceVariant, 40%);

@mixin group-padding {
    padding: 0.341rem;
}

.sidebar-right-bg {
    @include menu_decel;
    background-color: $background;
    padding: 1.023rem;
    // border-radius: 0 $rounding_small $rounding_small 0;

}
.sidebar-right-rounded {
    border-radius: 0 $rounding_small $rounding_small 0;
}

.sidebar-left-bg {
    @include menu_decel;
    background-color: $background;
    // border-radius: $rounding_small 0 0 $rounding_small;
    padding: 1.023rem;
}
.sidebar-left-rounded {
    border-radius: $rounding_small 0 0 $rounding_small;
}

.sidebar-group {
    @include normal-rounding;
    @include group-padding;
    background-color: $layer1;
}

.calendarsep {
    margin: 50px;
}

.sidebar-group-nopad {
    @include normal-rounding;
    background-color: $layer1;
}

.sidebar-group-invisible {
    @include group-padding;
}

.sidebar-group-invisible-morehorizpad {
    padding: 0.341rem 0.682rem;
}

.sidebar-togglesbox {
    @include full-rounding;
    @include group-padding;
    background-color: $layer1;
}

.sidebar-iconbutton {
    @include full-rounding;
    @include element_decel;
    color: $onSurfaceVariant;
    min-width: 2.727rem;
    min-height: 2.727rem;
}

.sidebar-iconbutton:hover,
.sidebar-iconbutton:focus {
    background-color: $layer1Hover;
}

.sidebar-iconbutton:active {
    background-color: $layer1Active;
}

.sidebar-button-active {
    background-color: $primary;
    color: $onPrimary;
}

.sidebar-button-active:hover,
.sidebar-button-active:focus {
    background-color: mix($primary, $layer1Hover, 70%);
}

.sidebar-button-active:active {
    background-color: mix($primary, $layer1Active, 40%);
}

.sidebar-buttons-separator {
    min-width: 0.068rem;
    min-height: 0.068rem;
    background-color: $onSurfaceVariant;
}

.sidebar-navrail {
    padding: 0rem $rounding_medium;
}

.sidebar-navrail-btn>box>label {
    @include full-rounding;
    @include menu_decel;
}

.sidebar-navrail-btn:hover>box>label:first-child,
.sidebar-navrail-btn:focus>box>label:first-child {
    background-color: $layer1Hover;
}

.sidebar-navrail-btn:active>box>label:first-child {
    background-color: $layer1Active;
}

.sidebar-navrail-btn-active>box>label:first-child {
    background-color: $secondaryContainer;
    color: $onSecondaryContainer;
}

.sidebar-navrail-btn-active:hover>box>label:first-child,
.sidebar-navrail-btn-active:focus>box>label:first-child {
    background-color: mix($secondaryContainer, $layer1Hover, 90%);
    color: mix($onSecondaryContainer, $layer1Hover, 90%);
}

.sidebar-sysinfo-grouppad {
    padding: 1.159rem;
}

.sidebar-memory-ram-circprog {
    @include fluent_decel_long;
    min-width: $rounding_small;
    min-height: 4.091rem;
    padding: 0.409rem;
    background-color: $secondaryContainer;
    color: $onSecondaryContainer;
    font-size: 0px;
}

.sidebar-memory-swap-circprog {
    @include fluent_decel_long;
    min-width: $rounding_small;
    min-height: 2.255rem;
    padding: 0.409rem;
    margin: 0.918rem;
    background-color: $secondaryContainer;
    color: $onSecondaryContainer;
    font-size: 0px;
}

.sidebar-cpu-circprog {
    min-width: $rounding_small;
    min-height: 3.409rem;
    padding: 0.409rem;
    background-color: $secondaryContainer;
    color: $onSecondaryContainer;
    @include fluent_decel_long;
    font-size: 0px;
}

.sidebar-scrollbar {
    trough {
        @include full-rounding;
        min-width: 0.545rem;
        background-color: transparent;
    }

    slider {
        @include full-rounding;
        @include element_decel;
        min-width: 0.273rem;
        min-height: 2.045rem;
        background-color: transparentize($onSurfaceVariant, 0.7);
    }

    slider:hover,
    slider:focus {
        background-color: transparentize($onSurfaceVariant, 0.6);
    }

    slider:active {
        background-color: transparentize($onSurface, 0.5);
    }
}

.sidebar-calendar-btn {
    @include full-rounding;
    @include element_decel;
    min-height: 2.523rem;
    min-width: 2.523rem;
    color: $onSurface;
}

.sidebar-calendar-btn:hover,
.sidebar-calendar-btn:focus {
    background-color: $hovercolor;
}

.sidebar-calendar-btn:active {
    background-color: $activecolor;
}

.sidebar-calendar-btn-txt {
    margin-left: -10.341rem;
    margin-right: -10.341rem;
}

.sidebar-calendar-btn-today {
    background-color: $primary;
    color: $onPrimary;
}

.sidebar-calendar-btn-today:hover,
.sidebar-calendar-btn-today:focus {
    background-color: mix($primary, $hovercolor, 70%);
}

.sidebar-calendar-btn-today:active {
    background-color: mix($primary, $hovercolor, 40%);
}

.sidebar-calendar-btn-othermonth {
    color: $outline;
}

.sidebar-calendar-header {
    margin: 0.341rem;
}

.sidebar-calendar-monthyear-btn {
    @include full-rounding;
    @include element_decel;
    padding: 0rem 0.682rem;
    background-color: $layer2;
    color: $onSurface;
}

.sidebar-calendar-monthyear-btn:hover,
.sidebar-calendar-monthyear-btn:focus {
    background-color: $hovercolor;
}

.sidebar-calendar-monthyear-btn:active {
    background-color: $activecolor;
}

.sidebar-calendar-monthshift-btn {
    @include full-rounding;
    @include element_decel;
    min-width: 2.045rem;
    min-height: 2.045rem;
    background-color: $layer2;
    color: $outline;
}

.sidebar-calendar-monthshift-btn:hover {
    background-color: $hovercolor;
}

.sidebar-calendar-monthshift-btn:active {
    background-color: $activecolor;
}

.sidebar-todo-item {
    @include small-rounding;
    margin-right: 0.545rem;
    // padding: 0.341rem;
    background-color: $layer2;
    color: $onLayer2;
}

.sidebar-todo-txt {
    margin: 0.682rem;
    margin-bottom: 0rem;
}

.sidebar-todo-actions {
    margin: 0.341rem;
    margin-top: 0rem;
    margin-right: 0rem;
}

.sidebar-todo-item-action {
    @include element_decel;
    border-radius: 9999px;
    min-width: 1.705rem;
    min-height: 1.705rem;
}

.sidebar-todo-item-action:hover,
.sidebar-todo-item-action:focus {
    background-color: $layer2Hover;
}

.sidebar-todo-item-action:active {
    background-color: $layer2Active;
}

.sidebar-todo-crosser {
    transition: margin 200ms cubic-bezier(0.1, 1, 0, 1), background-color 0ms;
    min-width: 0rem;
}

.sidebar-todo-crosser-crossed {
    background-color: transparent;
}

.sidebar-todo-crosser-removed {
    background-color: transparent;
}

.sidebar-todo-new {
    @include full-rounding;
    @include element_decel;
    background-color: $layer2;
    color: $onLayer2;
    margin: 0.341rem;
    padding: 0.205rem 0.545rem;
}

.sidebar-todo-new,
.sidebar-todo-new:focus {
    color: $onSecondaryContainer;
    background-color: $secondaryContainer;
}

.sidebar-todo-new:active {
    color: $onPrimaryContainer;
    background-color: $primaryContainer;
}

.sidebar-todo-add {
    @include element_decel;
    @include small-rounding;
    min-width: 1.705rem;
    min-height: 1.705rem;
    color: $onSecondaryContainer;
    border: 0.068rem solid $onSurface;
}

.sidebar-todo-add:hover,
.sidebar-todo-add:focus {
    background-color: $surfaceContainerHigh;
}

.sidebar-todo-add:active {
    background-color: $surfaceContainerHighest;
}

.sidebar-todo-add-available {
    @include element_decel;
    @include small-rounding;
    min-width: 1.705rem;
    min-height: 1.705rem;
    background-color: $primary;
    color: $onPrimary;
    border: 0.068rem solid $primary;
}

.sidebar-todo-add-available:hover,
.sidebar-todo-add-available:focus {
    background-color: mix($primary, $hovercolor, 70%);
}

.sidebar-todo-add-available:active {
    background-color: mix($primary, $hovercolor, 40%);
}

.sidebar-todo-entry {
    @include element_decel;
    @include small-rounding;
    background-color: $surfaceVariant;
    color: $onSurfaceVariant;
    caret-color: $onSurfaceVariant;
    margin: 0rem 0.341rem;
    min-height: 1.773rem;
    min-width: 0rem;
    padding: 0.205rem 0.682rem;
    border: 0.068rem solid $outline;
}

.sidebar-todo-entry:focus {
    border: 0.068rem solid $onSurfaceVariant;
}

.sidebar-module {
    @include normal-rounding;
    @include group-padding;
    background-color: $layer1;
    padding: 0.682rem;
}

// Bluetooth controller status indicators
.bluetooth-controller-info {
    margin-top: 0.682rem;
    margin-bottom: 0.682rem;
}

.bluetooth-status-indicator {
    @include full-rounding;
    padding: 0.341rem;
    background-color: $surfaceContainerHigh;
    color: $onSurfaceVariant;
    opacity: 0.5;

    &.active {
        background-color: $secondaryContainer;
        color: $onSecondaryContainer;
        opacity: 1;
    }

    &.inactive {
        background-color: $surfaceContainerHigh;
        color: $onSurfaceVariant;
        opacity: 0.5;
    }
}

// Bluetooth device sections - Material You 3
.bluetooth-device-section {
    margin-bottom: 1rem;
}

.bluetooth-section-separator {
    min-height: 1px;
    background-color: $outlineVariant;
    opacity: 0.5;
    margin: 0.8rem 0;
    border-radius: 1px;
}

// Bluetooth controls styling
.sidebar-bluetooth-controls {
    padding: 1rem;
    margin-bottom: 1rem;
    background-color: $surfaceContainerLow;
    border-radius: $rounding_medium;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.bluetooth-controls-row {
    margin-bottom: 0.8rem;
    /* GTK doesn't support align-items */
    /* Use valign property on child elements instead */
}

// Bluetooth scan button
.sidebar-bluetooth-scan-button {
    background-color: $primary;
    color: $onPrimary;
    padding: 0.5rem 1rem;
    border-radius: $rounding_medium;
    font-weight: 500;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    transition: all 200ms ease;

    &:hover {
        background-color: mix($primary, $surfaceContainerHighest, 90%);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
    }

    &:active {
        background-color: mix($primary, $surfaceContainerHighest, 70%);
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }

    &.scanning {
        background-color: $tertiary;
        color: $onTertiary;
    }
}

// Bluetooth empty state
.bluetooth-empty-state {
    padding: 2rem;
}

.bluetooth-empty-title {
    font-size: 1.2rem;
    font-weight: 500;
    margin: 1rem 0 0.5rem;
}

.bluetooth-empty-subtitle {
    color: $onSurfaceVariant;
    margin-bottom: 1rem;
}

.bluetooth-empty-scan-button {
    background-color: $primary;
    color: $onPrimary;
    padding: 0.5rem 1.5rem;
    border-radius: $rounding_medium;
    font-weight: 500;
    margin-top: 1rem;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    /* GTK doesn't support all transitions */
    /* We'll use specific properties instead */

    &:hover {
        background-color: mix($primary, $surfaceContainerHighest, 90%);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
        /* GTK doesn't support transform or margin transitions */
        /* We'll use opacity changes instead */
        opacity: 0.9;
    }
}

.bluetooth-empty-manager-button {
    background-color: $surfaceContainerHigh;
    color: $onSurfaceVariant;
    padding: 0.5rem 1.5rem;
    border-radius: $rounding_medium;
    margin-top: 0.5rem;
    /* GTK doesn't support all transitions */
    /* We'll use specific properties instead */

    &:hover {
        background-color: $surfaceContainerHighest;
        color: $onSurface;
    }
}

// Bluetooth controller info
.bluetooth-controller-info {
    background-color: $surfaceContainerLowest;
    padding: 1rem;
    border-radius: $rounding_medium;
    margin-top: 1rem;
}

.bluetooth-controller-status {
    margin-top: 0.5rem;
}

.bluetooth-status-indicator {
    /* GTK doesn't support display, align-items, justify-content, width, height */
    /* Use min-width, min-height and child element positioning instead */
    min-width: 2rem;
    min-height: 2rem;
    border-radius: 9999px; /* Use a large value for circular shape */
    background-color: $surfaceContainerLow;

    &.active {
        background-color: $primaryContainer;
        color: $onPrimaryContainer;
    }

    &.inactive {
        background-color: $surfaceContainerHigh;
        color: $onSurfaceVariant;
        opacity: 0.7;
    }
}

// Bluetooth bottom bar styles
.sidebar-bluetooth-bottombar {
    margin-top: 1rem;
    padding: 0.8rem;
    background-color: $surfaceContainerLow;
    border-radius: $rounding_medium;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.sidebar-bluetooth-bottombar-button {
    background-color: $surfaceContainerHigh;
    color: $onSurface;
    padding: 0.5rem 1rem;
    border-radius: $rounding_medium;
    font-weight: 500;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    /* GTK doesn't support all transitions */
    /* We'll use specific properties instead */

    &:hover {
        background-color: $primaryContainer;
        color: $onPrimaryContainer;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        /* GTK doesn't support transform or margin transitions */
        /* We'll use opacity changes instead */
        opacity: 0.9;
    }

    &:active {
        background-color: $primary;
        color: $onPrimary;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        opacity: 0.8;
    }
}

// Bluetooth settings button
.sidebar-bluetooth-settings-button {
    background-color: $surfaceContainerHigh;
    color: $onSurfaceVariant;
    padding: 0.5rem;
    border-radius: 9999px; /* Use a large value for circular shape */
    /* GTK doesn't support all transitions */
    /* We'll use specific properties instead */

    &:hover {
        background-color: $primaryContainer;
        color: $onPrimaryContainer;
        /* GTK doesn't support transform */
        /* We'll skip the rotation effect */
    }
}

/* Bluetooth toggle with label - spacing handled in JavaScript */

// Bluetooth action button
.sidebar-bluetooth-device-action {
    @include full-rounding;
}

// Bluetooth device styling - Material You 3
.sidebar-bluetooth-device {
    @include normal-rounding;
    padding: 0.8rem;
    margin-bottom: 0.8rem;
    background-color: $surfaceContainerLow;
    /* GTK doesn't support all transitions */
    /* We'll use specific properties instead */
    border-left: 3px solid transparent;
    margin-left: 2px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    transition: background-color 200ms ease, box-shadow 200ms ease;

    // Device icon container styling
    .sidebar-bluetooth-device-icon-container {
        margin-right: 12px;
    }

    // Device icon box styling
    .sidebar-bluetooth-icon-box {
        @include normal-rounding;
    }

    // Device icon styling
    .sidebar-bluetooth-icon {
        font-family: 'Material Icons', 'Material Symbols Rounded';
    }

    // Device name styling
    .bluetooth-device-name {
        font-weight: 500;
        margin-bottom: 2px;
    }

    // Device status styling
    .bluetooth-device-status {
        font-size: 0.8em;
        opacity: 0.8;
    }

    // Connected device styling
    &.bluetooth-device-connected {
        background-color: $secondaryContainer;
        border-left-color: $primary;
        margin-left: 4px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

        .bluetooth-device-name {
            color: $onSecondaryContainer;
            font-weight: 600;
        }

        .bluetooth-device-status {
            color: $onSecondaryContainer;
            opacity: 0.9;
        }
    }

    // Paired device styling
    &.bluetooth-device-paired {
        background-color: $surfaceContainerHigh;
        border-left-color: $secondaryContainer;

        .bluetooth-device-name {
            color: $onSurface;
        }

        .bluetooth-device-status {
            color: $onSurfaceVariant;
        }
    }

    // Available device styling
    &.bluetooth-device-available {
        background-color: $surfaceContainerLowest;

        .bluetooth-device-name {
            color: $onSurface;
        }
    }

    // Hover state
    &:hover {
        background-color: $surfaceContainerHighest;
        /* GTK doesn't support transform or margin-top/bottom transitions */
        /* We'll use opacity changes instead */
        opacity: 0.95;
        box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);

        &.bluetooth-device-connected {
            background-color: mix($secondaryContainer, $surfaceContainerHighest, 90%);
        }

        &.bluetooth-device-paired {
            background-color: mix($surfaceContainerHigh, $surfaceContainerHighest, 90%);
        }
    }

    /* GTK doesn't support align-items */
    /* Use valign property on child elements instead */
}

.sidebar-module-btn-arrow {
    @include full-rounding;
    @include icon-material;
    background-color: $layer2;
    min-width: 1.705rem;
    min-height: 1.705rem;

    &:hover,
    &:focus {
        background-color: $layer2Hover;
    }

    &:active {
        background-color: $layer2Active;
    }
}

.sidebar-module-scripts-button {
    @include full-rounding;
    @include icon-material;
    background-color: $layer1;
    min-width: 1.705rem;
    min-height: 1.705rem;

    &:hover,
    &:focus {
        background-color: $layer1Hover;
    }

    &:active {
        background-color: $layer1Active;
    }
}

$colorpicker_rounding: 0.341rem;

.sidebar-module-colorpicker-wrapper {
    padding: 0.341rem;
}

.sidebar-module-colorpicker-cursorwrapper {
    padding: 0.341rem 0.136rem;
}

.sidebar-module-colorpicker-hue {
    min-height: 13.636rem;
    min-width: 1.091rem;
    border-radius: $colorpicker_rounding;
}

.sidebar-module-colorpicker-hue-cursor {
    background-color: $onBackground;
    border: 0.136rem solid $onBackground;
    min-height: 0.136rem;
    margin-top: -0.136rem;
    border-radius: $colorpicker_rounding;
}

.sidebar-module-colorpicker-saturationandlightness-wrapper {
    padding: 0.341rem;
}

.sidebar-module-colorpicker-saturationandlightness {
    min-height: 13.636rem;
    min-width: 13.636rem;
    border-radius: $colorpicker_rounding;
}

.sidebar-module-colorpicker-saturationandlightness-cursorwrapper {
    padding: 0.341rem;
    margin-top: -0.409rem;
    margin-left: -0.409rem;
}

.sidebar-module-colorpicker-saturationandlightness-cursor {
    @include full-rounding;
    border: 0.136rem solid white;
    min-width: 0.682rem;
    min-height: 0.682rem;
    margin-top: -0.409rem;
    margin-left: -0.409rem;
}

.sidebar-module-colorpicker-result-area {
    padding: 0.341rem;
}

.sidebar-module-colorpicker-result-box {
    border-radius: $colorpicker_rounding;
    min-width: 2.045rem;
    min-height: 0.682rem;
    padding: 0.341rem;
}

.sidebar-icontabswitcher {
    @include full-rounding;
    @include group-padding;
    background-color: $layer1;
}

.sidebar-chat-providerswitcher {
    @include small-rounding;
    padding: 0.477rem 0.682rem;
    background-color: $surfaceContainerHigh;
    color: $onSurfaceVariant;
}

.sidebar-chat-viewport {
    @include element_decel;
    padding: 0.682rem 0rem;
}

.sidebar-chat-textarea {
    @include normal-rounding;
    background-color: $layer1;
    color: $onLayer1;
    padding: 0.682rem;
}

.sidebar-chat-entry {
    color: $onSurfaceVariant;
    caret-color: $onSurfaceVariant;
    min-height: 1.773rem;
    min-width: 0rem;
}

/* Removed transition from sidebar-chat-wrapper */

.sidebar-chat-wrapper-extended {
    min-height: 7.500rem;
}

.sidebar-chat-send {
    @include element_decel;
    min-width: 1.705rem;
    min-height: 1.705rem;
    border-radius: $rounding_medium - 0.681rem;
}

.sidebar-chat-send:hover,
.sidebar-chat-send:focus {
    background-color: $surfaceBright;
}

.sidebar-chat-send:active {
    background-color: $surfaceVariant;
}

.sidebar-chat-send-available {
    background-color: $primary;
    color: $onPrimary;
}

.sidebar-chat-send-available:hover,
.sidebar-chat-send-available:focus {
    background-color: mix($primary, $hovercolor, 70%);
}

.sidebar-chat-send-available:active {
    background-color: mix($primary, $hovercolor, 40%);
}

.sidebar-chat-messagearea {
    margin: 0.341rem;
}

.sidebar-chat-message {
    @include normal-rounding;
    @include group-padding;
    background-color: $layer1;
}

$skeleton-accent: mix($secondary, $onSecondary, 50%);

@keyframes sidebar-chat-message-skeletonline-anim {
    0% {
        background-position: 175% 0%;
    }

    100% {
        background-position: 50% 0%;
    }
}

.sidebar-chat-message-skeletonline {
    border-radius: $rounding_verysmall;
    min-height: 1.364rem;
    background-color: $layer2;
}

.sidebar-chat-message-skeletonline-offset0 {
    background-repeat: no-repeat;
    background: linear-gradient(to right, $layer3 0%, $skeleton-accent 25%, $layer3 50%, $layer3 100%);
    background-size: 500% 500%;
    animation: sidebar-chat-message-skeletonline-anim 2s linear;
    animation-iteration-count: infinite;
}

.sidebar-chat-message-skeletonline-offset1 {
    background-repeat: no-repeat;
    background: linear-gradient(to right, $layer3 0%, $layer3 50%, $skeleton-accent 75%, $layer3 100%);
    background-size: 500% 500%;
    animation: sidebar-chat-message-skeletonline-anim 2s linear;
    animation-iteration-count: infinite;
}

.sidebar-chat-message-skeletonline-offset2 {
    margin-right: 5.795rem;
    background-repeat: no-repeat;
    background: linear-gradient(to right, $layer3 0%, $layer3 25%, $skeleton-accent 50%, $layer3 75%, $layer3 100%);
    background-size: 500% 500%;
    animation: sidebar-chat-message-skeletonline-anim 2s linear;
    animation-iteration-count: infinite;
}

.sidebar-chat-indicator {
    @include element_decel;
    @include full-rounding;
    min-width: 0.136rem;
}

.sidebar-chat-name {
    @include titlefont;
    @include small-rounding;
    padding: 0.341rem 0.818rem;
    margin: 0.341rem;
    background-color: $layer2;
    color: $onLayer2;
}

.sidebar-chat-name-user {
    background-color: $layer2;
    color: $onLayer2;
}

.sidebar-chat-name-bot {
    background-color: $secondary;
    color: $onSecondary;
}

.sidebar-chat-name-system {
    background-color: $secondaryContainer;
    color: $onSecondaryContainer;
}

.sidebar-chat-txtblock {
    margin-left: -0.136rem;
    padding: 0.341rem;
    padding-left: 0.818rem;
}

.sidebar-chat-txt {
    @include readingfont;
}

.sidebar-chat-latex {
    @include small-rounding;
    margin: 0rem 0.682rem;
    padding: 0.682rem;
    color: $onBackground;
}

.sidebar-chat-codeblock {
    @include normal-rounding;
    background-color: $layer2;
    color: $onLayer2;
    margin: 0rem 0.682rem;
}

.sidebar-chat-codeblock-topbar {
    @include mainfont;
    background-color: $layer3;
    color: $onLayer3;
    border-top-left-radius: $rounding_small;
    border-top-right-radius: $rounding_small;
    padding: 0.341rem 0.477rem;
}

.sidebar-chat-codeblock-topbar-txt {
    @include full-rounding;
    padding: 0.273rem;
}

.sidebar-chat-codeblock-topbar-btn {
    @include full-rounding;
    @include element_decel;
    padding: 0.273rem 0.477rem;
}

.sidebar-chat-codeblock-topbar-btn:hover,
.sidebar-chat-codeblock-topbar-btn:focus {
    background-color: $surfaceBright;
}

.sidebar-chat-codeblock-topbar-btn:active {
    background-color: $surfaceVariant;
}

.sidebar-chat-codeblock-code {
    @include techfont;
    padding: 0.682rem;
}

.sidebar-chat-divider {
    min-height: 1px;
    background-color: $sidebar_chat_textboxareaColor;
    margin: 0rem 0.545rem;
}

.sidebar-chat-welcome-txt {
    margin: 0rem 3.409rem;
}

.sidebar-chat-settings-toggles {
    margin: 0rem 5.455rem;
}

.sidebar-chat-welcome-icon {
    @include full-rounding;
    font-size: 4rem;
}

.sidebar-chat-welcome-logo {
    @include full-rounding;
    @include element_decel;
    @include icon-material;
    min-height: 4.773rem;
    min-width: 4.773rem;
    font-size: 3.076rem;
    background-color: $secondaryContainer;
    color: $onSecondaryContainer;
}

.sidebar-chat-chip {
    @include element_decel;
    @include small-rounding;
    padding: 0.341rem 0.477rem;
}

.sidebar-chat-chip-action {
    @include element_decel;
    background-color: $layer2;
    color: $onSurfaceVariant;
}

.sidebar-chat-chip-action:hover,
.sidebar-chat-chip-action:focus {
    background-color: $hovercolor;
}

.sidebar-chat-chip-action:active {
    background-color: $activecolor;
}

.sidebar-chat-chip-action-active {
    color: $sidebar_chat_textboxareaColor;
    border: 0.068rem solid $sidebar_chat_textboxareaColor;
}

.sidebar-chat-chip-toggle {
    @include element_decel;
    @include small-rounding;
    padding: 0.341rem 0.477rem;
    background-color: $layer3;
    color: $onSurfaceVariant;
}

.sidebar-chat-chip-toggle:focus,
.sidebar-chat-chip-toggle:hover {
    background-color: $hovercolor;
}

.sidebar-chat-chip-toggle:active {
    background-color: $activecolor;
}

.sidebar-pin {
    @include small-rounding;
    @include element_decel;
    min-height: 2.386rem;
    min-width: 2.386rem;
    color: $onSurface;
}

.sidebar-pin:hover,
.sidebar-pin:focus {
    background-color: $hovercolor;
}

.sidebar-pin:active {
    background-color: $activecolor;
}

.sidebar-pin-enabled {
    background-color: $primary;

    label {
        color: $onPrimary;
    }
}

.sidebar-pin-enabled:hover,
.sidebar-pin-enabled:focus {
    background-color: mix($primary, $hovercolor, 70%);
}

.sidebar-pin-enabled:active {
    background-color: mix($primary, $hovercolor, 40%);
}

.sidebar-volmixer-stream {
    border-bottom: 0.068rem solid $outlineVariant;
    padding: 0.682rem;
    color: $onSurface;
}

.sidebar-volmixer-stream-appicon {
    font-size: 3.273rem;
}

.sidebar-volmixer-stream-slider {
    trough {
        border-radius: $rounding_verysmall;
        min-height: 1.364rem;
        min-width: 1.364rem;
        background-color: $secondaryContainer;
    }

    highlight {
        border-radius: $rounding_verysmall;
        min-height: 1.364rem;
        min-width: 1.364rem;
        background-color: $primary;
    }

    slider {
        border-radius: $rounding_verysmall;
        min-height: 1.364rem;
        min-width: 1.364rem;
    }
}

.sidebar-volmixer-status {
    color: $onSurface;
    margin: 0rem 0.682rem;
}

.sidebar-volmixer-deviceselector {
    @include small-rounding;
    padding: 0.477rem 0.682rem;
    background-color: $surfaceContainerHigh;
    color: $onSurfaceVariant;
}

// Bluetooth module styles
.sidebar-bluetooth-controls {
    padding: 0.682rem;
    margin-bottom: 0.682rem;
    @include normal-rounding;
    background-color: $layer1;
    /* Removed bottom border */
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.sidebar-bluetooth-header {
    padding: 0.341rem 0;
    margin-bottom: 0.682rem;
}

.sidebar-bluetooth-title {
    font-weight: bold;
    color: $primary;
}

.sidebar-bluetooth-divider {
    background-color: $outlineVariant;
    margin: 0.341rem 0;
}

.material-card {
    @include normal-rounding;
    background-color: $layer2;
    /* GTK doesn't support transitions */
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.material-button {
    @include normal-rounding;
    background-color: $primary;
    color: $onPrimary;
    padding: 0.341rem 0.682rem;
    /* GTK doesn't support transitions */
}

.material-icon-button {
    @include full-rounding;
    padding: 0.341rem;
    /* GTK doesn't support transitions */
}

.sidebar-bluetooth-scan-button {
    @include normal-rounding;
    @include element_decel;
    padding: 0.341rem 0.682rem;
    background-color: $primary;
    color: $onPrimary;
}

@keyframes pulse {
    0% { background-color: mix($primary, $layer3, 50%); }
    50% { background-color: mix($primary, $layer3, 70%); }
    100% { background-color: mix($primary, $layer3, 50%); }
}

.scanning {
    background-color: mix($primary, $layer3, 50%);
    animation: pulse 2s infinite ease-in-out;
}

.sidebar-bluetooth-device {
    padding: 0.682rem;
    margin-bottom: 0.682rem;
    @include normal-rounding;
    background-color: $layer2;
    color: $onLayer2;
    /* GTK doesn't support transitions */
    border-left: 3px solid transparent;

    &:hover {
        background-color: mix($layer2, $primary, 90%);
    }
}

.sidebar-bluetooth-device-icon-container {
    // GTK CSS doesn't support positioning
    margin: 0;
    padding: 0;
}

.sidebar-bluetooth-battery {
    color: $primary;
    font-weight: bold;
    margin-top: 0.2rem;
}

.sidebar-bluetooth-appicon {
    @include symbolic-icon;
    font-size: 2.045rem;
}

.sidebar-bluetooth-battery {
    // Use margin instead of positioning
    margin-top: -0.341rem;
    margin-left: -0.341rem;
    background-color: $layer3;
    padding: 0.136rem 0.273rem;
    border-radius: 0.682rem;
}

.sidebar-bluetooth-device-action {
    @include full-rounding;
    min-width: 2.045rem;
    min-height: 2.045rem;
    padding: 0.341rem;

    &:hover, &:focus {
        background-color: $layer2Hover;
    }

    &:active {
        background-color: $layer2Active;
    }
}

.sidebar-bluetooth-device-remove {
    @include full-rounding;
    min-width: 2.045rem;
    min-height: 2.045rem;
    padding: 0.341rem;

    &:hover, &:focus {
        background-color: $layer2Hover;
    }

    &:active {
        background-color: $layer2Active;
    }
}

.sidebar-bluetooth-device-actions {
    padding: 0.341rem;
    background-color: $layer3;
    @include small-rounding;
}

.sidebar-bluetooth-action-button {
    @include full-rounding;
    @include element_decel;
    padding: 0.341rem 0.682rem;
    background-color: $layer2;

    // Use primary color for text
    label {
        color: $primary;
    }

    &:hover, &:focus {
        background-color: $layer2Hover;
    }

    &:active {
        background-color: $layer2Active;
    }
}

.sidebar-bluetooth-bottombar {
    // Use homogeneous instead of flex layout
    margin-top: 0.682rem;
}

// Bluetooth info popup styles
.bluetooth-info-popup {
    padding: 1rem;
    background-color: $layer2;
    min-width: 400px;
    min-height: 300px;
    @include normal-rounding;
}

.bluetooth-info-title {
    font-weight: bold;
    font-size: 1.2rem;
    color: $primary;
    margin-bottom: 0.5rem;
}

.bluetooth-info-content {
    background-color: $layer1;
    padding: 0.5rem;
    margin: 0.5rem 0;
    @include small-rounding;
    min-height: 200px;
}

.bluetooth-info-close-button {
    @include normal-rounding;
    background-color: $primary;
    color: $onPrimary;
    padding: 0.5rem 1rem;
    margin-top: 0.5rem;
}

// Bluetooth rename popup styles
.bluetooth-rename-popup {
    padding: 1rem;
    background-color: $layer2;
    min-width: 350px;
    @include normal-rounding;
}

.bluetooth-rename-title {
    font-weight: bold;
    font-size: 1.2rem;
    color: $primary;
    margin-bottom: 0.5rem;
}

.bluetooth-rename-content {
    background-color: $layer1;
    padding: 0.5rem;
    margin: 0.5rem 0;
    @include small-rounding;
}

.bluetooth-rename-buttons {
    margin-top: 0.5rem;
}

.bluetooth-rename-cancel-button {
    @include normal-rounding;
    background-color: $layer3;
    color: $onLayer3;
    padding: 0.5rem 1rem;
}

.bluetooth-rename-confirm-button {
    @include normal-rounding;
    background-color: $primary;
    color: $onPrimary;
    padding: 0.5rem 1rem;
}

// Bluetooth empty state styles
.bluetooth-empty-state {
    padding: 2rem;
    opacity: 0.8;
}

.bluetooth-empty-title {
    font-weight: bold;
    color: $primary;
    font-size: 1.2rem;
    margin-top: 1rem;
}

.bluetooth-empty-subtitle {
    color: $onSurfaceVariant;
    margin-bottom: 1rem;
}

.bluetooth-empty-scan-button {
    @include normal-rounding;
    background-color: $primary;
    color: $onPrimary;
    padding: 0.5rem 1rem;
    margin-top: 1rem;
    transition: background-color 0.2s ease;
}

// Bluetooth section styles
.bluetooth-section-header {
    padding: 0.5rem 0;
    margin-bottom: 0.5rem;
    border-bottom: 2px solid $primary;
}

.bluetooth-section-title {
    font-weight: bold;
    color: $primary;
    font-size: 1rem;
}

.bluetooth-device-section {
    margin-bottom: 1rem;
}

.bluetooth-no-devices {
    padding: 1rem;
    color: $onSurfaceVariant;
    font-style: italic;
    opacity: 0.7;
}



.sidebar-wifinetworks-network {
    padding: 0.682rem;
    @include normal-rounding;
    background-color: $layer2;
    color: $onLayer2;
}

.sidebar-wifinetworks-network:hover,
.sidebar-wifinetworks-network:focus {
    background-color: $layer2Hover;
}

.sidebar-wifinetworks-network:active {
    background-color: $layer2Active;
}

.sidebar-wifinetworks-signal {
    @include symbolic-icon;
    font-size: 2.045rem;
}

.sidebar-wifinetworks-auth-entry {
    @include small-rounding;
    background-color: $layer1;
    color: $onLayer1;
    padding: 0.682rem;
}

.sidebar-centermodules-bottombar-button {
    @include full-rounding;
    @include element_decel;
    min-width: 6.818rem;
    min-height: 2.25rem;
    background-color: $layer2;
    color: $onLayer2;
}

.sidebar-centermodules-bottombar-button:hover,
.sidebar-centermodules-bottombar-button:focus {
    background-color: $layer2Hover;
}

.sidebar-centermodules-bottombar-button:active {
    background-color: $layer2Active;
}

.sidebar-centermodules-scrollgradient-bottom {
    background: linear-gradient(to top, $layer1 0%, transparentize($layer1, 1) 1.023rem);
}

.sidebar-scrollable {
    min-height: 100px;
}

.sidebar-chat-message {
    margin: 0.205rem 0.341rem;
}

.sidebar-chat-message-box {
    background-color: $layer2;
    border-radius: $rounding_small;
    padding: 0.682rem;
}

.sidebar-chat-divider {
    margin: 0.341rem 0;
    border-top: 0.068rem solid $outlineVariant;
}

.sidebar-module-box {
    padding: 0.341rem 0.682rem;
}

.sidebar-prayertime-top {
    @include normal-rounding;
    background: $background;
    padding: 0.82rem;
    // margin-top:0.7rem;
    margin-bottom: 1rem;
    color: $onSecondaryContainer;
    // border:0.2rem solid $outline;
}

.sidebar-prayertime-next {
    margin: 1.682rem 0;
    margin-top: 0.341rem;
    margin-right: 1.5rem;
    @include titlefont;
    color: $onSecondaryContainer;
}

.sidebar-prayertime-item {
    @include small-rounding;
    @include titlefont;
    color: $onSecondaryContainer;

    @include element_decel;
    padding: 0.682rem;
    background-color: mix($surfaceContainer, $primary, 96%);
}

.sidebar-prayertime-item:hover {
    background-color: mix($surfaceContainerLow, $primary, 85%);
}

.sidebar-prayertime-item:active {
    background-color: mix($surfaceContainerLow, $primary, 70%);
}

.sidebar-prayertime-name {
    @include mainfont;
    color: $onSurface;
    margin: 0.273rem 0;
    font-weight: 500;
}

.sidebar-prayertime-time {
    @include mainfont;
    color: $primary;
    font-weight: bold;
    font-size: 1.1rem;
    min-width: 45px;
    margin: 0.273rem 0;
}

.sidebar-prayertimes {
    @include menu_decel;
    @include elevation2;
    @include normal-rounding;
    min-width: 29.659rem;
    padding: 1.023rem;
    margin: 0.273rem 0;
}

.sidebar-prayertimes-header {
    @include titlefont;
    font-size: 1.364rem;
    color: $onSecondaryContainer;
}

.sidebar-prayertimes-header-icon {
    margin-right: 0.682rem;
    color: $onSecondaryContainer;
}

.sidebar-prayertimes-time {
    @include mainfont;
    font-size: 1.023rem;
    color: mix($onSecondaryContainer, $secondaryContainer, 80%);
}

.sidebar-prayertimes-time-icon {
    margin-right: 0.682rem;
    color: mix($onSecondaryContainer, $secondaryContainer, 80%);
}

.sidebar-prayertimes-time-text {
    @include mainfont;
    font-size: 1.023rem;
    color: mix($onSecondaryContainer, $secondaryContainer, 80%);
}

.sidebar-prayertimes-time-remaining {
    @include mainfont;
    font-size: 0.818rem;
    color: mix($onSecondaryContainer, $secondaryContainer, 60%);
    margin: 0.273rem 0;
}

.sidebar-prayertimes-time-remaining-text {
    @include mainfont;
    font-size: 0.818rem;
    color: mix($onSecondaryContainer, $secondaryContainer, 60%);
    margin: 0.273rem 0;
}

// Prayer Times Module Styles
.prayer-times-box {
    @include normal-rounding;
    background-color: $surfaceContainerLow;
    padding: 0.682rem;
    margin: 0.341rem 0;
}

.prayer-times-header {
    @include titlefont;
    padding: 0.341rem;
    margin-bottom: 0.682rem;
    color: $onSurface;

    label {
        font-size: 1.2rem;
    }
}

.prayer-times-next {
    @include normal-rounding;
    background: linear-gradient(135deg,
            $primary 0%,
            mix($primary, $surfaceContainerHighest, 85%) 100%);
    color: $onPrimary;
    padding: 1.2rem 0.682rem;
    margin-bottom: 0.682rem;

    &:hover {
        background: linear-gradient(135deg,
                $primary 0%,
                mix($primary, $surfaceContainerHighest, 75%) 100%);
    }
}

.prayer-times-item {
    @include normal-rounding;
    padding: 1.2rem 0.682rem;
    background-color: $surfaceContainerHigh;
    margin: 0.341rem 0;
    transition: all 200ms ease;
    border: 1px solid transparent;

    &:hover {
        background-color: $surfaceContainerHighest;
        border-color: $outlineVariant;
    }
}

.prayer-times-name {
    @include mainfont;
    color: $onSurface;
    font-size: 1.1rem;
}

.prayer-times-time {
    @include mainfont;
    color: $primary;
    font-weight: bold;
    font-size: 1.1rem;
    min-width: 5.5rem;
}

.prayer-times-date {
    @include small-rounding;
    padding: 0.341rem 0.682rem;
    background: linear-gradient(to right,
            $surfaceContainerHigh 0%,
            mix($surfaceContainerHigh, $primary, 95%) 100%);
    color: $onSurfaceVariant;
    margin-bottom: 0.682rem;
    border-left: 3px solid $primary;

    label {
        font-size: 0.95rem;
    }
}

.prayer-times-remaining {
    @include small-rounding;
    color: mix($onSurface, $surface, 65%);
    font-size: 0.85rem;
    margin-top: 0.2rem;
}

.prayer-times-icon {
    @include icon-material;
    color: mix($onSurface, $surface, 75%);
    margin-right: 0.5rem;
}

.prayer-times-icon-next {
    @include icon-material;
    color: $onPrimary;
    margin-right: 0.5rem;
}

// Timer Module Styles
.sidebar-timer {
    @include normal-rounding;
    @include titlefont;
    color: $onSurface;
}

.sidebar-timer-btn {
    @include normal-rounding;
    padding: 0.2rem;
    min-width: 1.8rem;
    min-height: 1.8rem;
}

.sidebar-timer-btn-start {
    background-color: $primary;
    color: $onPrimary;
}

.sidebar-timer-delete {
    @include normal-rounding;
    padding: 0.2rem;
    min-width: 1.8rem;
    min-height: 1.8rem;
    background-color: $error;
    color: $onError;
}

.sidebar-timer-item {
    @include normal-rounding;
    @include titlefont;
    color: $onSurface;
}

.sidebar-timer-icon {
    @include icon-material;
}

.sidebar-todo-add {
    @include normal-rounding;
    background-color: $layer2;
    padding: 0.3em 0.5em;
    margin: 0 0.5em;
}

.sidebar-todo-add:focus {
    background-color: mix($primary, $surfaceContainerHighest, 15%);
}

.sidebar-todo-new {
    padding: 0.3em;
    margin: 0 0.5em;
}

.sidebar-todo-new:hover {
    background-color: mix($primary, $surfaceContainerHighest, 15%);
}

.sidebar-timer-presets {
    @include normal-rounding;
    background-color: $layer1;
    padding: 0.5em;
    margin: 0 0.5em;

    .sidebar-timer-btn {
        margin: 0.2em;
        padding: 0.3em;
    }
}

.quran-surah-name {
    font-family: "TE HAFS2 Tharwat Emara", "Noto Naskh Arabic",
        "Noto Sans Arabic", serif;
    font-size: 2.2rem;
    font-weight: bold;
    color: $primary;
    margin: 1rem 0;
}

.quran-loading {
    opacity: 0.7;
    transition: opacity 0.3s ease;
}

.quran-message {
    background-color: $layer1;
    border-radius: $rounding_medium;
    padding: 0.682rem;
    margin: 0.341rem 0
}

.quran-message-container {
    padding: 1rem;
    background-color: $surfaceContainerLow;
    border-radius: $rounding_large;
    margin: 1rem;
}

.welcome-message {
    padding: 2rem;

    .welcome-title {
        font-size: 2em;
        font-weight: bold;
        margin-bottom: 0.5rem;
        color: $onSurface;
    }

    .welcome-subtitle {
        opacity: 0.8;
        color: $onSurfaceVariant;
    }

    .welcome-examples {
        margin: 1rem 0;
        min-width: 300px;

        .welcome-example-btn {
            // padding: 0.7 5rem 1rem;
            border-radius: $rounding_large;
            background-color: $surfaceContainerLow;
            border: 1px solid $surfaceContainerHighest;
            transition: all 0.2s ease;
            min-width: 300px;
            color: $onSurface;

            &:hover {
                background-color: $surfaceContainerHigh;
                border-color: $primary;
            }
        }
    }

    .capability-item {
        opacity: 0.8;
        color: $onSurfaceVariant;
        margin: 0.25rem 0;
    }
}

.sidebar-collapsed {
    min-width: 350px;
}

.sidebar-expanded {
    min-width: 500px;
}

// Task Manager styles
.task-manager-widget {
    @include normal-rounding;
    background-color: $surfaceContainerLow;
    padding: 0.682rem;
}

.task-manager-header {
    padding: 0.341rem;
    margin-bottom: 0.682rem;
}

.task-manager-box {
    padding: 0.341rem;
}

.task-manager-item {
    @include normal-rounding;
    padding: 0.682rem;
    background-color: $surfaceContainerHigh;
    // transition: background-color 0.2s ease;

    &:hover {
        background-color: $surfaceContainerHighest;
    }
}

.task-manager-button {
    @include full-rounding;
    padding: 0.341rem;
    background-color: transparent;
    color: $error;
    transition: background-color 0.2s ease;

    &:hover {
        background-color: mix($error, transparent, 10%);
    }
}

.task-manager-refresh-button {
    @include full-rounding;
    padding: 0.341rem;
    background-color: transparent;
    color: $onSurface;
    transition: background-color 0.2s ease;

    &:hover {
        background-color: $surfaceContainerHighest;
    }
}

.task-manager-scrollable {
    min-height: 20.455rem;
}

.audio-files-widget {
    padding: 0.6rem;
    background-color: $background;
    border-radius: 0.9rem;
}

.media-header {
    margin: 0 0 0.6rem 0;
    padding: 0 0.4rem;
}

.media-header-title {
    font-weight: 500;
    font-size: 1rem;
    color: $onSurface;
}

.media-mode-button {
    min-height: 2rem;
    min-width: 2rem;
    border-radius: 2rem;
    background-color: $secondaryContainer;
    color: $onSecondaryContainer;
    margin-left: 8px;
    padding: 0;
}

.media-mode-button:hover {
    background-color: mix($secondaryContainer, $surfaceContainerHighest, 85%);
}

.media-mode-button:active {
    background-color: mix($secondaryContainer, $surfaceContainerHighest, 70%);
}

.empty-media-message {
    margin: 1rem 0;
}

.empty-media-icon {
    margin-bottom: 0.6rem;
    color: $outline;
    opacity: 0.8;
    font-size: 24px;
}

.empty-media-title {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: $onSurface;
}

.empty-media-subtitle {
    font-size: 0.85rem;
    opacity: 0.8;
    color: $onSurfaceVariant;
}

.audio-files-scrollable {
    border-radius: 0.6rem;
    min-height: 100px;
}

.audio-files-list {
    padding: 0.5rem;
}

.audio-files-button {
    padding: 0.6rem;
    margin-bottom: 0.3rem;
    border-radius: 0.6rem;
    background-color: mix($surfaceContainerLow, $hovercolor, 80%);
}

.audio-files-button:hover {
    background-color: mix($surfaceContainerLow, $hovercolor, 80%);
}

.audio-files-button:active {
    background-color: mix($surfaceContainerLow, $hovercolor, 80%);
}

.audio-files-icon {
    color: $secondary;
    font-size: 22px;
    margin-right: 8px;
    min-width: 22px;
}

.audio-files-label {
    font-size: 1rem;
    color: $onSurface;
    min-width: 150px;
}

.audio-files-player-name {
    font-weight: 600;
    font-size: 1rem;
    color: $onSurface;
    min-width: 120px;
}

.audio-files-player-track {
    font-size: 0.9rem;
    opacity: 0.8;
    color: $onSurfaceVariant;
    margin-top: 4px;
    min-width: 150px;
}

.audio-files-button-active {
    background-color: mix($surfaceContainerLow, $hovercolor, 80%);
    border-left: 3px solid $primary;
}

.audio-files-button-active .audio-files-player-name {
    color: $secondary;
    font-weight: bold;
}

.audio-files-button-active .audio-files-player-track {
    color: $secondary;
    opacity: 0.9;
}

.audio-files-button-active .audio-files-icon {
    color: $secondary;
}

.audio-files-button-last-played {
    background-color: mix($surfaceContainerLow, $hovercolor, 80%);
    padding-left: 0.4rem;
}

.media-container {
    border-radius: 0.8rem;
    padding: 0.8rem;
    margin-top: 0.4rem;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    background-color: $surfaceContainerLow; /* Use onSecondary for background */
    min-height: 160px; /* Reduced height */
}

.media-controls-box {
    margin-bottom: 0.5rem;
}

.media-title-label {
    font-weight: 500;
    font-size: 0.95rem;
    margin-bottom: 0.5rem;
    padding: 0.5rem;
    color: $secondary; /* Use secondary color for text */
    min-width: 180px;
    background-color: $background; /* Use theme variable instead of black */
    border-radius: 0.4rem;
}

.media-progress-container {
    margin-bottom: 0.4rem;
    padding: 0;
}

.media-progress-bar {
    min-height: 0.4rem;
    border-radius: 0.4rem;
    margin: 0 0.4rem 0.2rem;
}

.media-progress-bar highlight {
    background-color: $secondary;
    border-radius: 0.4rem;
}

.media-progress-bar trough {
    background-color: $surfaceContainerHighest;
    border-radius: 0.4rem;
    min-height: 0.4rem;
}

.media-progress-bar slider {
    min-height: 0.9rem;
    min-width: 0.9rem;
    border-radius: 0.9rem;
    background-color: $secondary;
    border: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.media-time-label {
    font-size: 0.8rem;
    opacity: 0.9;
    font-family: monospace;
    margin-bottom: 0.2rem;
    color: $secondary; /* Use secondary color for text */
    padding: 0;
}

.media-bottom-container {
    margin-top: 0.5rem; /* Reduced space above bottom container */
    padding: 0;
    background-color: transparent;
}

.media-buttons-box {
    padding: 0.5rem 0;
    background-color: transparent;
}

.media-control-button {
    min-height: 2.1rem;
    min-width: 2.1rem;
    border-radius: 0.3rem; /* More square corners for M3 design */
    padding: 0;
    margin: 0 0.15rem;
    background-color: $primary;
    color: $onPrimary;
    box-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.media-control-button:hover {
    background-color: mix($primary, $hovercolor, 90%);
    box-shadow: 0 1px 3px rgba(0,0,0,0.15);
}

.media-control-button:active {
    background-color: mix($primary, $activecolor, 80%);
    box-shadow: 0 1px 1px rgba(0,0,0,0.1);
}

.media-control-button-toggled {
    background-color: mix($secondary, $primary, 30%);
    color: $onPrimary;
    box-shadow: inset 0 1px 1px rgba(0,0,0,0.1);
}

.media-playpause-button {
    min-height: 2.1rem;
    min-width: 2.1rem;
    border-radius: 0.3rem; /* More square corners for M3 design */
    box-shadow: 0 1px 3px rgba(0,0,0,0.2);
}

.material-welcome-logo {
    padding: 0.5rem;
    min-width: 4rem;
    margin-left: 0.135rem
}

/* This is a duplicate rule that was causing issues */

.media-section-title {
    font-weight: bold;
    font-size: 1.1em;
    color: $onSurfaceVariant;
    margin-bottom: 8px;
    padding-left: 5px;
}

.media-header {
    margin-bottom: 8px;
    padding: 4px;
}

.media-header-title {
    font-weight: 600;
    font-size: 1rem;
    color: $onSurface;
    margin: 4px 0;
}

.media-mode-button {
    @include full-rounding;
    min-width: 30px;
    min-height: 30px;
    padding: 6px;
    background-color: $primary;
    color: $onPrimary;
    margin-left: 8px;

    &:hover {
        background-color: mix($primary, $hovercolor, 70%);
    }
}

.audio-files-player-name {
    font-weight: 600;
    font-size: 0.95rem;
    color: $onSurface;
}

.audio-files-player-track {
    font-size: 0.85rem;
    color: $onSurfaceVariant;
    margin-top: 2px;
}

.audio-files-button-active {
    background-color: mix($surfaceContainerLow, $hovercolor, 80%);
    border-left: 3px solid $primary;

    .audio-files-player-name {
        color: $secondary;
        font-weight: bold;
    }

    .audio-files-player-track {
        color: $secondary;
        opacity: 0.9;
    }

    .audio-files-icon {
        color: $secondary;
    }
}

/* This is a duplicate rule that was causing issues */

// .media-button-left-margin {
//     margin-left: 5px;
// }

// .media-button-right-margin {
//     margin-right: 5px;
// }

