@mixin notif-rounding {
    @include normal-rounding;
}

.notif-low {
    @include notif-rounding;
    background-color: $layer2;
    color: $onLayer2;
    padding: $rounding_small;
    padding-right: $rounding_small + 0.545rem;
}

.notif-normal {
    @include notif-rounding;
    background-color: $layer2;
    color: $onLayer2;
    padding: $rounding_small;
    padding-right: $rounding_small + 0.545rem;
}

.notif-critical {
    @include notif-rounding;
    background-color: $secondaryContainer;
    color: $onSecondaryContainer;
    padding: $rounding_small;
    padding-right: $rounding_small + 0.545rem;
}

.notif-clicked-low {
    background-color: $layer1Hover;
}

.notif-clicked-normal {
    background-color: $layer1Hover;
}

.notif-clicked-critical {
    background-color: $onSecondary;
    color: $onSecondaryContainer;
}

.popup-notif-low {
    @include notif-rounding;
    min-width: 30.682rem;
    background-color: $layer2;

    @if $transparent ==True {
        background-color: $layer0;
    }

    border: 0.034rem solid $outlineVariant;
    color: $onLayer2;
    padding: $rounding_small;
    padding-right: $rounding_small + 0.545rem;
}

.popup-notif-normal {
    @include notif-rounding;
    min-width: 30.682rem;
    background-color: $layer2;

    @if $transparent ==True {
        background-color: $layer0;
    }

    border: 0.034rem solid $outlineVariant;
    color: $onLayer2;
    padding: $rounding_small;
    padding-right: $rounding_small + 0.545rem;
}

.popup-notif-critical {
    @include notif-rounding;
    min-width: 30.682rem;
    background-color: $secondaryContainer;
    border: 0.034rem solid $onSecondaryContainer;
    color: $onSecondaryContainer;
    padding: $rounding_small;
    padding-right: $rounding_small + 0.545rem;
}

.popup-notif-clicked-low {
    background-color: $surfaceContainerLow;
}

.popup-notif-clicked-normal {
    background-color: $surfaceContainerLow;
}

.popup-notif-clicked-critical {
    background-color: $onSecondary;
    color: $onSecondaryContainer;
}

.notif-body-low {
    color: $outline;
}

.notif-body-normal {
    color: $outline;
}

.notif-body-critical {
    color: mix($onSecondaryContainer, $secondaryContainer, 67%);
}

.notif-icon {
    @include full-rounding;
    min-width: 3.409rem;
    min-height: 3.409rem;
    font-size: 2.182rem;
}

.notif-icon-material {
    background-color: $secondaryContainer;
    color: $onSecondaryContainer;
}

.notif-icon-material-low {
    background-color: $secondaryContainer;
    color: $onSecondaryContainer;
}

.notif-icon-material-normal {
    background-color: $secondaryContainer;
    color: $onSecondaryContainer;
}

.notif-icon-material-critical {
    background-color: $secondary;
    color: $onSecondary;
}

.notif-expand-btn {
    @include notif-rounding;
    min-width: 1.841rem;
    min-height: 1.841rem;
}

.notif-expand-btn:hover,
.notif-expand-btn:focus {
    background: $layer2Hover;
}

.notif-expand-btn:active {
    background: $layer2Active;
}

.notif-listaction-btn {
    @include notif-rounding;
    padding: 0.341rem 0.682rem;
}

.notif-listaction-btn:hover,
.notif-listaction-btn:focus {
    background-color: $layer2Hover;
}

.notif-listaction-btn:active {
    background-color: $layer2Active;
}

.notif-listaction-btn-enabled {
    background-color: $secondaryContainer;
    color: $onSecondaryContainer;
}

.notif-listaction-btn-enabled:hover,
.notif-listaction-btn-enabled:focus {
    background-color: mix($secondaryContainer, $onSecondaryContainer, 90%);
}

.notif-listaction-btn-enabled:active {
    background-color: mix($secondaryContainer, $onSecondaryContainer, 75%);
}

.osd-notif {
    @include notif-rounding;
    background-color: $layer0;
    min-width: 30.682rem;
}

.notif-circprog-low {
    transition: 0ms linear;
    min-width: 0.136rem; // line width
    min-height: 3.136rem;
    padding: 0rem;
    color: $onSecondaryContainer;
}

.notif-circprog-normal {
    transition: 0ms linear;
    min-width: 0.136rem; // line width
    min-height: 3.136rem;
    padding: 0rem;
    color: $onSecondaryContainer;
}

.notif-circprog-critical {
    transition: 0ms linear;
    min-width: 0.136rem; // line width
    min-height: 3.136rem;
    padding: 0rem;
    color: $secondaryContainer;
}

.notif-actions {
    min-height: 2.045rem;
}

.notif-action {
    @include small-rounding;
}

.notif-action-low {
    background-color: $surfaceContainerHighest;
    color: $onLayer2;
}

.notif-action-low:focus,
.notif-action-low:hover {
    border: 0.040rem solid $outlineVariant;
}

.notif-action-low:active {
    background-color: $surfaceBright;
}

.notif-action-normal {
    background-color: $surfaceContainerHighest;
    color: $onSurface;
}

.notif-action-normal:focus,
.notif-action-normal:hover {
    border: 0.040rem solid $outlineVariant;
}

.notif-action-normal:active {
    background-color: $surfaceBright;
}

.notif-action-critical {
    background-color: mix($secondary, $onSecondary, 30%);
    color: $onSurfaceVariant;
}

.notif-action-critical:focus,
.notif-action-critical:hover {
    border: 0.040rem solid $outline;
}

.notif-action-critical:active {
    background-color: mix($secondary, $onSecondary, 40%);
}
