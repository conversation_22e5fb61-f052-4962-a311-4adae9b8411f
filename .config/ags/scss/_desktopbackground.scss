.bg-wallpaper-transition {
    transition: 1400ms cubic-bezier(0.05, 0.7, 0.1, 1);
    font-size: 1px;
}

@mixin bg-textshadow {
    // text-shadow: mix($shadow, $secondaryContainer, 50%) 1px 0px 3px;
}

.bg-time-box {
    @include large-rounding;
    margin: 2.045rem;
    padding: 0.682rem;
}

.bg-time-clock {
    @include titlefont;
    @include bg-textshadow;
    font-size: 5.795rem;
    color: $onLayer0;
}

.bg-time-date {
    @include titlefont;
    @include bg-textshadow;
    font-size: 2.591rem;
    color: $onLayer0;
}

.bg-distro-box {
    @include large-rounding;
    margin: 2.045rem;
    padding: 0.682rem;
}

.bg-distro-txt {
    @include titlefont;
    @include bg-textshadow;
    font-size: 1.432rem;
    color: $onLayer0;
}

.bg-distro-name {
    @include titlefont;
    @include bg-textshadow;
    font-size: 1.432rem;
    color: $onSecondaryContainer;
}

.bg-graph {
    color: rgba(255, 255, 255, 0.5);
    border-radius: 0.614rem;
    border: 0.682rem solid;
}

.bg-quicklaunch-title {
    @include mainfont;
    color: $onSurfaceVariant;
}

.bg-quicklaunch-btn {
    @include mainfont;
    @include full-rounding;
    background-color: $layer2;
    color: $onLayer2;
    min-width: 4.432rem;
    min-height: 2.045rem;
    padding: 0.273rem 0.682rem;
}

.bg-quicklaunch-btn:hover,
.bg-quicklaunch-btn:focus {
    background-color: $layer2Hover;
}

.bg-quicklaunch-btn:active {
    background-color: $layer2Active;
}

.bg-system-bg {
    @include normal-rounding;
    // background-color: $background;
}

.bg-system-circprog {
    @include fluent_decel_long;
    min-width: 0.205rem; // Trough stroke width
    min-height: 4.091rem; // Diameter
    font-size: 0px;
    padding: 0rem;
    background-color: $layer2;
}