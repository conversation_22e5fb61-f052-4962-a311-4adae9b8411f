.auva-clock {
    @include mainfont;
    font-size: 1.8rem;
    color: $onPrimary
}

.auva-date {
    @include mainfont;
    font-size: 1.4rem;
    color: $onPrimary
}

.auva-day {
    font-family: 'steelfish';
    font-size: 4rem;
}

.auva-day-color {
    font-family: 'steelfish';
    color: $primary;
    font-size: 4rem;

}

.auva-weather {
    @include mainfont;
    font-size: 1.3rem;
    font-weight: 300;
}

.auva-greeting {
    font-family: 'Big John';
    font-size: 3rem
}

.auva-circprog-main {
    @include fluent_decel_long;
    min-width: 0.705rem;
    min-height: 4.7rem;
    padding: 0rem;
    background: linear-gradient(to right, $primary 0%, $surface 100%);
}

// Container for each resource to ensure consistent sizing and alignment
.auva-resource-container {
    min-height: 4.7rem; // Match the height of the circular progress
    min-width: 4.7rem; // Match the width of the circular progress
}

// Center the icon in the circular progress - match system resources widget
.auva-circprog-main + .icon-material {
    margin: 0;
    padding: 0;
    font-size: 1.8182rem; // txt-hugeass size
    // Position adjustments for perfect centering
    margin-top: 0px;
    margin-left: 0px;
}

.auva-clock-box {
    @include full-rounding;
    padding: 0.2rem 1.5rem;
    background-color: $primary;
}