.overview-search-box {
    @include fluent_accel;
    min-width: 24.636rem;
    min-height: 3.409rem;
    margin-bottom:4rem;
    border-radius: 0 0 $rounding_mediumlarge $rounding_mediumlarge;
    padding-right: 3.5rem;
    border-bottom:0.3px solid $outliner;
    background-color: $background;
    color: $onBackground;

    selection {
        background-color: $secondary;
        color: $onSecondary;
    }

    caret-color: transparent;
}

.overview-search-box-spotlight {
    @include anim-enter;
    min-width: 24.636rem;
    min-height: 3.409rem;
    margin-bottom:4rem;
    border-radius: $rounding_mediumlarge;
    padding: 0.2rem 3.5rem 0.2rem 0;
    border: 0.3px solid $outliner;
    background-color: $background;
    color: $onSurfaceVariant;

    selection {
        background-color: $secondary;
        color: $onSecondary;
    }

    caret-color: transparent;
}
.overview-search-box-extended {
    min-width: 45rem;
    caret-color: $onSecondaryContainer;
    margin-bottom:0.4rem;
    padding-left:1.364rem;
}

.overview-search-prompt {
    // @include anim-enter;
    color: $subtext;

}

.overview-search-icon {
    margin: 0rem 1.023rem;
}

.overview-search-prompt-box {
    margin-left: -18.545rem;
    margin-right: $elevation_margin + 0.068rem;
}

.overview-search-icon-box {
    margin-left: -18.545rem;
    margin-right: $elevation_margin + 0.068rem;
}

.overview-search-results {
    border-radius: $rounding_medium;
    @include elevation-border;
    @include elevation2;
    margin: 0.8rem 4rem 4rem 4rem;
    min-width: 48.5rem;
    padding: 0.682rem;
    background-color: $layer0;
    color: $onLayer0;
}

.overview-search-results-icon {
    margin: 0rem 0.682rem;
    font-size: 2.386rem;
    min-width: 2.386rem;
    min-height: 2.386rem;
    
}

.overview-search-results-txt {
    margin-right: 0.682rem;
}

.overview-search-results-txt-cmd {
    margin-right: 0.682rem;
    @include techfont;
    font-size: 1.227rem;
}

.overview-search-result-btn {
    @include normal-rounding;
    padding: 0.341rem;
    min-width: 2.386rem;
    min-height: 2.386rem;

    caret-color: transparent;
}

.overview-search-result-btn:hover,
.overview-search-result-btn:focus {
    background-color: $layer2;
}

.overview-search-result-btn:active {
    background-color: $layer2Hover;
}
.overview-round {
    border-radius: $rounding_mediumlarge $rounding_mediumlarge 0 0;
}
.overview-tasks {
    border-top: 0.2px solid $outliner;
    padding: 1rem;
    background-color: $layer0;
    color: $onLayer0;
}

.overview-tasks-workspace {
    @include normal-rounding;
    margin: 0.341rem;
    background-color: $layer1;
}

.overview-tasks-workspace-number {
    @include mainfont;
    color: $onSurfaceVariant;
}

.overview-tasks-window {
    @include normal-rounding;
    @include menu_decel;
    background-color: transparentize($layer3, 0.2);
    color: $onSurface;
    border: 0.1px solid $outliner;
}

.overview-tasks-window:hover,
.overview-tasks-window:focus {
    background-color: transparentize($secondaryContainer, 0.3);
}

.overview-tasks-window:active {
    background-color: transparentize($secondaryContainer, 0);
}

.overview-tasks-window-selected {
    background-color: transparentize($secondaryContainer, 0.3);
}

.overview-tasks-window-dragging {
    opacity: 0.2;
}
