.test {
    background-image: linear-gradient(45deg, #f4d609 0%, #f4d609 10%, #212121 10%, #212121 20%, #f4d609 20%, #f4d609 30%, #212121 30%,
            #212121 40%, #f4d609 40%, #f4d609 50%, #212121 50%, #212121 60%, #f4d609 60%,
            #f4d609 70%, #212121 70%, #212121 80%, #f4d609 80%, #f4d609 90%, #212121 90%, #212121 100%);
    background-repeat: repeat;
}
.elevation {
   border-radius: $rounding_medium;
   margin:0.76rem;
   
}
.shadow-window {
    box-shadow: 
    0 5px 15px rgba($shadow, 0.15), /* Lighter, broader blur for depth */
    0 14px 25px rgba($shadow, 0.2),  /* Subtle mid-distance shadow */
    0 20px 28px rgba($shadow, 0.05),  /* Subtle mid-distance shadow */
}

.shadow-window-light {
    box-shadow: 
    0 1px 10px rgba($shadow, 0.15), /* Lighter, broader blur for depth */
    0 0 25px rgba($shadow, 0.2),  /* Subtle mid-distance shadow */
}
.test-size {
    min-height: 3rem;
    min-width: 3rem;
}

.txt-title {
    @include titlefont;
    font-size: 2.045rem;
}

.txt-title-small {
    @include titlefont;
    font-size: 1.364rem;
}

.techfont {
    @include techfont;
}

.txt-reading {
    @include readingfont;
}

.no-anim {
    @include noanim;
}

.txt {
    color: $onBackground;
}

.txt-primary {
    color: $primary;
}

.txt-onSecondaryContainer {
    color: $onSecondaryContainer;
}

.txt-onSurfaceVariant {
    color: $onSurfaceVariant;
}

.txt-onLayer1 {
    color: $onLayer1;
}

.txt-shadow {
    text-shadow: 1px 2px 20px rgba(0, 0, 0, 0.6);
    // margin: 10px;
}
.txt-monospace {
    font-family: "Iosevka"
}
.txt-gigantic {
    @include mainfont;
    font-size: 3rem;
}

.txt-massive {
    @include mainfont;
    font-size: 2.7273rem;
}

.txt-hugerass {
    @include mainfont;
    font-size: 2.045rem;
}

.txt-hugeass {
    @include mainfont;
    font-size: 1.8182rem;
}

.txt-larger {
    @include mainfont;
    font-size: 1.6363rem;
}

.txt-large {
    //16pt
    @include mainfont;
    font-size: 1.4545rem;
}

.txt-norm {
    //14pt
    @include mainfont;
    font-size: 1.2727rem;
}

.txt-small {
    //12pt
    @include mainfont;
    font-size: 1.0909rem;
}

.txt-smallie {
    //11pt
    @include mainfont;
    font-size: 1rem;
}

.txt-smaller {
    //10pt
    @include mainfont;
    font-size: 0.9091rem;
}

.txt-tiny {
    @include mainfont;
    font-size: 0.7273rem;
}

.txt-poof {
    font-size: 0px;
}

.txt-subtext {
    @include subtext;
}

.txt-action {
    @include actiontext;
}

.txt-thin {
    font-weight: 300;
}

.txt-semibold {
    font-weight: 500;
}

.txt-bold {
    font-weight: bold;
}

.txt-italic {
    font-style: italic;
}

.btn-primary {
    @include full-rounding;
    background-color: $primary;
    color: $onPrimary;
    padding: 0.682rem 1.023rem;
}

.titlefont {
    @include titlefont;
}

.mainfont {
    @include mainfont;
}

.icon-material {
    @include icon-material;
}

.icon-nerd {
    @include icon-nerd;
}

.separator-line {
    background-color: mix($subtext, $surface, 50%);
    min-width: 0.068rem;
    min-height: 0.068rem;
}

.separator-circle {
    @include full-rounding;
    background-color: $outline;
    margin: 0rem 0.682rem;
    min-width: 0.273rem;
    min-height: 0.273rem;
}

.spacing-h-3>* {
    margin-right: 0.205rem;
}

.spacing-h-3>*:last-child {
    margin-right: 0rem;
}

.spacing-v-3>* {
    margin-bottom: 0.205rem;
}

.spacing-v-3>*:last-child {
    margin-bottom: 0rem;
}

.spacing-v-15>* {
    margin-bottom: 1.023rem;
}

.spacing-v-15>*:last-child {
    margin-bottom: 0rem;
}

.spacing-h-15>* {
    margin-right: 1.023rem;
}

.spacing-h-15>*:last-child {
    margin-right: 0rem;
}

.spacing-h-15>revealer>* {
    margin-right: 1.023rem;
}

.spacing-h-15>revealer:last-child>* {
    margin-right: 0rem;
}

.spacing-h-15>scrolledwindow>* {
    margin-right: 1.023rem;
}

.spacing-h-15>scrolledwindow:last-child>* {
    margin-right: 0rem;
}

.spacing-v-5>box {
    margin-bottom: 0.341rem;
}

.spacing-v-5>box:last-child {
    margin-bottom: 0rem;
}

.spacing-v-5>* {
    margin-bottom: 0.341rem;
}

.spacing-v-5>*:last-child {
    margin-bottom: 0rem;
}

.spacing-v-5-revealer>revealer>* {
    margin-bottom: 0.341rem;
}

.spacing-v-5-revealer>revealer:last-child>* {
    margin-bottom: 0rem;
}

.spacing-v-5-revealer>scrolledwindow>* {
    margin-bottom: 0.341rem;
}

.spacing-v-5-revealer>scrolledwindow:last-child>* {
    margin-bottom: 0rem;
}

.spacing-h-4>* {
    margin-right: 0.273rem;
}

.spacing-h-4>*:last-child {
    margin-right: 0rem;
}

.spacing-h-4>overlay>*:first-child {
    margin-right: 0.273rem;
}

.spacing-h-4>overlay:last-child>* {
    margin-right: 0rem;
}

.spacing-h-5>* {
    margin-right: 0.341rem;
}

.spacing-h-5>*:last-child {
    margin-right: 0rem;
}

.spacing-h-5>widget>* {
    margin-right: 0.341rem;
}

.spacing-h-5>widget:last-child>* {
    margin-right: 0rem;
}

.spacing-h-5>revealer>* {
    margin-right: 0.341rem;
}

.spacing-h-5>revealer:last-child>* {
    margin-right: 0rem;
}

.spacing-h-5>scrolledwindow>* {
    margin-right: 0.341rem;
}

.spacing-h-5>scrolledwindow:last-child>* {
    margin-right: 0rem;
}

.spacing-v-minus5>* {
    margin-bottom: -0.341rem;
}

.spacing-v-minus5>*:last-child {
    margin-bottom: 0rem;
}

.spacing-h-10>* {
    margin-right: 0.682rem;
}

.spacing-h-10>*:last-child {
    margin-right: 0rem;
}

.spacing-h-10>revealer>* {
    margin-right: 0.682rem;
}

.spacing-h-10>revealer:last-child>* {
    margin-right: 0rem;
}

.spacing-h-10>scrolledwindow>* {
    margin-right: 0.682rem;
}

.spacing-h-10>scrolledwindow:last-child>* {
    margin-right: 0rem;
}

.spacing-h-10>flowboxchild>* {
    margin-right: 0.682rem;
}

.spacing-h-10>flowboxchild:last-child>* {
    margin-right: 0rem;
}

.spacing-v-10>* {
    margin-bottom: 0.682rem;
}

.spacing-v-10>*:last-child {
    margin-bottom: 0rem;
}

.spacing-h-20>* {
    margin-right: 1.364rem;
}

.spacing-h-20>*:last-child {
    margin-right: 0rem;
}

.spacing-v-20>* {
    margin-bottom: 1.364rem;
}

.spacing-v-20>*:last-child {
    margin-bottom: 0rem;
}

.spacing-h-30>* {
    margin-right: 1.364rem;
}

.spacing-h-30>*:last-child {
    margin-right: 0rem;
}

.spacing-v-30>* {
    margin-bottom: 1.364rem;
}

.spacing-v-30>*:last-child {
    margin-bottom: 0rem;
}

.anim-enter {
    @include anim-enter;
}

.anim-exit {
    @include anim-exit;
}

.button-minsize {
    @include button-minsize;
}

@each $spacing in (5, 8, 10, 15, 20) {
    @each $dir in ("top", "bottom", "left", "right") {
        .margin-#{$dir}-#{$spacing} {
            margin-#{$dir}: 0.068rem * $spacing;
        }
        .padding-#{$dir}-#{$spacing} {
            padding-#{$dir}: 0.068rem * $spacing;
        }
    }
    .padding-#{$spacing} {
        padding: 0.068rem * $spacing;
    }
    .margin-#{$spacing} {
        padding: 0.068rem * $spacing;
    }
}

.width-10 {
    min-width: 0.682rem;
}

.height-10 {
    min-width: 0.682rem;
}

.invisible {
    opacity: 0;
    background-color: transparent;
    color: transparent;
}

.spacing-h--5>box {
    margin-right: -0.341rem;
}

.spacing-h--5>box:last-child {
    margin-right: 0rem;
}

.spacing-v--5>* {
    margin-bottom: -0.341rem;
}

.spacing-v--5>*:last-child {
    margin-bottom: 0rem;
}

.spacing-h--10>* {
    margin-left: -1.364rem;
}

.spacing-h--10>*:first-child {
    margin-left: 0rem;
}

.spacing-v--10>* {
    margin-bottom: -0.682rem;
}

.spacing-v--10>*:last-child {
    margin-bottom: 0rem;
}

.spacing-v--10>* {
    margin-bottom: -0.682rem;
}

.spacing-v--10>*:last-child {
    margin-bottom: 0rem;
}

.spacing-h--20>* {
    margin-left: -1.364rem;
}

.spacing-h--20>*:first-child {
    margin-left: 0rem;
}

.instant {
    transition: 0ms;
}

.menu-decel {
    @include menu_decel;
}

.element-show {
    @include element_easeInOut;
}

.element-hide {
    @include element_easeInOut;
}

.element-move {
    @include element_easeInOut;
}

.element-decel {
    @include element_decel;
}

.element-bounceout {
    @include element_bounceOut;
}

.element-accel {
    @include element_accel;
}
.sec-txt {
    color:$secondary;
}
.padding01{
    padding: 0rem 1rem;

}
.txt-percent{
    font-family: Helvetica;
    font-weight: 900;
}
.onSurfaceVariant{
    color: $onSurfaceVariant;
}
.quran-arabic-text {
    font-family: "TE HAFS2 Tharwat Emara", "Noto Naskh Arabic",
        "Noto Sans Arabic", serif;
        color: $onSurfaceVariant;
}