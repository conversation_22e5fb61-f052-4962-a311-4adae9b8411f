.osd-bg {
    min-width: 8.864rem;
    min-height: 3.409rem;
}

.osd-value {
    @include elevation-border;
    @include elevation2;
    background-color: $layer0;
    border-radius: 1.023rem;
    padding: 0.625rem 1.023rem;
    padding-top: 0.313rem;
}

.osd-progress {
    min-height: 0.955rem;
    min-width: 0.068rem;
    padding: 0rem;
    border-radius: 10rem;
    @include fluent_decel;

    trough {
        min-height: 0.954rem;
        min-width: 0.068rem;
        border-radius: 10rem;
        background-color: $layer2;
        // border: 0.068rem solid $onSecondaryContainer;
    }

    progress {
        @include fluent_decel;
        min-height: 0.680rem;
        min-width: 0.680rem;
        margin: 0rem 0.137rem;
        border-radius: 10rem;
        background-color: $onLayer2;
    }
}

.osd-label {
    font-size: 1.023rem;
    font-weight: 500;
    margin-top: 0.341rem;
}

.osd-value-txt {
    @include titlefont;
    font-size: 1.688rem;
    font-weight: 500;
    color: $onLayer0;
}

.osd-value-icon {
    font-size: 1.688rem;
}

.osd-brightness {
    color: $brightnessOnLayer0;
}
.osd-brightness-progress {
    progress {
        background-color: $brightnessOnLayer0;
    }
}
.osd-volume {
    color: $volumeOnLayer0;
}
.osd-volume-progress {
    progress {
        background-color: $volumeOnLayer0;
    }
}

.osd-notifs {
    padding-top: 0.313rem;
}

.osd-round {
    border-radius: 0 0 $rounding_medium $rounding_medium;
}

.osd-colorscheme {
    background-color: $background;
    padding: 0 0.626rem;
    border-bottom: 0.5px solid $outliner;
    // margin: 0rem 0rem  1rem 0rem;
}

.osd-colorscheme-settings {
    background-color: $layer1;
    padding: 0.313rem 0.626rem;
    @include small-rounding;
}

.osd-color {
    border-radius: 0.650rem;
    -gtk-outline-radius: 0.650rem;
    min-width: 2.727rem;
    min-height: 1.705rem;
    padding: 0rem 0.341rem;
    font-weight: bold;

    box {
        @include small-rounding;
        margin: 0.409rem;
    }
}

.osd-color-primary {
    background-color: $primary;
    color: $onPrimary;
    box { background-color: $onPrimary; }
}
.osd-color-primaryContainer {
    background-color: $primaryContainer;
    color: $onPrimaryContainer;
    box { background-color: $onPrimaryContainer; }
}
.osd-color-secondary {
    background-color: $secondary;
    color: $onSecondary;
    box { background-color: $onSecondary; }
}
.osd-color-secondaryContainer {
    background-color: $secondaryContainer;
    color: $onSecondaryContainer;
    box { background-color: $onSecondaryContainer; }
}

.osd-color-tertiary {
    background-color: $tertiary;
    color: $onTertiary;
    box { background-color: $onTertiary; }
}
.osd-color-tertiaryContainer {
    background-color: $tertiaryContainer;
    color: $onTertiaryContainer;
    box { background-color: $onTertiaryContainer; }
}

.osd-color-error {
    background-color: $error;
    color: $onError;
    box { background-color: $onError; }
}
.osd-color-errorContainer {
    background-color: $errorContainer;
    color: $onErrorContainer;
    box { background-color: $onErrorContainer; }
}

.osd-color-surface {
    background-color: $surface;
    color: $onSurface;
    border: 0.068rem solid $outlineVariant;
    box { background-color: $onSurface; }
}

.osd-color-surfaceContainer {
    background-color: $surfaceContainer;
    color: $onSurface;
    box { background-color: $onSurface; }
}

.osd-color-inverseSurface {
    background-color: $inverseSurface;
    color: $inverseOnSurface;
    box { background-color: $onSurfaceVariant; }
}

.osd-color-surfaceVariant {
    background-color: $surfaceVariant;
    color: $onSurfaceVariant;
    box { background-color: $onSurfaceVariant; }
}
.osd-color-L1 {
    background-color: $layer1;
    color: $onLayer1;
    box { background-color: $onLayer1; }
}

.osd-color-layer0 {
    background-color: $layer0;
    color: $onLayer0;
    box { background-color: $onLayer0; }
}

.osd-settings-btn-arrow {
    @include full-rounding;
    @include icon-material;
    min-width: 1.705rem;
    min-height: 1.705rem;
    color: $onSurface;

    &:hover {
        background-color: $surfaceContainerHigh;
    }
    &:active {
        background-color: $surfaceContainerHighest;
    }
}

.osd-show {
    transition: 200ms cubic-bezier(0.1, 1, 0, 1);
}

.osd-hide {
    transition: 190ms cubic-bezier(0.85, 0, 0.15, 1);
}

.recorder-bg {
    background-color: $background;
    color: $onBackground;
    padding : 1rem;
    border-left: 0.2px solid $outliner;
}
.recorder-btn {
    @include full-rounding;
    @include element_decel;
    background-color: $surfaceContainerHigh;
    padding:1.5rem;
    min-width: 1.8rem;
    min-height: 1.8rem;
    color: $onSurface;
}
.recording-state {
    @include titlefont;
    color: $onSurface;

}
.recorder-btn-red {
    @include full-rounding;
    @include element_decel;
    background-color: $error;
    color: $onError;
    padding:1.5rem;
    min-width: 1.8rem;
    min-height: 1.8rem;
}
.record-rounding {
    border-radius: $rounding_large 0 0  $rounding_large;
}