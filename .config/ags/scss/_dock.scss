.dock-bg {
    @include shadows1;
    background-color: $layer0;
    border-top : 0.5px solid $outliner;
}
.dock-round {
    border-top : 0.5px solid $outliner;
    border-radius: $rounding_medium $rounding_medium 0 0;
}
.dock-app-btn-animate {
    transition-property: color;
    transition-duration: 0.5s;
}

.dock-app-btn {
    @include normal-rounding;
}
.unpinned-dock-app-btn {
    color: $surfaceContainerLowest;

}
.pinned-dock-app-btn {
    @include full-rounding;
    color: $secondary;
}

.dock-app-btn:hover,
.dock-app-btn:focus {
    background-color: $layer0Hover;
}

.dock-app-btn:active {
    background-color: $layer0Active;
}

.dock-app-icon {
    min-width: 3.409rem;
    min-height: 3.409rem;
    font-size: 3.409rem;
}

.notification-badge {
    background-color: $error;
    color: $onError;
    border-radius: 0.6rem;
    min-width: 1.2rem;
    min-height: 1.2rem;
    margin: 0;
    padding: 0;
    font-size: 0.8rem;
    font-weight: bold;
}

.notification-count {
    font-size: 0.8rem;
    font-weight: bold;
    padding: 0;
    margin: 0;
}

.window-indicators {
    margin-bottom: 0.2rem;
    min-height: 0.4rem;
    min-width: 2rem;
}

.window-indicator {
    min-width: 0.5rem;
    min-height: 0.25rem;
    margin: 0 0.1rem;
    border-radius: 0.1rem;
    background-color: $onSurfaceVariant;
    opacity: 0.7;
}

.active-window {
    background-color: $primary;
    opacity: 1;
}

.window-count {
    font-size: 0.6rem;
    color: $onSurfaceVariant;
    margin-left: 0.2rem;
}

.dock-separator {
    min-width: 0.04rem;
    opacity: 0.4;
    margin:0.45rem 0.6rem;
    background-color: $outline;
}
