// Material You theme for Telegram Desktop
// Generated by matugen

<PERSON>_GRAY: #{{colors.outline.default.hex_stripped}};
COLOR_DARK: #{{colors.surface_variant.default.hex_stripped}};

windowBg: #{{colors.background.default.hex_stripped}}; // Main background
windowFg: #{{colors.on_background.default.hex_stripped}}; // Main text
windowBgOver: #{{colors.surface_variant.default.hex_stripped}}; // Generic background on hover
windowBgRipple: #{{colors.surface_variant.default.hex_stripped}}; // Ripple effect
windowFgOver: #{{colors.on_surface_variant.default.hex_stripped}}; // Text on hover
windowSubTextFg: #{{colors.outline.default.hex_stripped}}; // Minor text
windowSubTextFgOver: #{{colors.outline.default.hex_stripped}}; // Minor text on hover
windowBoldFg: #{{colors.on_background.default.hex_stripped}}; // Bold text
windowBoldFgOver: #{{colors.on_surface_variant.default.hex_stripped}}; // Bold text on hover
windowBgActive: #{{colors.primary.default.hex_stripped}}; // Active items background
windowFgActive: #{{colors.on_primary.default.hex_stripped}}; // Active items text
windowActiveTextFg: #{{colors.primary.default.hex_stripped}}; // Active items text
windowShadowFg: #{{colors.shadow.default.hex_stripped}}; // Window shadow
windowShadowFgFallback: #{{colors.shadow.default.hex_stripped}}; // Fallback for shadow

shadowFg: #{{colors.shadow.default.hex_stripped}}; // General shadow
slideFadeOutBg: #{{colors.background.default.hex_stripped}};
slideFadeOutShadowFg: #{{colors.shadow.default.hex_stripped}};

imageBg: #{{colors.surface.default.hex_stripped}};
imageBgTransparent: #{{colors.surface.default.hex_stripped}};

activeButtonBg: #{{colors.primary.default.hex_stripped}}; // Active button background
activeButtonBgOver: #{{colors.primary_container.default.hex_stripped}}; // Active button hover background
activeButtonBgRipple: #{{colors.on_primary_container.default.hex_stripped}}; // Active button ripple
activeButtonFg: #{{colors.on_primary.default.hex_stripped}}; // Active button text
activeButtonFgOver: #{{colors.on_primary_container.default.hex_stripped}}; // Active button hover text
activeButtonSecondaryFg: #{{colors.on_primary.default.hex_stripped}}; // Active button secondary text
activeButtonSecondaryFgOver: #{{colors.on_primary_container.default.hex_stripped}}; // Active button secondary hover text

lightButtonBg: #{{colors.surface.default.hex_stripped}}; // Light button background
lightButtonBgOver: #{{colors.surface_variant.default.hex_stripped}}; // Light button hover background
lightButtonBgRipple: #{{colors.primary.default.hex_stripped}}; // Light button ripple
lightButtonFg: #{{colors.on_surface.default.hex_stripped}}; // Light button text
lightButtonFgOver: #{{colors.on_surface_variant.default.hex_stripped}}; // Light button hover text

attentionButtonFg: #{{colors.error.default.hex_stripped}};
attentionButtonFgOver: #{{colors.error.default.hex_stripped}};
attentionButtonBgOver: #{{colors.error_container.default.hex_stripped}};
attentionButtonBgRipple: #{{colors.on_error_container.default.hex_stripped}};

outlineButtonBg: #{{colors.surface.default.hex_stripped}}; // Outline button background
outlineButtonBgOver: #{{colors.surface_variant.default.hex_stripped}}; // Outline button hover background
outlineButtonOutlineFg: #{{colors.primary.default.hex_stripped}}; // Outline button color
outlineButtonBgRipple: #{{colors.primary.default.hex_stripped}}; // Outline button ripple

menuBg: #{{colors.surface.default.hex_stripped}};
menuBgOver: #{{colors.surface_variant.default.hex_stripped}};
menuBgRipple: #{{colors.primary.default.hex_stripped}};
menuIconFg: #{{colors.on_surface.default.hex_stripped}};
menuIconFgOver: #{{colors.on_surface_variant.default.hex_stripped}};
menuSubmenuArrowFg: #{{colors.outline.default.hex_stripped}};
menuFgDisabled: #{{colors.outline.default.hex_stripped}};
menuSeparatorFg: #{{colors.outline.default.hex_stripped}};

scrollBarBg: #{{colors.primary.default.hex_stripped}}40; // Scroll bar background (40% opacity)
scrollBarBgOver: #{{colors.primary.default.hex_stripped}}60; // Scroll bar hover background (60% opacity)
scrollBg: #{{colors.surface_variant.default.hex_stripped}}40; // Scroll bar track (40% opacity)
scrollBgOver: #{{colors.surface_variant.default.hex_stripped}}60; // Scroll bar track on hover (60% opacity)

smallCloseIconFg: #{{colors.outline.default.hex_stripped}};
smallCloseIconFgOver: #{{colors.on_surface_variant.default.hex_stripped}};

radialFg: #{{colors.primary.default.hex_stripped}};
radialBg: #{{colors.surface.default.hex_stripped}};

placeholderFg: #{{colors.outline.default.hex_stripped}}; // Placeholder text
placeholderFgActive: #{{colors.primary.default.hex_stripped}}; // Active placeholder text
inputBorderFg: #{{colors.outline.default.hex_stripped}}; // Input border
filterInputBorderFg: #{{colors.outline.default.hex_stripped}}; // Search input border
filterInputInactiveBg: #{{colors.surface.default.hex_stripped}}; // Inactive search input background
checkboxFg: #{{colors.primary.default.hex_stripped}}; // Checkbox color

titleBg: #{{colors.surface.default.hex_stripped}}; // Window title background
titleShadow: #{{colors.shadow.default.hex_stripped}};
titleButtonFg: #{{colors.on_surface.default.hex_stripped}}; // Title button color
titleButtonBgOver: #{{colors.surface_variant.default.hex_stripped}}; // Title button hover background
titleButtonFgOver: #{{colors.on_surface_variant.default.hex_stripped}}; // Title button hover color
titleButtonCloseBgOver: #{{colors.error.default.hex_stripped}};
titleButtonCloseFgOver: #{{colors.on_error.default.hex_stripped}};
titleFgActive: #{{colors.on_surface.default.hex_stripped}}; // Active title text
titleFg: #{{colors.on_surface.default.hex_stripped}}; // Inactive title text

trayCounterBg: #{{colors.error.default.hex_stripped}}; // Tray counter background
trayCounterBgMute: #{{colors.outline.default.hex_stripped}}; // Muted tray counter background
trayCounterFg: #{{colors.on_error.default.hex_stripped}}; // Tray counter text
trayCounterBgMacInvert: #{{colors.error.default.hex_stripped}}; // Mac tray counter
trayCounterFgMacInvert: #{{colors.on_error.default.hex_stripped}}; // Mac tray counter text

layerBg: #{{colors.surface.default.hex_stripped}}99; // Layer background (60% opacity)

cancelIconFg: #{{colors.error.default.hex_stripped}}; // Cancel icon
cancelIconFgOver: #{{colors.error.default.hex_stripped}}; // Cancel icon on hover

boxBg: #{{colors.surface.default.hex_stripped}}; // Box background
boxTextFg: #{{colors.on_surface.default.hex_stripped}}; // Box text
boxTextFgGood: #{{colors.primary.default.hex_stripped}}; // Box good text
boxTextFgError: #{{colors.error.default.hex_stripped}}; // Box error text
boxTitleFg: #{{colors.on_surface.default.hex_stripped}}; // Box title text
boxSearchBg: #{{colors.surface.default.hex_stripped}}; // Box search field background
boxSearchCancelIconFg: #{{colors.error.default.hex_stripped}}; // Box search cancel icon
boxSearchCancelIconFgOver: #{{colors.error.default.hex_stripped}}; // Box search cancel icon on hover

contactsBg: #{{colors.surface.default.hex_stripped}}; // Contacts background
contactsBgOver: #{{colors.surface_variant.default.hex_stripped}}; // Contacts background on hover
contactsNameFg: #{{colors.on_surface.default.hex_stripped}}; // Contact name
contactsStatusFg: #{{colors.outline.default.hex_stripped}}; // Contact status
contactsStatusFgOver: #{{colors.on_surface_variant.default.hex_stripped}}; // Contact status on hover
contactsStatusFgOnline: #{{colors.primary.default.hex_stripped}}; // Online contact status

photoCropFadeBg: #{{colors.surface.default.hex_stripped}}cc; // Photo crop fade background
photoCropPointFg: #{{colors.primary.default.hex_stripped}}; // Photo crop points
