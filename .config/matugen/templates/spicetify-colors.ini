[Base]
main_fg                               = ${on_surface}
secondary_fg                          = ${on_surface_variant}
main_bg                              = ${surface}
sidebar_and_player_bg                = ${surface_container}
cover_overlay_and_shadow             = 000000
indicator_fg_and_button_bg           = ${primary}
pressing_fg                          = ${on_primary}
slider_bg                            = ${surface_variant}
sidebar_indicator_and_hover_button_bg = ${primary}
scrollbar_fg_and_selected_row_bg     = ${surface_variant}
pressing_button_fg                   = ${on_primary}
pressing_button_bg                   = ${primary}
selected_button                      = ${primary}
miscellaneous_bg                     = ${surface_container}
miscellaneous_hover_bg               = ${surface_container_high}
preserve_1                           = FFFFFF

[Dark]
main_fg                               = ${on_surface}
secondary_fg                          = ${on_surface_variant}
main_bg                              = ${surface}
sidebar_and_player_bg                = ${surface_container}
cover_overlay_and_shadow             = 000000
indicator_fg_and_button_bg           = ${primary}
pressing_fg                          = ${on_primary}
slider_bg                            = ${surface_variant}
sidebar_indicator_and_hover_button_bg = ${primary}
scrollbar_fg_and_selected_row_bg     = ${surface_variant}
pressing_button_fg                   = ${on_primary}
pressing_button_bg                   = ${primary}
selected_button                      = ${primary}
miscellaneous_bg                     = ${surface_container}
miscellaneous_hover_bg               = ${surface_container_high}
preserve_1                           = FFFFFF
