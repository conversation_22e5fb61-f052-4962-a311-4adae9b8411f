* {
    primary: {{colors.primary.default.hex}};
    primary-fixed: {{colors.primary_fixed.default.hex}};
    primary-fixed-dim: {{colors.primary_fixed_dim.default.hex}};
    on-primary: {{colors.on_primary.default.hex}};
    on-primary-fixed: {{colors.on_primary_fixed.default.hex}};
    on-primary-fixed-variant: {{colors.on_primary_fixed_variant.default.hex}};
    primary-container: {{colors.primary_container.default.hex}};
    on-primary-container: {{colors.on_primary_container.default.hex}};
    secondary: {{colors.secondary.default.hex}};
    secondary-fixed: {{colors.secondary_fixed.default.hex}};
    secondary-fixed-dim: {{colors.secondary_fixed_dim.default.hex}};
    on-secondary: {{colors.on_secondary.default.hex}};
    on-secondary-fixed: {{colors.on_secondary_fixed.default.hex}};
    on-secondary-fixed-variant: {{colors.on_secondary_fixed_variant.default.hex}};
    secondary-container: {{colors.secondary_container.default.hex}};
    on-secondary-container: {{colors.on_secondary_container.default.hex}};
    tertiary: {{colors.tertiary.default.hex}};
    tertiary-fixed: {{colors.tertiary_fixed.default.hex}};
    tertiary-fixed-dim: {{colors.tertiary_fixed_dim.default.hex}};
    on-tertiary: {{colors.on_tertiary.default.hex}};
    on-tertiary-fixed: {{colors.on_tertiary_fixed.default.hex}};
    on-tertiary-fixed-variant: {{colors.on_tertiary_fixed_variant.default.hex}};
    tertiary-container: {{colors.tertiary_container.default.hex}};
    on-tertiary-container: {{colors.on_tertiary_container.default.hex}};
    error: {{colors.error.default.hex}};
    on-error: {{colors.on_error.default.hex}};
    error-container: {{colors.error_container.default.hex}};
    on-error-container: {{colors.on_error_container.default.hex}};
    surface: {{colors.surface.default.hex}};
    on-surface: {{colors.on_surface.default.hex}};
    on-surface-variant: {{colors.on_surface_variant.default.hex}};
    outline: {{colors.outline.default.hex}};
    outline-variant: {{colors.outline_variant.default.hex}};
    shadow: {{colors.shadow.default.hex}};
    scrim: {{colors.scrim.default.hex}};
    inverse-surface: {{colors.inverse_surface.default.hex}};
    inverse-on-surface: {{colors.inverse_on_surface.default.hex}};
    inverse-primary: {{colors.inverse_primary.default.hex}};
    surface-dim: {{colors.surface_dim.default.hex}};
    surface-bright: {{colors.surface_bright.default.hex}};
    surface-container-lowest: {{colors.surface_container_lowest.default.hex}};
    surface-container-low: {{colors.surface_container_low.default.hex}};
    surface-container: {{colors.surface_container.default.hex}};
    surface-container-high: {{colors.surface_container_high.default.hex}};
    surface-container-highest: {{colors.surface_container_highest.default.hex}};
}