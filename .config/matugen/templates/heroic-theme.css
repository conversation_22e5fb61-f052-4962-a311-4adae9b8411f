/* Material You theme for Heroic */
/* Generated by matugen */

body {
  /* Base colors */
  --accent: {{colors.primary.default.hex}};
  --accent-overlay: {{colors.primary_container.default.hex}};
  --navbar-accent: {{colors.secondary.default.hex}};
  
  /* Surface colors - Using Material elevation tokens */
  --background: {{colors.surface.default.hex}};
  --body-background: {{colors.surface_container_low.default.hex}};
  --background-darker: {{colors.surface_container.default.hex}};
  --current-background: {{colors.surface_container.default.hex}};
  --navbar-background: {{colors.surface_container.default.hex}};
  --navbar-active-background: {{colors.surface_container_high.default.hex}};
  
  /* Interactive elements */
  --input-background: {{colors.surface_variant.default.hex}};
  --modal-background: {{colors.surface_container_high.default.hex}};
  --modal-border: {{colors.outline.default.hex}};
  
  /* State colors */
  --success: {{colors.tertiary.default.hex}};
  --success-hover: {{colors.tertiary_container.default.hex}};
  --primary: {{colors.primary.default.hex}};
  --primary-hover: {{colors.primary_container.default.hex}};
  --danger: {{colors.error.default.hex}};
  --danger-hover: {{colors.error_container.default.hex}};
  
  /* Status indicators */
  --anticheat-denied: {{colors.error.default.hex}};
  --anticheat-broken: {{colors.error_container.default.hex}};
  --anticheat-running: {{colors.primary.default.hex}};
  --anticheat-supported: {{colors.tertiary.default.hex}};
  --anticheat-planned: {{colors.secondary.default.hex}};
  
  /* Typography and icons */
  --text-title: {{colors.on_surface.default.hex}};
  --icons-background: {{colors.on_surface_variant.default.hex}};
  --action-icon: {{colors.on_surface.default.hex}};
  --action-icon-hover: {{colors.primary.default.hex}};
  --action-icon-active: {{colors.primary_container.default.hex}};
  --icon-disabled: {{colors.on_surface.default.hex}}38;
}
