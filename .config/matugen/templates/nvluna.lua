---@type Base46Table
-- NvLuna Theme for NvChad
-- A Material You theme that adapts to your system colors
-- Generated by Matugen

local M = {}

M.base_30 = {
  -- ألوان أساسية بتباين عالي جدًا
  white = "#{{colors.on_background.default.hex_stripped}}", -- أبيض نقي للنص
  black = "#{{colors.background.default.hex_stripped}}", -- أسود نقي للخلفية
  darker_black = "#{{colors.surface_container_lowest.default.hex_stripped}}", -- أسود أغمق للتباين
  black2 = "#{{colors.surface_container_low.default.hex_stripped}}", -- أسود أفتح قليلاً

  -- خلفيات متدرجة بتباين واضح
  one_bg = "#{{colors.surface_container.default.hex_stripped}}", -- خلفية العناصر
  one_bg2 = "#{{colors.surface_container_high.default.hex_stripped}}", -- خلفية العناصر المرتفعة
  one_bg3 = "#{{colors.surface_container_highest.default.hex_stripped}}", -- خلفية العناصر العالية
  lightbg = "#{{colors.surface_variant.default.hex_stripped}}", -- خلفية خفيفة
  statusline_bg = "#{{colors.surface_dim.default.hex_stripped}}", -- خلفية شريط الحالة
  line = "#{{colors.outline_variant.default.hex_stripped}}", -- لون الخطوط والحدود

  -- ألوان النص بتباين عالي
  light_grey = "#{{colors.on_surface.default.hex_stripped}}", -- نص فاتح
  grey_fg2 = "#{{colors.on_surface_variant.default.hex_stripped}}", -- نص متوسط
  grey_fg = "#{{colors.on_surface_variant.default.hex_stripped}}", -- نص رمادي
  grey = "#{{colors.outline.default.hex_stripped}}", -- نص رمادي غامق

  -- ألوان أساسية مشرقة
  blue = "#{{colors.primary.default.hex_stripped}}", -- اللون الأساسي
  red = "#{{colors.error.default.hex_stripped}}", -- لون الخطأ
  green = "#{{colors.secondary.default.hex_stripped}}", -- اللون الثانوي
  yellow = "#{{colors.tertiary.default.hex_stripped}}", -- اللون الثلاثي
  orange = "#{{colors.tertiary_container.default.hex_stripped}}", -- حاوية اللون الثلاثي
  baby_pink = "#{{colors.error_container.default.hex_stripped}}", -- حاوية لون الخطأ

  -- ألوان إضافية مشرقة
  nord_blue = "#{{colors.primary_container.default.hex_stripped}}", -- حاوية اللون الأساسي
  dark_purple = "#{{colors.secondary_container.default.hex_stripped}}", -- حاوية اللون الثانوي
  teal = "#{{colors.inverse_primary.default.hex_stripped}}", -- اللون الأساسي المعكوس
  purple = "#{{colors.inverse_surface.default.hex_stripped}}", -- لون السطح المعكوس
  pink = "#{{colors.primary.default.hex_stripped}}", -- نسخة من اللون الأساسي للوردي

  -- ألوان عناصر الواجهة
  pmenu_bg = "#{{colors.primary.default.hex_stripped}}", -- خلفية القائمة
  folder_bg = "#{{colors.primary.default.hex_stripped}}", -- لون المجلدات

  -- ألوان مميزة إضافية
  cyan = "#{{colors.tertiary_container.default.hex_stripped}}", -- لون مميز إضافي
  sun = "#{{colors.tertiary.default.hex_stripped}}", -- لون مشرق
  vibrant_green = "#{{colors.secondary.default.hex_stripped}}", -- أخضر مشرق
  seablue = "#{{colors.primary_container.default.hex_stripped}}", -- أزرق بحري
}

-- تمييز نحوي بتباين عالي - Syntax highlighting with high contrast
-- استنادًا إلى https://github.com/chriskempson/base16/blob/master/styling.md
M.base_16 = {
  -- ألوان الخلفية بتباين عالي
  base00 = "#{{colors.background.default.hex_stripped}}", -- الخلفية الافتراضية
  base01 = "#{{colors.surface_container_low.default.hex_stripped}}", -- خلفية أفتح (أشرطة الحالة، أرقام الأسطر)
  base02 = "#{{colors.surface_container.default.hex_stripped}}", -- خلفية التحديد

  -- ألوان النص بتباين عالي
  base03 = "#{{colors.outline.default.hex_stripped}}", -- التعليقات، العناصر غير المرئية
  base04 = "#{{colors.on_surface_variant.default.hex_stripped}}", -- نص أمامي داكن (أشرطة الحالة)
  base05 = "#{{colors.on_background.default.hex_stripped}}", -- النص الأمامي الافتراضي، المؤشر، المحددات
  base06 = "#{{colors.on_surface.default.hex_stripped}}", -- نص أمامي فاتح
  base07 = "#{{colors.on_surface.default.hex_stripped}}", -- خلفية فاتحة (نادرة الاستخدام)

  -- ألوان التمييز النحوي - مشرقة ومتميزة
  base08 = "#{{colors.error.default.hex_stripped}}", -- المتغيرات، علامات XML، نص الروابط
  base09 = "#{{colors.tertiary.default.hex_stripped}}", -- الأعداد، القيم المنطقية، الثوابت
  base0A = "#{{colors.tertiary_container.default.hex_stripped}}", -- الفئات، النص الغامق، خلفية نص البحث
  base0B = "#{{colors.secondary.default.hex_stripped}}", -- السلاسل النصية، الفئات الموروثة، الشفرة
  base0C = "#{{colors.secondary_container.default.hex_stripped}}", -- الدعم، التعبيرات العادية، أحرف الهروب
  base0D = "#{{colors.primary.default.hex_stripped}}", -- الوظائف، الطرق، معرفات السمات، العناوين
  base0E = "#{{colors.primary_container.default.hex_stripped}}", -- الكلمات المفتاحية، التخزين، المحدد
  base0F = "#{{colors.error_container.default.hex_stripped}}", -- العناصر المهملة، علامات اللغة المضمنة
}

-- مجموعات تمييز إضافية لتحسين الواجهة - Additional highlight groups for better UI
M.polish_hl = {
  -- تحسين تمييز العناصر النحوية
  ["@variable"] = {
    fg = M.base_16.base08, -- لون مشرق للمتغيرات
    italic = true, -- جعل المتغيرات مائلة للتمييز
  },

  ["@function"] = {
    fg = M.base_16.base0D, -- لون مميز للدوال
    bold = true, -- جعل الدوال غامقة لرؤية أفضل
  },

  ["@keyword"] = {
    fg = M.base_16.base0E, -- لون مميز للكلمات المفتاحية
    bold = true, -- جعل الكلمات المفتاحية غامقة
  },

  ["@string"] = {
    fg = M.base_16.base0B, -- لون مشرق للنصوص
  },

  ["@number"] = {
    fg = M.base_16.base09, -- لون مميز للأرقام
    bold = true, -- جعل الأرقام غامقة للتمييز
  },

  -- تعليقات بوضوح أفضل
  Comment = {
    fg = M.base_16.base03,
    italic = true, -- جعل التعليقات مائلة للتمييز
  },

  -- خط المؤشر بخلفية واضحة
  CursorLine = {
    bg = M.base_30.black2,
  },

  -- تحسين تمييز الأخطاء
  Error = {
    fg = M.base_16.base08, -- أحمر مشرق للأخطاء
    bold = true, -- جعلها غامقة لرؤية أفضل
    undercurl = true, -- إضافة خط متموج تحت الأخطاء
  },

  -- تحسين عناصر الواجهة
  NvDashAscii = {
    fg = M.base_30.blue, -- لون مميز لشعار لوحة البداية
    bg = "NONE",
  },

  -- شريط الحالة بتباين أفضل
  StatusLine = {
    bg = M.base_30.black2, -- خلفية داكنة لشريط الحالة
    fg = M.base_30.white, -- نص أبيض في شريط الحالة
  },

  -- التبويب النشط بلون مميز
  TabLineSel = {
    bg = M.base_30.blue, -- خلفية مميزة للتبويب النشط
    fg = M.base_30.white, -- نص أبيض في التبويب النشط
    bold = true, -- جعله غامقًا للتمييز
  },

  -- شريط التبويبات بتباين أفضل
  BufferLineFill = {
    bg = M.base_30.black, -- خلفية داكنة لشريط التبويبات
  },

  -- التبويب المحدد بلون مميز
  BufferLineBufferSelected = {
    fg = M.base_30.white, -- نص أبيض في التبويب المحدد
    bg = M.base_30.blue, -- خلفية مميزة للتبويب المحدد
    bold = true, -- جعله غامقًا للتمييز
    italic = true, -- إضافة نمط مائل للتمييز الإضافي
  },

  -- بحث FZF بتباين أفضل للعنصر المحدد
  TelescopeSelection = {
    bg = M.base_30.blue, -- خلفية مميزة للعنصر المحدد
    fg = M.base_30.white, -- نص أبيض للعنصر المحدد
    bold = true, -- جعله غامقًا للتمييز
  },

  TelescopeSelectionCaret = {
    fg = M.base_30.white, -- لون مؤشر التحديد
    bg = M.base_30.blue, -- خلفية مؤشر التحديد
    bold = true, -- جعله غامقًا للتمييز
  },

  TelescopePromptPrefix = {
    fg = M.base_30.blue, -- لون بادئة موجه الإدخال
    bold = true, -- جعله غامقًا للتمييز
  },

  TelescopeBorder = {
    fg = M.base_30.blue, -- لون حدود النافذة
  },

  -- FZF بتباين أفضل
  FzfLuaDefault = {
    fg = M.base_30.white, -- لون النص الافتراضي
  },

  FzfLuaCursor = {
    fg = M.base_30.black, -- لون النص عند المؤشر
    bg = M.base_30.blue, -- خلفية المؤشر
    bold = true, -- جعله غامقًا للتمييز
  },

  FzfLuaSelected = {
    fg = M.base_30.white, -- لون النص المحدد
    bg = M.base_30.blue, -- خلفية العنصر المحدد
    bold = true, -- جعله غامقًا للتمييز
    italic = true, -- إضافة نمط مائل للتمييز الإضافي
  },

  -- مدير الملفات بتمييز أفضل
  NvimTreeCursorLine = {
    bg = M.base_30.blue, -- خلفية السطر المحدد
    fg = M.base_30.white, -- لون النص في السطر المحدد
    bold = true, -- جعله غامقًا للتمييز
  },

  NvimTreeOpenedFile = {
    fg = M.base_30.blue, -- لون الملف المفتوح
    bold = true, -- جعله غامقًا للتمييز
    italic = true, -- إضافة نمط مائل للتمييز الإضافي
  },

  -- تحسين تمييز الكلمات المفتاحية والدوال
  Keyword = {
    fg = M.base_16.base0E,
    bold = true,
    italic = true, -- إضافة نمط مائل للتمييز الإضافي
  },

  Function = {
    fg = M.base_16.base0D,
    bold = true,
  },

  -- تحسين تمييز المتغيرات والثوابت
  Identifier = {
    fg = M.base_16.base08,
    italic = true, -- جعل المتغيرات مائلة للتمييز
  },

  Constant = {
    fg = M.base_16.base09,
    bold = true, -- جعل الثوابت غامقة للتمييز
  },
}

-- Set the theme type (dark or light)
M.type = "dark"

-- This will be used for users to override your theme table from chadrc
M = require("base46").override_theme(M, "nvluna")

return M
