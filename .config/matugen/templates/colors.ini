[<PERSON><PERSON>]
text               = {{colors.on_surface_variant.default.hex_stripped}}
subtext            = {{colors.outline.default.hex_stripped}}
main               = {{colors.surface_dim.default.hex_stripped}}
main-elevated      = {{colors.surface_container_highest.default.hex_stripped}}
main-transition    = {{colors.inverse_surface.default.hex_stripped}}
highlight          = {{colors.tertiary_container.default.hex_stripped}}
highlight-elevated = {{colors.secondary_container.default.hex_stripped}}
sidebar            = {{colors.surface_container_lowest.default.hex_stripped}}
player             = {{colors.surface_container_low.default.hex_stripped}}
card               = {{colors.surface_container_high.default.hex_stripped}}
shadow             = {{colors.scrim.default.hex_stripped}}
selected-row       = {{colors.primary_container.default.hex_stripped}}
button             = {{colors.secondary.default.hex_stripped}}
button-active      = {{colors.secondary_container.default.hex_stripped}}
button-disabled    = {{colors.surface_variant.default.hex_stripped}}
tab-active         = {{colors.inverse_on_surface.default.hex_stripped}}
notification       = {{colors.tertiary.default.hex_stripped}}
notification-error = {{colors.error_container.default.hex_stripped}}
misc               = {{colors.outline_variant.default.hex_stripped}}
play-button        = {{colors.primary.default.hex_stripped}}
play-button-active = {{colors.on_primary_container.default.hex_stripped}}
progress-fg        = {{colors.tertiary.default.hex_stripped}}
progress-bg        = {{colors.tertiary_container.default.hex_stripped}}
heart              = {{colors.error.default.hex_stripped}}
pagelink-active    = {{colors.inverse_primary.default.hex_stripped}}
radio-btn-active   = {{colors.on_tertiary_container.default.hex_stripped}}
