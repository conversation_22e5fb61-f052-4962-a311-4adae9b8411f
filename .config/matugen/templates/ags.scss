// Material You Theme for AGS Shell Lunaris-Project

// Core Colors
$primary: {{colors.primary.default.hex}};
$onPrimary: {{colors.on_primary.default.hex}};
$primaryContainer: {{colors.primary_container.default.hex}};
$onPrimaryContainer: {{colors.on_primary_container.default.hex}};
$secondary: {{colors.secondary.default.hex}};
$onSecondary: {{colors.on_secondary.default.hex}};
$secondaryContainer: {{colors.secondary_container.default.hex}};
$onSecondaryContainer: {{colors.on_secondary_container.default.hex}};
$tertiary: {{colors.tertiary.default.hex}};
$onTertiary: {{colors.on_tertiary.default.hex}};
$tertiaryContainer: {{colors.tertiary_container.default.hex}};
$onTertiaryContainer: {{colors.on_tertiary_container.default.hex}};
$error: {{colors.error.default.hex}};
$onError: {{colors.on_error.default.hex}};
$errorContainer: {{colors.error_container.default.hex}};
$onErrorContainer: {{colors.on_error_container.default.hex}};
$background: {{colors.background.default.hex}};
$onBackground: {{colors.on_background.default.hex}};
$surface: {{colors.surface.default.hex}};
$onSurface: {{colors.on_surface.default.hex}};

// Surface Variants
$surfaceVariant: {{colors.surface_variant.default.hex}};
$onSurfaceVariant: {{colors.on_surface_variant.default.hex}};
$surfaceDim: {{colors.surface_dim.default.hex}};
$surfaceBright: {{colors.surface_bright.default.hex}};
$surfaceContainerLowest: {{colors.surface_container_lowest.default.hex}};
$surfaceContainerLow: {{colors.surface_container_low.default.hex}};
$surfaceContainer: {{colors.surface_container.default.hex}};
$surfaceContainerHigh: {{colors.surface_container_high.default.hex}};
$surfaceContainerHighest: {{colors.surface_container_highest.default.hex}};

// Other Colors
$outline: {{colors.outline.default.hex}};
$outlineVariant: {{colors.outline_variant.default.hex}};
$shadow: {{colors.shadow.default.hex}};
$scrim: {{colors.scrim.default.hex}};
$inverseSurface: {{colors.inverse_surface.default.hex}};
$inverseOnSurface: {{colors.inverse_on_surface.default.hex}};
$inversePrimary: {{colors.inverse_primary.default.hex}};
$surfaceTint: {{colors.surface_tint.default.hex}};

// Fixed Colors
$primaryFixed: {{colors.primary_fixed.default.hex}};
$primaryFixedDim: {{colors.primary_fixed_dim.default.hex}};
$onPrimaryFixed: {{colors.on_primary_fixed.default.hex}};
$onPrimaryFixedVariant: {{colors.on_primary_fixed_variant.default.hex}};
$secondaryFixed: {{colors.secondary_fixed.default.hex}};
$secondaryFixedDim: {{colors.secondary_fixed_dim.default.hex}};
$onSecondaryFixed: {{colors.on_secondary_fixed.default.hex}};
$onSecondaryFixedVariant: {{colors.on_secondary_fixed_variant.default.hex}};
$tertiaryFixed: {{colors.tertiary_fixed.default.hex}};
$tertiaryFixedDim: {{colors.tertiary_fixed_dim.default.hex}};
$onTertiaryFixed: {{colors.on_tertiary_fixed.default.hex}};
$onTertiaryFixedVariant: {{colors.on_tertiary_fixed_variant.default.hex}};

// Optional Colors (Check your generator)
$success: {{colors.primary.default.hex}};
$onSuccess: {{colors.on_primary.default.hex}};
$successContainer: {{colors.primary_container.default.hex}};
$onSuccessContainer: {{colors.on_primary_container.default.hex}};

// Term Colors
$term0: $primary;
$term1: $onPrimary;
$term2: $primaryContainer;
$term3: $onPrimaryContainer;
$term4: $secondary;
$term5: $onSecondary;
$term6: $secondaryContainer;
$term7: $onSecondaryContainer;
$term8: $tertiary;
$term9: $onTertiary;
$term10: $tertiaryContainer;
$term11: $onTertiaryContainer;
$term12: $surface;
$term13: $onSurface;
$term14: $surfaceVariant;
$term15: $onSurfaceVariant;