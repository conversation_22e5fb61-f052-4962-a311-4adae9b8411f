{"name": "HyprLuna", "variables": {"theme_fg_color": "{{colors.on_surface_variant.default.hex}}", "theme_text_color": "{{colors.on_surface.default.hex}}", "theme_bg_color": "{{colors.surface.default.hex}}", "theme_base_color": "{{colors.surface.default.hex}}", "theme_selected_bg_color": "{{colors.primary_container.default.hex}}", "theme_selected_fg_color": "{{colors.on_primary_container.default.hex}}", "insensitive_bg_color": "{{colors.surface.default.hex}}", "insensitive_fg_color": "{{colors.on_surface_variant.default.hex}}", "insensitive_base_color": "{{colors.surface_variant.default.hex}}", "theme_unfocused_fg_color": "{{colors.on_surface_variant.default.hex}}", "theme_unfocused_text_color": "{{colors.on_surface_variant.default.hex}}", "theme_unfocused_bg_color": "{{colors.surface.default.hex}}", "theme_unfocused_base_color": "{{colors.surface.default.hex}}", "theme_unfocused_selected_bg_color": "{{colors.surface_variant.default.hex}}", "theme_unfocused_selected_fg_color": "{{colors.on_surface_variant.default.hex}}", "unfocused_insensitive_color": "{{colors.on_surface_variant.default.hex}}", "borders": "{{colors.outline.default.hex}}", "unfocused_borders": "{{colors.outline.default.hex}}", "warning_color": "{{colors.error.default.hex}}", "error_color": "{{colors.error.default.hex}}", "success_color": "{{colors.primary.default.hex}}", "wm_title": "{{colors.on_primary_container.default.hex}}", "wm_unfocused_title": "{{colors.on_surface_variant.default.hex}}", "wm_highlight": "{{colors.primary_container.default.hex}}", "wm_bg": "{{colors.background.default.hex}}", "wm_unfocused_bg": "{{colors.background.default.hex}}", "wm_button_close_icon": "{{colors.on_secondary_container.default.hex}}", "wm_button_close_hover_bg": "{{colors.secondary_container.default.hex}}", "wm_button_close_active_bg": "{{colors.surface_variant.default.hex}}", "content_view_bg": "{{colors.surface.default.hex}}", "placeholder_text_color": "{{colors.on_surface_variant.default.hex}}", "text_view_bg": "{{colors.surface_container.default.hex}}", "budgie_tasklist_indicator_color": "{{colors.primary.default.hex}}", "budgie_tasklist_indicator_color_active": "{{colors.primary.default.hex}}", "budgie_tasklist_indicator_color_active_window": "{{colors.secondary.default.hex}}", "budgie_tasklist_indicator_color_attention": "{{colors.error.default.hex}}", "accent_bg_color": "{{colors.primary_container.default.hex}}", "accent_fg_color": "{{colors.on_primary_container.default.hex}}", "accent_color": "{{colors.primary.default.hex}}", "destructive_bg_color": "{{colors.error_container.default.hex}}", "destructive_fg_color": "{{colors.on_error_container.default.hex}}", "destructive_color": "{{colors.error.default.hex}}", "success_bg_color": "{{colors.primary_container.default.hex}}", "success_fg_color": "{{colors.primary_container.default.hex}}", "warning_bg_color": "{{colors.error_container.default.hex}}", "warning_fg_color": "{{colors.on_primary_container.default.hex}}", "error_bg_color": "{{colors.error_container.default.hex}}", "error_fg_color": "{{colors.on_error_container.default.hex}}", "window_bg_color": "{{colors.background.default.hex}}", "window_fg_color": "{{colors.on_background.default.hex}}", "view_bg_color": "{{colors.surface.default.hex}}", "view_fg_color": "{{colors.on_surface.default.hex}}", "headerbar_bg_color": "{{colors.surface_container_low.default.hex}}", "headerbar_fg_color": "{{colors.on_surface.default.hex}}", "headerbar_border_color": "{{colors.outline.default.hex}}", "headerbar_backdrop_color": "{{colors.surface_container_low.default.hex}}", "headerbar_shade_color": "rgba(0, 0, 0, 0.09)", "card_bg_color": "{{colors.surface_container.default.hex}}", "card_fg_color": "{{colors.on_surface.default.hex}}", "card_shade_color": "rgba(0, 0, 0, 0.09)", "dialog_bg_color": "{{colors.secondary_container.default.hex}}", "dialog_fg_color": "{{colors.on_secondary_container.default.hex}}", "popover_bg_color": "{{colors.secondary_container.default.hex}}", "popover_fg_color": "{{colors.on_secondary_container.default.hex}}", "thumbnail_bg_color": "{{colors.surface_container.default.hex}}", "thumbnail_fg_color": "{{colors.on_surface.default.hex}}", "shade_color": "rgba(0, 0, 0, 1)", "scrollbar_outline_color": "rgba(0, 0, 0, 1)", "sidebar_bg_color": "{{colors.secondary_container.default.hex}}", "sidebar_fg_color": "{{colors.on_secondary_container.default.hex}}", "sidebar_border_color": "{{colors.background.default.hex}}", "sidebar_backdrop_color": "{{colors.background.default.hex}}"}, "palette": {"blue_": {}, "green_": {}}, "custom_css": {"gtk4": "", "gtk3": ""}, "plugins": {}}