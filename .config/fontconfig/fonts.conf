<?xml version="1.0"?>
<!DOCTYPE fontconfig SYSTEM "fonts.dtd">
<fontconfig>
    <!-- تحديد خطوط النظام الأساسية -->
    <match target="pattern">
        <test qual="any" name="family">
            <string>sans-serif</string>
        </test>
        <edit name="family" mode="prepend">
            <string>SF Pro Display</string> <!-- خط أبل الأساسي -->
            <string>Noto Kufi Arabic</string> <!-- خط كوفي للعربي -->
            <string>Amiri</string> <!-- خط كوفي إضافي لو نوتو مش موجود -->
        </edit>
    </match>

    <!-- ضبط الخطوط للغة العربية -->
    <match>
        <test name="lang" compare="contains">
            <string>ar</string>
        </test>
        <edit name="family" mode="prepend">
            <string>Noto Kufi Arabic</string> <!-- استخدام خط كوفي للعربي -->
            <string>Amiri</string>
        </edit>
    </match>

    <!-- ضبط الخطوط للمونوسبيس (الكود والـ Terminal) -->
    <match target="pattern">
        <test qual="any" name="family">
            <string>monospace</string>
        </test>
        <edit name="family" mode="prepend">
            <string>SF Mono</string> <!-- خط أبل للمبرمجين -->
            <string>Noto Sans Mono Arabic</string>
        </edit>
    </match>
</fontconfig>
