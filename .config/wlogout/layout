{
    "label" : "lock",
    "action" : "loginctl lock-session",
    "text" : "lock",
    "keybind" : "l"
}
{
    "label" : "hibernate",
    "action" : "systemctl hibernate || loginctl hibernate",
    "text" : "save",
    "keybind" : "h"
}
{
    "label" : "logout",
    "action" : "pkill Hyprland || pkill sway || pkill niri || loginctl terminate-user $USER",
    "text" : "logout",
    "keybind" : "e"
}
{
    "label" : "shutdown",
    "action" : "systemctl poweroff || loginctl poweroff",
    "text" : "power_settings_new",
    "keybind" : "s"
}
{
    "label" : "suspend",
    "action" : "systemctl suspend || loginctl suspend",
    "text" : "bedtime",
    "keybind" : "u"
}
{
    "label" : "reboot",
    "action" : "systemctl reboot || loginctl reboot",
    "text" : "restart_alt",
    "keybind" : "r"
}
