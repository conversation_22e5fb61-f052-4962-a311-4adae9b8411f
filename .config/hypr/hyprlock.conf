# For more information visit https://wiki.hyprland.org/Hypr-Ecosystem/hyprlock/

$scrPath      = ~/.config/hypr/hyprlock/
$imgPath      = ~/.config/hypr/hyprlock/
# Put whatever picture you want to use as your profile picture in the hyprlock folder and rename it to profile.png

$USER         = [NAMEHERE] # Your Alias
$host         = uname -n #Your Alias
$wifi-mode    = true # Change the WiFi output. true = show SSID, false = Connected.
$bt-mode      = true # Change the Bluetooth output. true = show Device name, false = Connected.

# Default geolocation provider is IP-API.COM, hardcode it if the location does not match the current location.
# Hardcode your location (If hardcode does not recognize your City, change it to IP Geolocation provider in weatherinfo.sh)
$CITY         = 
$COUNTRY      = 

# WALLPAPER
# Just comment and uncomment to select
$wallpaper    = screenshot
#$wallpaper    = ~/your/wallpaper-path.png
$blur  = 4
$rounding     = 12
$shape-rd     = -1
$shadow-pass  = 2
$shadow-size  = 3
$shadow-color = rgba(22, 18, 22, 1.0)
$shadow-boost = 1.2
$text-shadow-pass   = 1
$text-shadow-boost  = 0.5

# User
$user-bg-color   = rgba(0, 47, 29, 1.0)
$user-text    = GeistMono Nerd Font Bold
$user-color   = rgba(42, 113, 82, 1.0)
# Weather
$weather-text    = GeistMono Nerd Font Bold
$weather-color   = rgba(42, 113, 82, 1.0)
# Password 
$weight       = 2
$input-color   = rgba(142, 213, 176, 1.0)
$input-text   = GeistMono Nerd Font Bold
$inner-color  = rgba(22, 18, 22, 0.5)
$border-color = rgba(0, 47, 29, 1.0)
# Clock
$clock-text = PP Neue Machina Ultra-Bold
$clock-color  = rgba(142, 213, 176, 1.0)
$clock-bg     = rgba(255, 255, 255, 0.25)
# Greeting
$greeting-text = GeistMono Nerd Font Bold
$greeting-color  = rgba(42, 113, 82, 1.0)
# Date
$date-text = PP Neue Machina Ultra-Bold
$date-color  = rgba(142, 213, 176, 1.0)
# Shapes 
$shape-text    = GeistMono Nerd Font Bold
$shape-text-color        = rgba(142, 213, 176, 1.0)
$shape-back-color   = rgba(0, 47, 29, 1.0)
# Media
$media-bg   = rgba(0, 0, 0, 0.25)
$title-text    = GeistMono Nerd Font Bold
$title-color  = rgba(142, 213, 176, 1.0)
$artist-text    = GeistMono Nerd Font Bold
$artist-color  = rgba(42, 113, 82, 1.0)
$album-text   = GeistMono Nerd Font
$album-color  = rgba(42, 113, 82, 1.0)
$status-symbol  = JetBrainsMono Nerd Font Mono
$status-color  = rgba(42, 113, 82, 1.0)
$status-text    = GeistMono Nerd Font Bold

# GENERAL
general {
    no_fade_in          = false
    grace               = 1
    disable_loading_bar = false
    hide_cursor         = true
    ignore_empty_input  = true
    text_trim           = true
}

#BACKGROUND
background {
    monitor     = 
    path        = $wallpaper

    blur_passes         = $blur
    contrast            = 0.8916
    brightness          = 0.7172
    vibrancy            = 0.1696
    vibrancy_darkness   = 0
}

# PROFILE PICTURE
image {
    monitor     =
    path        = $imgPath/profile.png 
    size        = 120 # lesser side if not 1:1 ratio
    opacity     = 0.25

    shadow_pass         = $shadow-pass
    shadow_size         = $shadow-size
    shadow_color        = $shadow-color
    shadow_boost        = $shadow-boost

    rounding            = $rounding # negative values mean circle
    border_size         = 0
    border_color        = $user-bg-color
    rotate              = 0 # degrees, counter-clockwise
    
    position            = 20, 20
    halign              = left
    valign              = bottom
    zindex              = 1
}

# USER INFO
label {
    monitor     =
    text        = cmd[update:1000] echo -e "$(whoami)\n󰁥  $($host)\n\n$(uname -r)\nPackages: $(pacman -Q | wc -l) pacman"
    
    shadow_passes       = 1
    shadow_boost        = 0.5
    
    color               = $user-color
    font_size           = 11
    font_family         = $user-text

    position            = 170, 30
    halign              = left
    valign              = bottom
}

# WEATHERCAST & LOCATION
label {
    monitor     =
    text        = cmd[update:1000] echo "$(bash $scrPath/weatherinfo.sh)"
    
    shadow_passes       = 1
    shadow_boost        = 0.5
    
    color               = $weather-color
    font_size           = 11
    font_family         = $weather-text

    position            = 0, -20
    halign              = center
    valign              = top
}

# INPUT FIELD
input-field {
    monitor     =
    size        = 275, 55
    rounding    = $rounding
    
    outline_thickness   = $weight
    outer_color         = $border-color
    dots_size           = 0.1 # Scale of input-field height, 0.2 - 0.8
    dots_spacing        = 1 # Scale of dots' absolute size, 0.0 - 1.0
    dots_center         = true
    
    inner_color         = $inner-color
    font_color          = $input-color
    fade_on_empty       = false
    
    font_family         = $input-text
    placeholder_text    = <span>󰢏 $USER</span>
    hide_input          = false
    
    position            = 0, -240
    halign              = center
    valign              = center
    zindex              = 10
}

# TIME HR
label {
    monitor     =
    text        = cmd[update:1000] echo -e "$(date +"%H")" # 24-Hour Format
    #text        = cmd[update:1000] echo -e "$(date +"%I")" # 12-Hour Format
    color       = $clock-color
    
    shadow_pass         = $shadow-pass
    shadow_size         = $shadow-size
    shadow_color        = $shadow-color
    shadow_boost        = $shadow-boost
    
    font_size           = 150
    font_family         = $clock-text 
    
    position            = 0, -175
    halign              = center
    valign              = top
}

# TIME MM
label {
    monitor     =
    text        = cmd[update:1000] echo -e "$(date +"%M")"
    color       = $clock-color
    
    shadow_pass         = $shadow-pass
    shadow_size         = $shadow-size
    shadow_color        = $shadow-color
    shadow_boost        = $shadow-boost

    font_size           = 150
    font_family         = $clock-text
    
    position            = 0, -345
    halign              = center
    valign              = top
}

# AM/PM for 12-Hour Format
#label {
    monitor     =
    text = cmd[update:1000] echo -e "$(date +"%p")"
    color       = $clock-color
    
    shadow_pass         = $shadow-pass
    shadow_size         = $shadow-size
    shadow_color        = $shadow-color
    shadow_boost        = $shadow-boost

    font_size           = 16
    font_family         = $clock-text
    
    position            = 0, 42
    halign              = center
    valign              = center
    zindex		= 5
}

# AM/PM BG
#shape {
    monitor     =
    size        = 70, 40
    
    shadow_passes       = $text-shadow-pass
    shadow_boost        = $text-shadow-boost

    color               = $clock-bg
    rounding            = $rounding
    border_size         = 
    border_color        =

    position            = 0, 45
    halign              = center
    valign              = center
    zindex              = 1
}

# GREETING
label {
    monitor     =
    text        = cmd[update:1000] echo "$(bash $scrPath/greeting.sh)"
    
    shadow_passes       = $text-shadow-pass
    shadow_boost        = $text-shadow-boost
    
    color               = $greeting-color
    font_size           = 11
    font_family         = $greeting-text

    position            = 0, -40
    halign              = center
    valign              = center
}

# TODAY IS
label {
    monitor     =
    text        = cmd[update:1000] bash -c 'day=$(date +%A); echo "Today is $day"'

    shadow_passes       = $text-shadow-pass
    shadow_boost        = $text-shadow-boost

    color               = $date-color
    font_size           = 11
    font_family         = $date-text

    position            = 0, -90
    halign              = center
    valign              = center
}

# DATE
label {
    monitor     =
    text        = cmd[update:1000] bash -c 'day=$(date +%d); case "$day" in 1) suffix="st";; 2) suffix="nd";; 3) suffix="rd";; *) suffix="th";; esac; echo -e "$(date +"%B %e")'$day'$suffix, $(date +%Y)"'

    shadow_passes       = $text-shadow-pass
    shadow_boost        = $text-shadow-boost

    color               = $date-color
    font_size           = 14
    font_family         = $date-text

    position            = 0, -115
    halign              = center
    valign              = center
}

# BATTERY
label {
    monitor     =
    text        = cmd[update:1000] echo -e "$($scrPath/battery.sh)"
    
    color               = $shape-text-color
    font_size           = 12
    font_family         = $shape-text
    
    position            = -37, -29
    halign              = right
    valign              = top
    zindex              = 5
}

# NETWORK
#label {
    monitor     =
    text        = cmd[update:1000] echo -e "$($scrPath/network.sh)"
    
    color               = $shape-text-color
    font_size           = 12
    font_family         = $shape-text
    
    position            = 37, 29
    halign              = left
    valign              = bottom
    zindex              = 5
}

# BLUETOOTH
#label {
    monitor     = 
    text        = cmd[update:1000] echo -e "$(~/.config/hypr/hyprlock/bluetooth.sh)"
    
    color               = $shape-text-color
    font_size           = 12
    font_family         = $shape-text
    
    position            = 37, 85
    halign 		= left
    valign 		= bottom
    zindex 		= 5
}

# BATTERY BG
shape {
    monitor     =
    size        = 90, 40
    
    shadow_passes       = $text-shadow-pass
    shadow_boost        = $text-shadow-boost

    color               = $shape-back-color
    rounding            = $shape-rd
    border_size         = 
    border_color        =

    position            = -20, -20
    halign              = right
    valign              = top
    zindex              = 1
}

# NETWORK BG
#shape {
    monitor     =
    size        = 150, 40

    shadow_passes       = $text-shadow-pass
    shadow_boost        = $text-shadow-boost

    color               = $shape-back-color
    rounding            = $shape-rd
    border_size         = 
    border_color        =

    position            = 20, 20
    halign              = left
    valign              = bottom
    zindex              = 1
}

# BLUETOOTH BG
#shape {
    monitor     =
    size        = 150, 40

    shadow_passes       = $text-shadow-pass
    shadow_boost        = $text-shadow-boost

    color               = $shape-back-color
    rounding            = $shape-rd
    border_size         = 
    border_color        =

    position            = 20, 75
    halign              = left
    valign              = bottom
    zindex              = 1
}

# MEDIA BG
#image {
    monitor     =
    path        = $imgPath/media-bg-dark-25.png 
    size        = 120 # lesser side if not 1:1 ratio
    opacity     = 0.25
    
    rounding            = 5 # negative values mean circle
    border_size         = 0
    rotate              = 0 # degrees, counter-clockwise
    
    position            = 0, -410
    halign              = center
    valign              = center
    zindex              = 1
}

shape {
    monitor     =
    size        = 550, 120
    
    shadow_passes       = $text-shadow-pass
    shadow_boost        = $text-shadow-boost

    color               = $media-bg
    rounding            = $rounding
    border_size         = 
    border_color        =

    position            = 0, 70
    halign              = center
    valign              = bottom
    zindex              = 1
}


# PLAYER TITTLE
label {
    monitor     =
    text        = cmd[update:1000] echo "$($scrPath/playerctl.sh --title)"
    
    color               = $title-color
    font_size           = 14
    font_family         = $title-text
    
    position            = 0, -460
    halign              = center
    valign              = center
    zindex              = 5
}

# PLAYER ARTIST
label {
    monitor     =
    text        = cmd[update:1000] echo "$($scrPath/playerctl.sh --artist)"
    
    color               = $artist-color
    font_size           = 11
    font_family         = $artist-text
    
    position            = 0, -480
    halign              = center
    valign              = center
    zindex              = 5
}

# PLAYER ALBUM
label {
    monitor     =
    text        = cmd[update:1000] echo "$($scrPath/playerctl.sh --album)"
    
    color               = $album-color
    font_size           = 11 
    font_family         = $album-text    

    position            = 0, -510
    halign              = center
    valign              = center
    zindex              = 5
}

# PLAYER STATUS SYMBOL
label {
    monitor     =
    text        = cmd[update:1000] echo "$($scrPath/playerctl.sh --status-symbol)"
    
    color               = $status-color
    font_size           = 16
    font_family         = $status-symbol
    
    position            = 700, -430
    halign              = left
    valign              = center
    zindex              = 5
}

# PLAYER STATUS
label {
    monitor     =
    text        = cmd[update:1000] echo "$($scrPath/playerctl.sh --status)"
    
    color               = $status-color
    font_size           = 10
    font_family         = $status-text 
    
    position            = 720, -430
    halign              = left
    valign              = center
    zindex              = 5
}

# PLAYER SOURCE SYMBOL
label {
    monitor     =
    text        = cmd[update:1000] echo "$($scrPath/playerctl.sh --source-symbol)"
    
    color               = $status-color
    font_size           = 16
    font_family         = $status-symbol
    
    position            = -700, -430
    halign              = right
    valign              = center
    zindex              = 5
}

# PLAYER SOURCE
label {
    monitor     =
    text        = cmd[update:1000] echo "$($scrPath/playerctl.sh --source)"
    
    color               = $status-color
    font_size           = 10
    font_family         = $status-text 
    
    position            = -720, -430
    halign              = right
    valign              = center
    zindex              = 5
}

label {
    monitor     =
    text        = 

    color               = $status-color
    font_size           = 24
    font_family         = $status-symbol

    position            = 0, 15
    halign              = center
    valign              = bottom
}


