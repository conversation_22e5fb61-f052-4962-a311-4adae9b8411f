#Lines ending with `# [hidden]` won't be shown on cheatsheet
# Lines starting with #! are section headings

bindl = ,XF86AudioMute, exec, wpctl set-mute @DEFAULT_SOURCE@ toggle # [hidden]
bindl = Super ,XF86AudioMute, exec, wpctl set-mute @DEFAULT_SOURCE@ toggle # [hidden]
bindl = ,XF86AudioMute, exec, wpctl set-volume @DEFAULT_AUDIO_SINK@ 0% # [hidden]
bindl = Super+Shift,M, exec, wpctl set-volume @DEFAULT_AUDIO_SINK@ 0% # [hidden]
bindl = Super+CTRL,P, exec,sh ~/.config/ags/scripts/scale.sh 0.05 # [hidden]
bindl = Super+CTRL,O, exec,sh ~/.config/ags/scripts/scale.sh -0.05 # [hidden]
bindle=, XF86AudioRaiseVolume, exec, wpctl set-volume -l 1 @DEFAULT_AUDIO_SINK@ 5%+ # [hidden]
bindle=, XF86AudioLowerVolume, exec, wpctl set-volume @DEFAULT_AUDIO_SINK@ 5%- # [hidden]
bindle= Shift,F12, exec,hyprshade toggle  blue-light-filter
# Uncomment these if you can't get agsv1 to work
#bindle=, XF86MonBrightnessUp, exec, brightnessctl set '12.75+'
#bindle=, XF86MonBrightnessDown, exec, brightnessctl set '12.75-'

#!
##! Essentials for beginners
bind = , Super, exec, true # Open app launcher
##! Actions
# Screenshot, Record, OCR, Color picker, Clipboard history
bind = Super ,R ,exec,sh ~/.config/ags/scripts/color_generation/wallpapers.sh -r
bind = Ctrl+Shift+Alt, Delete, exec, pkill wlogout || wlogout -p layer-shell # [hidden]
# Color picker
bind = Super+Shift, C, exec, hyprpicker -a # Pick color (Hex) >> clipboard
# screenshot
bindl=,Print,exec,grim - | wl-copy # Screenshot >> clipboard
bindl= Ctrl,Print, exec, mkdir -p ~/Pictures/Screenshots && ~/.config/ags/scripts/grimblast.sh copysave screen ~/Pictures/Screenshots/Screenshot_"$(date '+%Y-%m-%d_%H.%M.%S')".png # Screenshot >> clipboard & file
bind = Super+Shift, S, exec, ~/.config/ags/scripts/grimblast.sh --freeze copy area # Screen snip
bind = Super+Shift+Alt, S, exec, grim -g "$(slurp)" - | swappy -f - # Screen snip >> edit
bind = Super+Shift,T,exec, grim -g "$(slurp $SLURP_ARGS)" "tmp.png" && tesseract -l eng "tmp.png" - | wl-copy && rm "tmp.png" # Screen snip to text >> clipboard
bind = Ctrl+Super+Shift,S,exec,grim -g "$(slurp $SLURP_ARGS)" "tmp.png" && tesseract "tmp.png" - | wl-copy && rm "tmp.png" # [hidden]


# Recording stuff
bind = Super+Alt, R, exec, ~/.config/ags/scripts/record-script.sh # Record region (no sound)
bind = Ctrl+Alt, R, exec, ~/.config/ags/scripts/record-script.sh --fullscreen # [hidden] Record screen (no sound)
bind = Super+Shift+Alt, R, exec, ~/.config/ags/scripts/record-script.sh --fullscreen-sound # Record screen (with sound)
##! Session
bind = Ctrl+Super, L, exec, agsv1 run-js 'lock.lock()' # [hidden]
bind = Super, L, exec, loginctl lock-session # Lock
bind = Super+Shift, L, exec, loginctl lock-session # [hidden]
bindl = Super+Shift, L, exec, sleep 0.1 && systemctl suspend || loginctl suspend # Suspend system
bind = Ctrl+Shift+Alt+Super, Delete, exec, systemctl poweroff || loginctl poweroff # [hidden] Power off

#!
##! Window management
# Focusing
#/# bind = Super, ←/↑/→/↓,, # Move focus in direction
bind = Super, Left, movefocus, l # [hidden]
bind = Super, Right, movefocus, r # [hidden]
bind = Super, Up, movefocus, u # [hidden]
bind = Super, Down, movefocus, d # [hidden]
bind = Super, BracketLeft, movefocus, l # [hidden]
bind = Super, BracketRight, movefocus, r # [hidden]
bindm = Super, mouse:272, movewindow
bindm = Super, mouse:273, resizewindow
bind = Super, Q, killactive,
bind = Super+Shift+Alt, Q, exec, hyprctl kill # Pick and kill a window
##! Window arrangement
#/# bind = Super+Shift, ←/↑/→/↓,, # Window: move in direction
bind = Super+Shift, Left, movewindow, l # [hidden]
bind = Super+Shift, Right, movewindow, r # [hidden]
bind = Super+Shift, Up, movewindow, u # [hidden]
bind = Super+Shift, Down, movewindow, d # [hidden]
# Window split ratio
#/# binde = Super, +/-,, # Window: split ratio +/- 0.1
binde = Super, Minus, splitratio, -0.1 # [hidden]
binde = Super, Equal, splitratio, +0.1 # [hidden]
binde = Super, Semicolon, splitratio, -0.1 # [hidden]
binde = Super, Apostrophe, splitratio, +0.1 # [hidden]
# Positioning mode
bind = Super+Alt, Space, togglefloating,
bind = Super+Alt, F, fullscreenstate, 0 3 # Toggle fake fullscreen
bind = Super, F, fullscreen, 0
bind = Super, D, fullscreen, 1
bind = Super Ctrl, W,exec,hyprctl dispatch workspaceopt allfloat
##! Rofi
bind = Super Shift, E, exec, pkill rofi || rofi -show filebrowser # Mini Filebrowser
bind = Super, V, exec, pkill rofi || cliphist list | rofi -dmenu | cliphist decode | wl-copy && wtype -M ctrl shift -P v -m ctrl # Clipboard history >> clipboard
bind = Super, Period, exec, pkill rofi || rofi -show emoji # Pick emoji >> clipboard
bind = Super, Tab, exec, pkill rofi || rofi -show drun # Rofi Launcher

#!
##! Workspace navigation
# Switching
#/# bind = Super, Hash,, # Focus workspace # (1, 2, 3, 4, ...)
bind = Super, 1, exec, ~/.config/ags/scripts/hyprland/workspace_action.sh workspace 1 # [hidden]
bind = Super, 2, exec, ~/.config/ags/scripts/hyprland/workspace_action.sh workspace 2 # [hidden]
bind = Super, 3, exec, ~/.config/ags/scripts/hyprland/workspace_action.sh workspace 3 # [hidden]
bind = Super, 4, exec, ~/.config/ags/scripts/hyprland/workspace_action.sh workspace 4 # [hidden]
bind = Super, 5, exec, ~/.config/ags/scripts/hyprland/workspace_action.sh workspace 5 # [hidden]
bind = Super, 6, exec, ~/.config/ags/scripts/hyprland/workspace_action.sh workspace 6 # [hidden]
bind = Super, 7, exec, ~/.config/ags/scripts/hyprland/workspace_action.sh workspace 7 # [hidden]
bind = Super, 8, exec, ~/.config/ags/scripts/hyprland/workspace_action.sh workspace 8 # [hidden]
bind = Super, 9, exec, ~/.config/ags/scripts/hyprland/workspace_action.sh workspace 9 # [hidden]
bind = Super, 0, exec, ~/.config/ags/scripts/hyprland/workspace_action.sh workspace 10 # [hidden]

#/# bind = Ctrl+Super, ←/→,, # Workspace: focus left/right
bind = Ctrl+Super, Right, workspace, +1 # [hidden]
bind = Ctrl+Super, Left, workspace, -1 # [hidden]
#/# bind = Super, Scroll ↑/↓,, # Workspace: focus left/right
bind = Super, mouse_up, workspace, +1 # [hidden]
bind = Super, mouse_down, workspace, -1 # [hidden]
bind = Ctrl+Super, mouse_up, workspace, +1 # [hidden]
bind = Ctrl+Super, mouse_down, workspace, -1 # [hidden]
#/# bind = Super, Page_↑/↓,, # Workspace: focus left/right
bind = Super, Page_Down, workspace, +1 # [hidden]
bind = Super, Page_Up, workspace, -1 # [hidden]
bind = Ctrl+Super, Page_Down, workspace, +1 # [hidden]
bind = Ctrl+Super, Page_Up, workspace, -1 # [hidden]
## Special
bind = Super, S, togglespecialworkspace,
bind = Super, mouse:275, togglespecialworkspace,

##! Workspace management
# Move window to workspace Super + Alt + [0-9]
#/# bind = Super+Alt, Hash,, # Window: move to workspace # (1, 2, 3, 4, ...)
bind = Super+Alt, 1, exec, ~/.config/ags/scripts/hyprland/workspace_action.sh movetoworkspacesilent 1 # [hidden]
bind = Super+Alt, 2, exec, ~/.config/ags/scripts/hyprland/workspace_action.sh movetoworkspacesilent 2 # [hidden]
bind = Super+Alt, 3, exec, ~/.config/ags/scripts/hyprland/workspace_action.sh movetoworkspacesilent 3 # [hidden]
bind = Super+Alt, 4, exec, ~/.config/ags/scripts/hyprland/workspace_action.sh movetoworkspacesilent 4 # [hidden]
bind = Super+Alt, 5, exec, ~/.config/ags/scripts/hyprland/workspace_action.sh movetoworkspacesilent 5 # [hidden]
bind = Super+Alt, 6, exec, ~/.config/ags/scripts/hyprland/workspace_action.sh movetoworkspacesilent 6 # [hidden]
bind = Super+Alt, 7, exec, ~/.config/ags/scripts/hyprland/workspace_action.sh movetoworkspacesilent 7 # [hidden]
bind = Super+Alt, 8, exec, ~/.config/ags/scripts/hyprland/workspace_action.sh movetoworkspacesilent 8 # [hidden]
bind = Super+Alt, 9, exec, ~/.config/ags/scripts/hyprland/workspace_action.sh movetoworkspacesilent 9 # [hidden]
bind = Super+Alt, 0, exec, ~/.config/ags/scripts/hyprland/workspace_action.sh movetoworkspacesilent 10 # [hidden]

bind = Ctrl+Super+Shift, Up, movetoworkspacesilent, special # [hidden]

bind = Ctrl+Super+Shift, Right, movetoworkspace, +1 # [hidden]
bind = Ctrl+Super+Shift, Left, movetoworkspace, -1 # [hidden]
bind = Ctrl+Super, BracketLeft, workspace, -1 # [hidden]
bind = Ctrl+Super, BracketRight, workspace, +1 # [hidden]
bind = Ctrl+Super, Up, workspace, -5 # [hidden]
bind = Ctrl+Super, Down, workspace, +5 # [hidden]
#/# bind = Super+Shift, Scroll ↑/↓,, # Window: move to workspace left/right
bind = Super+Shift, mouse_down, movetoworkspace, -1 # [hidden]
bind = Super+Shift, mouse_up, movetoworkspace, +1 # [hidden]
bind = Super+Alt, mouse_down, movetoworkspace, -1 # [hidden]
bind = Super+Alt, mouse_up, movetoworkspace, +1 # [hidden]
#/# bind = Super+Shift, Page_↑/↓,, # Window: move to workspace left/right
bind = Super+Alt, Page_Down, movetoworkspace, +1 # [hidden]
bind = Super+Alt, Page_Up, movetoworkspace, -1 # [hidden]
bind = Super+Shift, Page_Down, movetoworkspace, +1  # [hidden]
bind = Super+Shift, Page_Up, movetoworkspace, -1  # [hidden]
bind = Super+Alt, S, movetoworkspacesilent, special
bind = Super, P, pin

bind = Ctrl+Super, S, togglespecialworkspace, # [hidden]
bind = Alt, Tab, cyclenext # [hidden] sus keybind
bind = Alt, Tab, bringactivetotop, # [hidden] bring it to the top
bind = Super, J,   togglesplit

#!
##! Widgets
bindr = Ctrl+Super, R, exec, killall agsv1 ;agsv1 -c ~/.config/ags/config.js # Restart widgets
bindr = Ctrl+Super+Alt, R, exec, hyprctl reload; killall agsv1 ydotool; agsv1 & # [hidden]
bindir = Super, Super_L, exec,pkill rofi | agsv1 -t 'overview' # Toggle overview/launcher
bind = Super, grave, exec,pkill rofi | agsv1 -t 'glance' # [hidden]
bind = Super Shift, A, exec,pkill rofi | agsv1 -t 'sideright' # Toggle right 
bind = Super Shift, 1, exec, agsv1 run-js 'openColorScheme.value = true; Utils.timeout(1500, () => openColorScheme.value = false);' # View color scheme and options
bind = Super Shift, 2, exec,pkill rofi | agsv1 -t 'recorder' # Toggle Music
bind = Super Shift, 3, exec,pkill rofi | agsv1 -t 'music' # Toggle Music
bind = Super Ctrl, D, exec, for ((i=0; i<$(hyprctl monitors -j | jq length); i++)); do agsv1 -t "desktopbackground""$i"; done
bind = Super, A, exec,pkill rofi | agsv1 -t 'sideleft' # [hidden]
bind = Super, N, exec,pkill rofi | agsv1 -t 'sideright' # Toggle right sidebar
bind = Super, W, exec,pkill rofi | agsv1 -t 'wallselect' # Launch Wallpaper Selector
bind = Super, M, exec,pkill rofi | agsv1 -t 'music' # Launch Music Widget
bind = Super, Comma, exec, agsv1 run-js 'openColorScheme.value = true; Utils.timeout(1500, () => openColorScheme.value = false);' # View color scheme and options
bind = Super, Slash, exec, agsv1 -t 'cheatsheet0'  #for ((i=0; i<$(hyprctl monitors -j | jq length); i++)); do agsv1 -t "cheatsheet""$i"; done
bind = Super+Ctrl+Shift, Delete, exec, for ((i=0; i<$(hyprctl monitors -j | jq length); i++)); do agsv1 -t "session""$i"; done # Toggle power menu
bindle=, XF86MonBrightnessUp, exec, agsv1 run-js 'brightness.screen_value += 0.05; indicator.popup(1);' # [hidden]
bindle=, XF86MonBrightnessDown, exec, agsv1 run-js 'brightness.screen_value -= 0.05; indicator.popup(1);' # [hidden]
bindl  = , XF86AudioMute, exec, agsv1 run-js 'indicator.popup(1);' # [hidden]
bindl  = Super+Shift,M,   exec, agsv1 run-js 'indicator.popup(1);' # [hidden]
bind = Super,Z,exec, agsv1 --run-js "globalThis.handleStyles(true)" # [hidden]
bind = Super Shift,R,exec,agsv1 -t 'recorder'
# Testing
bind = SuperAlt, f12, exec, notify-send "Hyprland version: $(hyprctl version | head -2 | tail -1 | cut -f2 -d ' ')" "owo" -a 'Hyprland keybind'
bind = Super+Alt, f11, exec, notify-send "Millis since epoch" "$(date +%s%N | cut -b1-13)" -a 'Hyprland keybind'
bind = Super+Alt, f10, exec, notify-send 'Test notification' "Here's a really long message to test truncation and wrapping\nYou can middle click or flick this notification to dismiss it!" -a 'Shell' -A "Test1=I got it!" -A "Test2=Another action" -t 5000 # [hidden]
bind = Super+Alt, Equal, exec, notify-send "Urgent notification" "Ah hell no" -u critical -a 'Hyprland keybind' # [hidden]

##! Media
bindl= Super+Shift, N, exec, playerctl next || playerctl position `bc <<< "100 * $(playerctl metadata mpris:length) / 1000000 / 100"` # Next track
bindl= ,XF86AudioNext, exec, playerctl next || playerctl position `bc <<< "100 * $(playerctl metadata mpris:length) / 1000000 / 100"` # [hidden]
bindl= ,XF86AudioPrev, exec, playerctl previous # [hidden]
bind = Super+Shift+Alt, mouse:275, exec, playerctl previous # [hidden]
bind = Super+Shift+Alt, mouse:276, exec, playerctl next || playerctl position `bc <<< "100 * $(playerctl metadata mpris:length) / 1000000 / 100"` # [hidden]
bindl= Super+Shift, B, exec, playerctl previous # Previous track
bindl= Super+Shift, P, exec, playerctl play-pause # Play/pause media
bindl= ,XF86AudioPlay, exec, playerctl play-pause # [hidden]
bindl= ,XF86AudioPause, exec, playerctl play-pause # [hidden]

#!
##! Apps
bind = Super, Return, exec, ghostty # [hidden] # In case you're from i3 or its Wayland clone
bind = Super, B, exec, zen-browser # Toggle left sidebar
bind = Super, T, exec, warp # Launch Terminal
bind = Super Shift, G, exec,github-desktop
bind = Super, O, exec, obsidian
bind = Super, C, exec, cursor
bind = Super + Shift, V, exec, code
bind = Super CTRL, S, exec, spotify-launcher
bind = Super, E, exec, nautilus --new-window #Launch Nautilus (file manager)
bind = Super, I, exec, XDG_CURRENT_DESKTOP="gnome" gnome-control-center # Launch GNOME Settings
bind = Ctrl+Super, V, exec, pavucontrol # Launch pavucontrol (volume mixer)
bind = Ctrl+Shift, Escape, exec, kitty btop # Launch System monitor
##! Bar Mode Switching
# Bar mode control
bind = Super, X, exec, agsv1 --run-js 'const current = parseInt(currentShellMode.value[0]); const next = current >= 0 && current < 8 ? current + 1 : 0; updateMonitorShellMode(currentShellMode, 0, next.toString())' #Cycle Between Horizontal Bars
bind = Super Shift, X, exec, agsv1 --run-js 'const current = parseInt(currentShellMode.value[0]); const next = current >= 9 && current < 10 ? current + 1 : 9; updateMonitorShellMode(currentShellMode, 0, next.toString())' #Cycle Between Vertical Bars
bind = Super Alt, X, exec, agsv1 --run-js 'toggleBarPosition()'


