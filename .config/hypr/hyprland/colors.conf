# exec = export SLURP_ARGS='-d -c FFDAD8BB -b 5D3F3F44 -s 00000000'

general {
    col.active_border =  rgb(FFB3B2) rgb(1A1111)
    col.inactive_border = rgba(1A1111FF)
}

misc {
    background_color = rgba(1A1111FF)
}

plugin {
    hyprbars {
        # Honestly idk if it works like css, but well, why not
        bar_text_font = <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, AR One Sans, Reddit Sans, Inter, Roboto, Ubuntu, Noto Sans, sans-serif
        bar_height = 30
        bar_padding = 10
        bar_button_padding = 5
        bar_precedence_over_border = true
        bar_part_of_window = true

        bar_color = rgba(1A1111FF)
        col.text = rgba(F0DEDDFF)


        # example buttons (R -> L)
        # hyprbars-button = color, size, on-click
        hyprbars-button = rgb(F0DEDD), 13, 󰖭, hyprctl dispatch killactive
        hyprbars-button = rgb(F0DEDD), 13, 󰖯, hyprctl dispatch fullscreen 1
        hyprbars-button = rgb(F0DEDD), 13, 󰖰, hyprctl dispatch movetoworkspacesilent special
    }
}

windowrulev2 = bordercolor rgba(FFB3B2AA) rgba(FFB3B277),pinned:1
