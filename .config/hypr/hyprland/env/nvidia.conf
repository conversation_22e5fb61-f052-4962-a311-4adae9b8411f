### NVIDIA ###
# Environment variables for Hyprland 0.48.1 and NVIDIA drivers (576.xx+)
# Updated: May 10, 2025

# --- Input Method (Fcitx) ---
env = INPUT_METHOD,fcitx                   # Sets Fcitx as the primary input method
env = SDL_IM_MODULE,fcitx                    # Configures Fcitx for SDL applications
env = QT_IM_MODULE,fcitx                     # Configures Fcitx for Qt applications
env = GTK_IM_MODULE,fcitx                    # Configures Fcitx for GTK applications
env = XMODIFIERS,@im=fcitx                   # Configures Fcitx for X11 applications

# --- Wayland/X11 Backend & Theming ---
env = GDK_BACKEND,wayland,x11                # Prioritizes Wayland for GTK, falls back to X11
env = QT_QPA_PLATFORM,wayland;xcb            # Prioritizes Wayland for Qt, falls back to XCB (X11)
env = QT_QPA_PLATFORMTHEME,qt5ct             # Defines the theme to use for Qt5/Qt6 applications
env = QT_WAYLAND_DISABLE_WINDOWDECORATION,1  # Disables <PERSON><PERSON>'s own window decorations on Wayland (Hyprland handles them)
env = QT_AUTO_SCREEN_SCALE_FACTOR,1        # Optional: Enables automatic scaling in Qt
env = QT_SCALE_FACTOR,1                    # Optional: Defines a manual scaling factor for Qt
env = CLUTTER_BACKEND,wayland                # Forces Wayland backend for Clutter (used in some GNOME apps)

# --- Desktop Identification ---
env = XDG_CURRENT_DESKTOP,Hyprland           # Identifies the current desktop environment as Hyprland
env = XDG_SESSION_DESKTOP,Hyprland           # Identifies the desktop session as Hyprland
env = XDG_SESSION_TYPE,wayland               # Identifies the session type as Wayland

# --- Nvidia Specific ---
env = LIBVA_DRIVER_NAME,nvidia               # Uses Nvidia driver for VA-API video acceleration
env = __GLX_VENDOR_LIBRARY_NAME,nvidia       # Ensures Nvidia's GLX implementation is used
env = GBM_BACKEND,nvidia-drm                 # Essential! Uses Nvidia's GBM backend for Wayland rendering
env = ELECTRON_OZONE_PLATFORM_HINT,auto      # Suggests Electron apps use the appropriate backend (ideally Wayland)
env = NVD_BACKEND,direct                     # Enables direct backend for NVIDIA (better performance)

# --- Nvidia Troubleshooting (Optional - Uncomment if needed) ---
env = WLR_NO_HARDWARE_CURSORS,1            # Workaround for cursor glitches (flickering, invisibility)
# env = WLR_DRM_NO_ATOMIC,1                  # Workaround for flickering/modesetting issues (rarely needed with modern drivers)

# --- Variables for Laptops with iGPU+dGPU ---
# Uncomment these lines ONLY if you have a laptop with hybrid graphics
env = __NV_PRIME_RENDER_OFFLOAD,1          # Enables rendering offload to the Nvidia GPU
env = __VK_LAYER_NV_optimus,NVIDIA_only    # Forces Vulkan applications to use the Nvidia GPU

# --- GPU Selection for Hyprland 0.48.1+ ---
# AQ_DRM_DEVICES replaces the older WLR_DRM_DEVICES in Hyprland 0.48.0+
# Format: /dev/dri/card0:/dev/dri/card1 where card0 is typically the integrated GPU and card1 is the NVIDIA GPU
# To identify your GPUs: run `lspci | grep VGA` then `ls -l /dev/dri/by-path/` to match PCI IDs to cardX
env = AQ_DRM_DEVICES,/dev/dri/card0:/dev/dri/card1 # Runs most apps with NVIDIA GPU

