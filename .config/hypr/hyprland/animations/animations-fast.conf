# ----------------------------------------------------- 
# ▄▀█ █▄░█ █ █▀▄▀█ ▄▀█ ▀█▀ █ █▀█ █▄░█
# █▀█ █░▀█ █ █░▀░█ █▀█ ░█░ █ █▄█ █░▀█
#
# name "Fast"
# credit https://github.com/mylinuxforwork/dotfiles
# ----------------------------------------------------- 
animations {
    enabled = true
    bezier = linear, 0, 0, 1, 1
    bezier = liner, 1, 1, 1, 1
    bezier = md3_standard, 0.2, 0, 0, 1
    bezier = md3_decel, 0.05, 0.7, 0.1, 1
    bezier = md3_accel, 0.3, 0, 0.8, 0.15
    bezier = overshot, 0.05, 0.9, 0.1, 1.1
    bezier = crazyshot, 0.1, 1.5, 0.76, 0.92 
    bezier = hyprnostretch, 0.05, 0.9, 0.1, 1.0
    bezier = fluent_decel, 0.1, 1, 0, 1
    bezier = easeInOutCirc, 0.85, 0, 0.15, 1
    bezier = easeOutCirc, 0, 0.55, 0.45, 1
    bezier = easeOutExpo, 0.16, 1, 0.3, 1
    animation = windows, 1, 3, md3_decel, popin 60%
    animation = border, 1, 10, default
    animation = borderangle, 1, 15, liner, loop
    animation = fade, 1, 2.5, md3_decel
    animation = workspaces, 1, 3.5, easeOutExpo, slide
    animation = specialWorkspace, 1, 3, md3_decel, slidevert
}
