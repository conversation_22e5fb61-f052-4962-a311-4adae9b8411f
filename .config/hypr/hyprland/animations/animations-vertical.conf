# ----------------------------------------------------- 
# ▄▀█ █▄░█ █ █▀▄▀█ ▄▀█ ▀█▀ █ █▀█ █▄░█
# █▀█ █░▀█ █ █░▀░█ █▀█ ░█░ █ █▄█ █░▀█
#
# name "Vertical"
# ----------------------------------------------------- 

animations {
    bezier = fluent_decel, 0, 0.2, 0.4, 1
    bezier = easeOutCirc, 0, 0.55, 0.45, 1
    bezier = easeOutCubic, 0.33, 1, 0.68, 1
    bezier = easeinoutsine, 0.37, 0, 0.63, 1
    
    # Windows
    animation = windowsIn, 1, 1.5, easeinoutsine, popin 60% # window open
    animation = windowsOut, 1, 1.5, easeOutCubic, popin 60% # window close.
    animation = windowsMove, 1, 1.5, easeinoutsine, slide # everything in between, moving, dragging, resizing.

    # Fading
    animation = fade, 1, 2.5, fluent_decel

	animation = fadeLayersIn, 0
    animation = border, 0


	# Layers
	animation = layers, 1, 1.5, easeinoutsine, popin
	
    # Workspaces
    #animation = workspaces, 1, 3, fluent_decel, slidefade 30% # styles: slide, slidevert, fade, slidefade, slidefadevert
    animation = workspaces, 1, 3, fluent_decel, slidefadevert 30% # styles: slide, slidevert, fade, slidefade, slidefadevert

	animation = specialWorkspace, 1, 2, fluent_decel, slidefade 10%
}
