exec-once = agsv1 
exec-once = sleep 5 && discord
exec-once = swww-daemon --format xrgb 
exec-once = foot --server 
exec-once = gnome-keyring-daemon --start --components=secrets
exec-once = /usr/lib/polkit-gnome/polkit-gnome-authentication-agent-1 || /usr/libexec/polkit-gnome-authentication-agent-1
exec-once = hypridle
exec-once = dbus-update-activation-environment --all
exec-once = hyprpm reload
# Cursor
exec-once = hyprctl setcursor Bibata-Modern-Ice 24
# Force Geist in Hyprland
exec-once = gsettings set org.gnome.desktop.interface font-name 'Geist'
exec-once = gsettings set org.gnome.desktop.interface document-font-name 'Geist'
exec-once = gsettings set org.gnome.desktop.interface monospace-font-name 'Geist Mono'
exec-once = gsettings set org.gnome.desktop.wm.preferences titlebar-font 'Geist'

# Audio
exec-once = easyeffects --gapplication-service
# Clipboard: history
# exec-once = wl-paste --watch cliphist store &
exec-once = wl-paste --type text --watch cliphist store
exec-once = wl-paste --type image --watch cliphist store

