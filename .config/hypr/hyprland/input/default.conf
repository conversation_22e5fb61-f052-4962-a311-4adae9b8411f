input {
    kb_layout = us,ara
    kb_options = grp:alt_shift_toggle
    numlock_by_default = true
    repeat_delay = 300
    repeat_rate = 35
    touchpad {
        natural_scroll = yes
        disable_while_typing = true
        clickfinger_behavior = true
        scroll_factor = 0.5
    }
    special_fallthrough = true
    follow_mouse = 1
    focus_on_close = 1 # focus will shift to the window under the cursor.
}

binds {
    scroll_event_delay = 0
    }

gestures {
    workspace_swipe = true
    }
