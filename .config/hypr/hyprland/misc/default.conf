misc {
    vfr = true
    vrr = 2
    font_family = Geist
    animate_manual_resizes = true
    animate_mouse_windowdragging = true
    enable_swallow = false
    swallow_regex = (foot|kitty|allacritty|Alacritty|ghostty|Ghostty)
    focus_on_activate = true # Test
    render_ahead_of_time = true
    render_ahead_safezone = 30
    # disable_scale_checks = true
    disable_hyprland_logo = true
    force_default_wallpaper = 0
    # new_window_takes_over_fullscreen = 2
    allow_session_lock_restore = true
    initial_workspace_tracking = true
}


xwayland {
  force_zero_scaling = false
}

render {
    explicit_sync = 1
    explicit_sync_kms = 2
    direct_scanout = 2
}
cursor {
    sync_gsettings_theme = true
    no_hardware_cursors = true
}

