format = '''
$directory$git_branch$rust$python
$character'''

palette = 'colors'

[palettes.colors]
mustard = '#af8700' # example
color1 = '#81d5cc'
color2 = '#003733'
color3 = '#bec9c6'
color4 = '#1a2120'
color5 = '#003733'
color6 = '#0e1514'
color7 = '#0e1514'
color8 = '#81d5cc'
color9 = '#afc9e7'

# Prompt symbols 
[character]
success_symbol = "[🞈](color9 bold)"
error_symbol = "[🞈](@{error})"
vicmd_symbol = "[🞈](#f9e2af)"

[directory]
format = "[](fg:color1 bg:color4)[󰉋](bg:color1 fg:color2)[ ](fg:color1 bg:color4)[$path ](fg:color3 bg:color4)[ ](fg:color4)"

[directory.substitutions]
"Documents" = "󰈙 "
"Downloads" = " "
"Music" = " "
"Pictures" = " "

[git_branch]
format = "[](fg:color8 bg:color4)[ ](bg:color8 fg:color5)[](fg:color8 bg:color4)[(bg:color8 fg:color5) $branch](fg:color3 bg:color4)[](fg:color4) "

[time]
format = "[](fg:color8 bg:color4)[ ](bg:color8 fg:color5)[](fg:color8 bg:color4)[(bg:color8 fg:color5) $time](fg:color3 bg:color4)[](fg:color4) "
disabled = false
time_format = "%R" # Hour:Minute Format

[python]
format = "[](fg:color8 bg:color4)[${symbol}${version}](bg:color8 fg:color5)[](fg:color8 bg:color4)[(bg:color8 fg:color5)( ${virtualenv})](fg:color3 bg:color4)[](fg:color4) "
symbol = '🐍'
# pyenv_version_name = true
pyenv_prefix = 'venv'
